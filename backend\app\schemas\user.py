from typing import Optional
from pydantic import BaseModel, EmailStr, validator, Field
from datetime import datetime
import re

# 用户相关Schema
class UserBase(BaseModel):
    username: str
    email: EmailStr

class UserCreate(UserBase):
    password: str

class UserLogin(BaseModel):
    username: str
    password: str

class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    avatar_url: Optional[str] = None

# 密码操作相关Schema
class PasswordChangeRequest(BaseModel):
    old_password: str = Field(..., description="当前密码")
    new_password: str = Field(..., min_length=6, max_length=128, description="新密码")
    confirm_password: str = Field(..., description="确认新密码")
    
    @validator('new_password')
    def validate_password_strength(cls, v):
        """验证密码强度"""
        if len(v) < 6:
            raise ValueError('密码长度至少为6位')
        if len(v) > 128:
            raise ValueError('密码长度不能超过128位')
        
        # 检查是否包含至少一个数字和一个字母
        if not re.search(r'[A-Za-z]', v):
            raise ValueError('密码必须包含至少一个字母')
        if not re.search(r'\d', v):
            raise ValueError('密码必须包含至少一个数字')
            
        return v
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        """验证确认密码与新密码一致"""
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('确认密码与新密码不一致')
        return v

class PasswordChangeResponse(BaseModel):
    success: bool = True
    message: str = "密码修改成功"

# 账户注销相关Schema
class AccountDeleteRequest(BaseModel):
    username_confirmation: str = Field(..., description="输入用户名确认删除")
    
class AccountDeleteResponse(BaseModel):
    success: bool = True
    message: str = "账户已成功删除"

class UserInDB(UserBase):
    id: int
    is_active: bool
    is_admin: bool
    avatar_url: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class User(UserInDB):
    pass

class UserPublic(BaseModel):
    id: int
    username: str
    avatar_url: Optional[str] = None

    class Config:
        from_attributes = True

# Token相关Schema
class Token(BaseModel):
    access_token: str
    token_type: str
    user: "UserInDB"  # 包含用户信息

class TokenData(BaseModel):
    username: Optional[str] = None

# 前向引用解决
Token.model_rebuild()