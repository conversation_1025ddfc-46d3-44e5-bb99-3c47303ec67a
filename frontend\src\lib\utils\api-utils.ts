import { PAGINATION } from '../constants';

/**
 * 构建查询参数字符串
 */
export function buildQueryParams(params: Record<string, any>): URLSearchParams {
  const queryParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        value.forEach(item => queryParams.append(key, String(item)));
      } else {
        queryParams.append(key, String(value));
      }
    }
  });
  
  return queryParams;
}

/**
 * 构建分页参数
 */
export function buildPaginationParams(page?: number, pageSize?: number) {
  const actualPage = Math.max(1, page || 1);
  const actualPageSize = Math.min(PAGINATION.MAX_PAGE_SIZE, pageSize || PAGINATION.DEFAULT_PAGE_SIZE);
  const skip = (actualPage - 1) * actualPageSize;
  
  return {
    skip,
    limit: actualPageSize,
    page: actualPage,
    pageSize: actualPageSize,
  };
}

/**
 * 处理API响应错误
 */
export function createApiError(response: Response, errorData?: any): Error {
  const error = new Error(
    errorData?.message || 
    errorData?.detail || 
    `HTTP ${response.status}: ${response.statusText}`
  ) as any;
  
  error.response = {
    status: response.status,
    statusText: response.statusText,
    data: errorData,
  };
  
  return error;
}

/**
 * 检查响应是否成功
 */
export function isResponseOk(response: Response): boolean {
  return response.ok && response.status >= 200 && response.status < 300;
}

/**
 * 安全的JSON解析
 */
export async function safeJsonParse<T>(response: Response): Promise<T | null> {
  try {
    return await response.json();
  } catch {
    return null;
  }
}

/**
 * 重试机制
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (i === maxRetries) {
        throw lastError;
      }
      
      // 指数退避
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
    }
  }
  
  throw lastError!;
}

/**
 * 请求超时包装器
 */
export function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Request timeout')), timeoutMs);
    }),
  ]);
}
