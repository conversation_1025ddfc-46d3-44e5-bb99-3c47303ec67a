# Hanime1.me 正式爬取系统使用说明

## 🚀 快速开始

### 方法一：快速启动（推荐）
```bash
python quick_start.py
```
然后按提示输入年份、月份等参数即可。

### 方法二：使用配置文件
```bash
python production_crawler.py production_config.yml
```

## 📁 文件说明

### 核心文件
- `production_crawler.py` - 正式爬取脚本（主程序）
- `production_config.yml` - 生产环境配置文件
- `quick_start.py` - 快速启动脚本（交互式配置）
- `unified_crawler.py` - 统一爬虫核心模块

### 测试和验证文件
- `test_config.py` - 配置文件和系统环境验证脚本
- `test_domain_prefix.py` - 域名前缀功能测试脚本
- `full_test_crawl_2025_01.py` - 完整功能测试脚本

### 配置文件结构
```yaml
crawl:
  date_filter:
    year: 2025      # 目标年份
    month: 1        # 目标月份
    # 或多个月份: [1, 2, 3]

strategy:
  max_videos_per_month: 0    # 每月最大视频数（0=无限制）
  skip_existing: true        # 跳过已存在的视频

download:
  enable_video: true         # 是否下载视频
  enable_cover: true         # 是否下载封面
  enable_fanart: true        # 是否下载剧照

web_access:
  domain_prefix: "https://static.denlu.top"  # 域名前缀
  base_path: ""                              # 基础路径（可选）

getchu:
  enabled: true              # 是否启用getchu剧照下载

database:
  host: "***********"       # 数据库地址
  user: "root"              # 数据库用户名
  password: "123456"        # 数据库密码
  database: "hentai"        # 数据库名
```

## 🎯 使用场景

### 1. 爬取2025年1月数据
```bash
# 修改 production_config.yml
year: 2025
month: 1

# 运行
python production_crawler.py
```

### 2. 爬取多个月份
```bash
# 修改配置文件
month: [1, 2, 3]  # 爬取1月、2月、3月

# 运行
python production_crawler.py
```

### 3. 测试模式（限制视频数量）
```bash
# 修改配置文件
strategy:
  max_videos_per_month: 5   # 只爬取5个视频

# 运行
python production_crawler.py
```

### 4. 只下载剧照，不下载视频
```bash
# 修改配置文件
download:
  enable_video: false       # 不下载视频
  enable_fanart: true       # 下载剧照

# 运行
python production_crawler.py
```

### 5. 配置域名前缀（生成完整URL）
```bash
# 修改配置文件
web_access:
  domain_prefix: "https://static.denlu.top"   # 域名前缀
  base_path: ""                               # 基础路径

# 运行后，数据库中的URL将是完整链接，例如：
# https://static.denlu.top/2025/01/视频标题/fanart.jpg
# https://static.denlu.top/2025/01/视频标题/extrafanart/fanart1.jpg
```

### 6. 使用相对路径（不配置域名）
```bash
# 修改配置文件（或留空）
web_access:
  domain_prefix: ""         # 留空使用相对路径
  base_path: ""

# 运行后，数据库中的URL将是相对路径，例如：
# 2025/01/视频标题/fanart.jpg
# 2025/01/视频标题/extrafanart/fanart1.jpg
```

## 📊 输出结果

### 文件结构
```
downloads/
├── 2025/
│   ├── 01/
│   │   ├── 视频标题1/
│   │   │   ├── 视频标题1.mp4
│   │   │   ├── 视频标题1.nfo
│   │   │   ├── fanart.jpg (封面)
│   │   │   ├── poster.jpg (海报)
│   │   │   └── extrafanart/
│   │   │       ├── fanart1.jpg
│   │   │       ├── fanart2.jpg
│   │   │       └── ...
│   │   └── 视频标题2/
│   │       └── ...
│   └── 02/
│       └── ...
```

### 数据库记录
- `animes` 表：视频基本信息
  - `cover`: 封面图片URL（支持完整链接或相对路径）
  - `fanart`: 剧照图片URL列表（逗号分隔，按数字顺序：fanart1.jpg,fanart2.jpg...）
  - `video_url`: 视频文件URL（支持完整链接或相对路径）
- `tags` 表：标签信息
- `anime_tags` 表：视频-标签关联
- `categories` 表：分类信息

### URL格式说明
**配置域名前缀时**：
- 视频：`https://static.denlu.top/2025/01/视频标题/视频标题.mp4`
- 封面：`https://static.denlu.top/2025/01/视频标题/fanart.jpg`
- 剧照：`https://static.denlu.top/2025/01/视频标题/extrafanart/fanart1.jpg,https://static.denlu.top/2025/01/视频标题/extrafanart/fanart2.jpg`

**不配置域名前缀时**：
- 视频：`2025/01/视频标题/视频标题.mp4`
- 封面：`2025/01/视频标题/fanart.jpg`
- 剧照：`2025/01/视频标题/extrafanart/fanart1.jpg,2025/01/视频标题/extrafanart/fanart2.jpg`

### 日志文件
- `production_crawl.log` - 详细运行日志
- 自动轮转，最大50MB，保留10个备份

## ⚙️ 高级配置

### 性能优化
```yaml
performance:
  request_delay: 2          # 请求间隔（秒），避免被封
  max_concurrent_downloads: 1  # 最大并发下载数
```

### 网络配置
```yaml
network:
  timeout: 30               # 请求超时时间
  proxy:                    # 代理设置
    enabled: true
    http: "http://proxy:8080"

web_access:                 # Web访问配置
  domain_prefix: "https://static.denlu.top"  # 域名前缀
  base_path: "/anime"       # 基础路径（可选）
  # 最终URL示例: https://static.denlu.top/anime/2025/01/视频/fanart.jpg
```

### 错误处理
```yaml
strategy:
  continue_on_error: true   # 遇到错误继续爬取
  skip_existing: true       # 跳过已存在的文件
```

## 🔧 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置
   - 确认数据库服务运行正常
   - 检查网络连接

2. **视频下载失败**
   - 检查网络连接
   - 可能需要使用代理
   - 调整超时时间

3. **剧照下载失败**
   - getchu.com可能临时不可用
   - 可以禁用剧照下载继续其他功能

4. **域名前缀配置问题**
   - 检查域名前缀格式是否正确
   - 确认域名前缀以http://或https://开头
   - 验证生成的完整URL是否可访问

5. **内存占用过高**
   - 减少并发数
   - 增加请求间隔

### 调试技巧

1. **查看详细日志**
```yaml
logging:
  level: "DEBUG"            # 输出调试信息
```

2. **测试单个视频**
```yaml
strategy:
  max_videos_per_month: 1   # 只处理1个视频
```

3. **禁用某些功能**
```yaml
download:
  enable_video: false       # 禁用视频下载
  enable_fanart: false      # 禁用剧照下载

web_access:
  domain_prefix: ""         # 禁用域名前缀，使用相对路径
```

4. **测试域名前缀功能**
```bash
# 运行域名前缀测试脚本
python test_domain_prefix.py
```

## 📝 日志说明

### 日志级别
- `INFO` - 基本运行信息
- `DEBUG` - 详细调试信息
- `WARNING` - 警告信息
- `ERROR` - 错误信息

### 关键日志标识
- 🚀 - 启动信息
- 📅 - 月份处理
- 🎬 - 视频处理
- 🎥 - 视频下载
- 🖼️ - 封面下载
- 🎨 - 剧照下载
- ✅ - 成功操作
- ❌ - 失败操作
- ⚠️ - 警告信息

## 🔐 安全建议

1. **数据库安全**
   - 使用强密码
   - 限制数据库访问权限
   - 定期备份数据库

2. **网络安全**
   - 考虑使用代理
   - 控制爬取频率
   - 遵守网站robots.txt

3. **文件安全**
   - 定期备份下载文件
   - 检查磁盘空间
   - 设置合适的文件权限

## 📞 支持

如遇到问题：
1. 查看日志文件定位错误
2. 检查配置文件是否正确
3. 验证数据库连接
4. 测试网络连接

## 🆕 版本更新

### v1.1.0 (最新)
- ✅ 新增域名前缀配置功能
- ✅ 支持生成完整URL链接存储到数据库
- ✅ 兼容相对路径和绝对URL两种模式
- ✅ 更新快速启动脚本支持域名配置
- ✅ 新增域名前缀功能测试脚本

### v1.0.0
- ✅ 完整的视频爬取功能
- ✅ 真实剧照下载
- ✅ 数据库自动导入
- ✅ 按数字排序的fanart字段
- ✅ 完善的错误处理
- ✅ 可配置的爬取参数
- ✅ 详细的日志记录