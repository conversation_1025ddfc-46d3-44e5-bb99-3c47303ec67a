'use client';

import { useEffect, useState } from 'react';
import { X, Bell } from 'lucide-react';

interface Announcement {
  id: number;
  title: string;
  content: string;
  type: string;
  target: string;
  is_active: boolean;
}

export default function AnnouncementPopup() {
  const [announcement, setAnnouncement] = useState<Announcement | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [autoCloseTimer, setAutoCloseTimer] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    fetchActiveAnnouncement();
    
    // 清理定时器
    return () => {
      if (autoCloseTimer) {
        clearTimeout(autoCloseTimer);
      }
    };
  }, []);

  const checkShouldShowAnnouncement = (announcementData: any): boolean => {
    const announcementKey = `announcement_${announcementData.id}`;
    const lastShownData = localStorage.getItem(announcementKey);
    
    // 根据显示频率判断
    switch (announcementData.display_frequency) {
      case 'always':
        // 总是显示
        return true;
        
      case 'once':
        // 仅显示一次
        return !lastShownData;
        
      case 'daily':
        // 每天显示一次
        if (!lastShownData) return true;
        const lastShownDate = new Date(lastShownData);
        const today = new Date();
        return lastShownDate.toDateString() !== today.toDateString();
        
      case 'weekly':
        // 每周显示一次
        if (!lastShownData) return true;
        const lastShownWeek = new Date(lastShownData);
        const now = new Date();
        const daysDiff = Math.floor((now.getTime() - lastShownWeek.getTime()) / (1000 * 60 * 60 * 24));
        return daysDiff >= 7;
        
      default:
        // 默认仅显示一次
        return !lastShownData;
    }
  };

  const fetchActiveAnnouncement = async () => {
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
      const response = await fetch(`${apiUrl}/api/v1/admin/announcements/popup/active`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data && data.id) {
          // 根据显示频率检查是否应该显示
          const shouldShow = checkShouldShowAnnouncement(data);
          if (shouldShow) {
            setAnnouncement(data);
            setIsVisible(true);
            
            // 根据配置设置自动关闭
            if (data.auto_dismiss) {
              const dismissTime = (data.auto_dismiss_seconds || 5) * 1000;
              const timer = setTimeout(() => {
                handleClose();
              }, dismissTime);
              setAutoCloseTimer(timer);
            }
          }
        }
      }
    } catch (error) {
      console.error('Failed to fetch announcement:', error);
    }
  };

  const handleClose = () => {
    if (autoCloseTimer) {
      clearTimeout(autoCloseTimer);
    }
    
    if (announcement) {
      // 记录公告显示时间
      const announcementKey = `announcement_${announcement.id}`;
      localStorage.setItem(announcementKey, new Date().toISOString());
    }
    setIsVisible(false);
  };

  if (!isVisible || !announcement) {
    return null;
  }

  return (
    <>
      {/* 背景遮罩 */}
      <div 
        className="fixed inset-0 bg-black/60 z-[9998] animate-fade-in backdrop-blur-sm"
        onClick={handleClose}
      />
      
      {/* 弹窗内容 */}
      <div className="fixed inset-0 flex items-center justify-center z-[9999] pointer-events-none px-4">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-lg w-full pointer-events-auto animate-slide-up">
          {/* 弹窗头部 */}
          <div className="flex items-center justify-between p-5 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                <Bell className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                {announcement.title}
              </h3>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
              aria-label="关闭"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          {/* 弹窗内容 */}
          <div className="p-5">
            <div 
              className="text-gray-600 dark:text-gray-300 leading-relaxed"
              style={{ whiteSpace: 'pre-wrap' }}
            >
              {announcement.content}
            </div>
          </div>
          
          {/* 弹窗底部 */}
          <div className="flex items-center justify-end p-5 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={handleClose}
              className="px-5 py-2.5 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-lg hover:from-purple-700 hover:to-purple-800 transition-all duration-200 font-medium shadow-lg shadow-purple-500/25"
            >
              我知道了
            </button>
          </div>
        </div>
      </div>
    </>
  );
}