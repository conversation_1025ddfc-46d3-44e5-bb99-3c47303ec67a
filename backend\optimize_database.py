#!/usr/bin/env python3
"""
数据库优化脚本 - 添加索引以提升查询性能
"""
import asyncio
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_database_indexes():
    """创建数据库索引以优化查询性能"""
    engine = create_engine(settings.DATABASE_URL)
    
    # 索引SQL语句
    indexes = [
        # 动漫表优化索引
        "CREATE INDEX IF NOT EXISTS idx_animes_title ON animes(title);",
        "CREATE INDEX IF NOT EXISTS idx_animes_title_english ON animes(title_english);", 
        "CREATE INDEX IF NOT EXISTS idx_animes_title_japanese ON animes(title_japanese);",
        "CREATE INDEX IF NOT EXISTS idx_animes_category_id ON animes(category_id);",
        "CREATE INDEX IF NOT EXISTS idx_animes_view_count ON animes(view_count DESC);",
        "CREATE INDEX IF NOT EXISTS idx_animes_favorite_count ON animes(favorite_count DESC);",
        "CREATE INDEX IF NOT EXISTS idx_animes_created_at ON animes(created_at DESC);",
        "CREATE INDEX IF NOT EXISTS idx_animes_is_active ON animes(is_active);",
        
        # 标签相关索引
        "CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);",
        "CREATE INDEX IF NOT EXISTS idx_anime_tags_anime_id ON anime_tags(anime_id);",
        "CREATE INDEX IF NOT EXISTS idx_anime_tags_tag_id ON anime_tags(tag_id);",
        
        # 收藏表索引
        "CREATE INDEX IF NOT EXISTS idx_favorites_user_id ON favorites(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_favorites_anime_id ON favorites(anime_id);",
        "CREATE INDEX IF NOT EXISTS idx_favorites_user_anime ON favorites(user_id, anime_id);",
        
        # 评论表索引
        "CREATE INDEX IF NOT EXISTS idx_comments_anime_id ON comments(anime_id);",
        "CREATE INDEX IF NOT EXISTS idx_comments_user_id ON comments(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_comments_created_at ON comments(created_at DESC);",
        "CREATE INDEX IF NOT EXISTS idx_comments_is_deleted ON comments(is_deleted);",
        
        # 用户表索引
        "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);",
        "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);",
        "CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);",
        
        # 推荐动漫表索引
        "CREATE INDEX IF NOT EXISTS idx_featured_animes_is_active ON featured_animes(is_active);",
        "CREATE INDEX IF NOT EXISTS idx_featured_animes_order_index ON featured_animes(order_index);",
        "CREATE INDEX IF NOT EXISTS idx_featured_animes_anime_id ON featured_animes(anime_id);",
        
        # 系统配置索引
        "CREATE INDEX IF NOT EXISTS idx_system_configs_key ON system_configs(key);",
        
        # 分类表索引
        "CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name);"
    ]
    
    with engine.connect() as connection:
        for index_sql in indexes:
            try:
                logger.info(f"执行索引创建: {index_sql}")
                connection.execute(text(index_sql))
                connection.commit()
                logger.info("索引创建成功")
            except Exception as e:
                logger.warning(f"索引创建失败或已存在: {e}")
                continue

def optimize_database_settings():
    """优化数据库设置以提升性能"""
    engine = create_engine(settings.DATABASE_URL)
    
    # MySQL/MariaDB优化设置
    optimization_settings = [
        # 查询缓存优化
        "SET GLOBAL query_cache_type = ON;",
        "SET GLOBAL query_cache_size = 67108864;",  # 64MB
        
        # InnoDB缓冲池优化
        "SET GLOBAL innodb_buffer_pool_size = 268435456;",  # 256MB
        
        # 连接优化
        "SET GLOBAL max_connections = 200;",
        "SET GLOBAL wait_timeout = 300;",
        "SET GLOBAL interactive_timeout = 300;",
        
        # 日志优化
        "SET GLOBAL slow_query_log = ON;",
        "SET GLOBAL long_query_time = 2;",
        
        # 表缓存优化
        "SET GLOBAL table_open_cache = 2000;",
        "SET GLOBAL table_definition_cache = 1400;"
    ]
    
    with engine.connect() as connection:
        for setting in optimization_settings:
            try:
                logger.info(f"执行优化设置: {setting}")
                connection.execute(text(setting))
                logger.info("设置优化成功")
            except Exception as e:
                logger.warning(f"设置优化失败: {e}")
                continue

def analyze_table_statistics():
    """分析表统计信息"""
    engine = create_engine(settings.DATABASE_URL)
    
    tables = [
        "animes", "users", "favorites", "comments", 
        "tags", "anime_tags", "categories", 
        "featured_animes", "system_configs"
    ]
    
    with engine.connect() as connection:
        for table in tables:
            try:
                logger.info(f"分析表统计: {table}")
                connection.execute(text(f"ANALYZE TABLE {table};"))
                connection.commit()
                logger.info(f"表 {table} 统计分析完成")
            except Exception as e:
                logger.warning(f"表 {table} 统计分析失败: {e}")
                continue

def main():
    """主函数 - 执行数据库优化"""
    logger.info("开始数据库优化...")
    
    try:
        # 创建索引
        logger.info("1. 创建数据库索引...")
        create_database_indexes()
        
        # 优化数据库设置
        logger.info("2. 优化数据库设置...")
        optimize_database_settings()
        
        # 分析表统计信息
        logger.info("3. 分析表统计信息...")
        analyze_table_statistics()
        
        logger.info("数据库优化完成!")
        
    except Exception as e:
        logger.error(f"数据库优化过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    main()