"""
测试DragonflyDB连接和兼容性
DragonflyDB是一个与Redis协议兼容的高性能内存数据库
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import SessionLocal
from app.core.redis_config import RedisConfigManager
from app.core.redis_client import redis_client
import logging
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_dragonflydb():
    """测试DragonflyDB连接和功能"""
    
    print("\n" + "="*60)
    print("DragonflyDB 兼容性测试")
    print("="*60 + "\n")
    
    db = SessionLocal()
    
    try:
        # 1. 配置DragonflyDB连接
        print("1. 配置DragonflyDB连接...")
        dragonflydb_config = {
            'redis_enabled': True,
            'redis_host': '***********',
            'redis_port': 16379,  # DragonflyDB默认端口
            'redis_db': 0,
            'redis_password': '',  # 如果有密码请设置
            'redis_max_memory': 1024,  # 1GB
            'redis_expire_time': 7200,  # 2小时
            'redis_max_connections': 200  # DragonflyDB支持更多连接
        }
        
        success = RedisConfigManager.update_configs(db, dragonflydb_config)
        if success:
            print("   [OK] DragonflyDB配置已设置")
            print(f"   服务器: {dragonflydb_config['redis_host']}:{dragonflydb_config['redis_port']}")
        else:
            print("   [FAIL] 配置设置失败")
            return
        
        # 2. 测试连接
        print("\n2. 测试DragonflyDB连接...")
        await redis_client.init_redis(force_reconnect=True)
        
        if redis_client.is_available:
            print("   [OK] DragonflyDB连接成功！")
            
            # 获取服务器信息
            try:
                info = await redis_client.redis_client.info()
                if 'dragonfly_version' in info:
                    print(f"   [INFO] DragonflyDB版本: {info.get('dragonfly_version', 'unknown')}")
                else:
                    print(f"   [INFO] 服务器类型: {info.get('redis_version', 'unknown')}")
            except:
                print("   [INFO] 无法获取服务器信息")
        else:
            print("   [FAIL] 无法连接到DragonflyDB")
            print("   请确保DragonflyDB服务运行在 ***********:16379")
            return
        
        # 3. 测试基本操作
        print("\n3. 测试基本缓存操作...")
        
        # 字符串操作
        test_key = "test:dragonfly:string"
        test_value = "DragonflyDB测试数据"
        await redis_client.set(test_key, test_value, 60)
        cached = await redis_client.get(test_key)
        if cached == test_value:
            print("   [OK] 字符串缓存操作正常")
        else:
            print("   [FAIL] 字符串缓存操作失败")
        
        # 复杂对象操作
        complex_key = "test:dragonfly:object"
        complex_value = {
            "id": 1,
            "name": "DragonflyDB测试",
            "features": ["高性能", "低延迟", "Redis兼容"],
            "metrics": {
                "qps": 1000000,
                "latency": 0.1
            }
        }
        await redis_client.set(complex_key, complex_value, 60)
        cached_obj = await redis_client.get(complex_key)
        if cached_obj and cached_obj['name'] == complex_value['name']:
            print("   [OK] 复杂对象缓存操作正常")
        else:
            print("   [FAIL] 复杂对象缓存操作失败")
        
        # 4. 性能测试
        print("\n4. 性能测试...")
        
        # 写入性能测试
        start_time = time.time()
        for i in range(1000):
            await redis_client.set(f"perf:test:{i}", {"value": i, "data": "x" * 100}, 60)
        write_time = time.time() - start_time
        write_qps = 1000 / write_time
        print(f"   [OK] 写入1000条: {write_time:.2f}秒 (QPS: {write_qps:.0f})")
        
        # 读取性能测试
        start_time = time.time()
        for i in range(1000):
            await redis_client.get(f"perf:test:{i}")
        read_time = time.time() - start_time
        read_qps = 1000 / read_time
        print(f"   [OK] 读取1000条: {read_time:.2f}秒 (QPS: {read_qps:.0f})")
        
        # 5. 清理测试数据
        print("\n5. 清理测试数据...")
        await redis_client.delete(test_key)
        await redis_client.delete(complex_key)
        for i in range(1000):
            await redis_client.delete(f"perf:test:{i}")
        print("   [OK] 测试数据已清理")
        
        # 6. 内存信息
        print("\n6. DragonflyDB内存状态...")
        memory_info = await redis_client.get_memory_info()
        if memory_info:
            print(f"   使用内存: {memory_info['used_memory_human']}")
            if memory_info.get('maxmemory', 0) > 0:
                print(f"   内存限制: {memory_info['maxmemory_human']}")
            print(f"   淘汰策略: {memory_info.get('maxmemory_policy', 'noeviction')}")
        
        print("\n" + "="*60)
        print("✅ DragonflyDB测试完成！")
        print("="*60 + "\n")
        
        print("DragonflyDB优势：")
        print("- 🚀 性能比Redis高5-10倍")
        print("- 💾 内存效率提升30%")
        print("- 🔧 完全兼容Redis协议")
        print("- 📈 支持更多并发连接")
        print("- ⚡ 更低的延迟")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        print(f"\n[ERROR] 测试失败: {e}")
        print("\n请检查：")
        print("1. DragonflyDB是否运行在 ***********:16379")
        print("2. 网络连接是否正常")
        print("3. 防火墙设置是否允许访问")
    finally:
        db.close()
        await redis_client.close_redis()

if __name__ == "__main__":
    asyncio.run(test_dragonflydb())