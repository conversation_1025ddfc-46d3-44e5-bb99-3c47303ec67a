// API相关常量
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    ME: '/auth/me',
    LOGOUT: '/auth/logout',
  },
  
  // 动漫相关
  ANIME: {
    LIST: '/animes',
    SEARCH: '/animes/search',
    FEATURED: '/animes/featured',
    PLAYER_CONFIG: '/animes/player-config',
    BY_ID: (id: number) => `/animes/${id}`,
    RECOMMENDATIONS: (id: number) => `/animes/${id}/recommendations`,
    VIEW: (id: number) => `/animes/${id}/view`,
  },
  
  // 漫画相关
  MANGA: {
    LIST: '/manga',
    POPULAR: '/manga/popular',
    RECENT: '/manga/recent',
    SEARCH_BY_TAGS: '/manga/search/by-tags',
    BY_ID: (id: number) => `/manga/${id}`,
    RELATED: (id: number) => `/manga/${id}/related`,
    CHAPTERS: (id: number) => `/manga/${id}/chapters`,
    CHAPTER_BY_NUMBER: (id: number, chapterNumber: number) => `/manga/${id}/chapters/${chapterNumber}`,
    READING_PROGRESS: (id: number) => `/manga/${id}/reading-progress`,
    READING_HISTORY: '/manga/reading-history',
    BOOKMARKS: '/manga/bookmarks',
  },
  
  // 收藏相关
  FAVORITES: {
    LIST: '/favorites',
    CHECK: '/favorites/check',
    ADD: '/favorites',
    REMOVE: '/favorites',
  },
  
  // 评论相关
  COMMENTS: {
    ANIME: (id: number) => `/comments/anime/${id}`,
    MANGA: (id: number) => `/comments/manga/${id}`,
    BY_ID: (id: number) => `/comments/${id}`,
    REPLIES: (id: number) => `/comments/${id}/replies`,
    LIKE: (id: number) => `/comments/${id}/like`,
    LIKE_STATUS: (id: number) => `/comments/${id}/like-status`,
  },
  
  // 用户相关
  USER: {
    PROFILE: '/user/profile',
    STATS: '/user/stats',
    FAVORITES: '/user/favorites',
    FAVORITES_SUMMARY: '/user/favorites/summary',
    READING_HISTORY: '/user/reading-history',
    PASSWORD: '/user/password',
    ACCOUNT: '/user/account',
  },
  
  // 管理员相关
  ADMIN: {
    STATS: '/auth/admin/stats',
    USERS: '/admin/users',
    FEATURED: '/admin/featured',
    CONFIGS: '/admin/configs',
    EMAIL: '/admin/email',
    ANNOUNCEMENTS: '/admin/announcements',
  },
} as const;

// 分页相关常量
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  DEFAULT_SKIP: 0,
  MAX_PAGE_SIZE: 100,
} as const;

// 本地存储键名
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  USER_PREFERENCES: 'user_preferences',
  READING_SETTINGS: 'reading_settings',
  THEME: 'theme',
} as const;

// 阅读器相关常量
export const READER = {
  MODES: {
    FLIP: 'flip',
    SCROLL: 'scroll',
  },
  DIRECTIONS: {
    LTR: 'ltr',
    RTL: 'rtl',
  },
  ZOOM_LEVELS: [0.5, 0.75, 1, 1.25, 1.5, 2],
  DEFAULT_ZOOM: 1,
} as const;

// 主题相关常量
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
} as const;

// 内容类型
export const CONTENT_TYPES = {
  ANIME: 'anime',
  MANGA: 'manga',
} as const;

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  UNAUTHORIZED: '请先登录后再进行此操作',
  FORBIDDEN: '您没有权限执行此操作',
  NOT_FOUND: '请求的资源不存在',
  SERVER_ERROR: '服务器内部错误，请稍后重试',
  VALIDATION_ERROR: '输入数据格式不正确',
  UNKNOWN_ERROR: '未知错误，请稍后重试',
} as const;

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '登录成功',
  REGISTER_SUCCESS: '注册成功',
  LOGOUT_SUCCESS: '退出登录成功',
  FAVORITE_ADDED: '收藏成功',
  FAVORITE_REMOVED: '取消收藏成功',
  COMMENT_ADDED: '评论发表成功',
  COMMENT_UPDATED: '评论更新成功',
  COMMENT_DELETED: '评论删除成功',
  PROFILE_UPDATED: '个人信息更新成功',
  PASSWORD_CHANGED: '密码修改成功',
  LINK_COPIED: '链接已复制到剪贴板',
} as const;

// 验证规则
export const VALIDATION = {
  USERNAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 20,
    PATTERN: /^[a-zA-Z0-9_]+$/,
  },
  PASSWORD: {
    MIN_LENGTH: 6,
    MAX_LENGTH: 128,
  },
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
} as const;

// 文件上传相关
export const UPLOAD = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  ALLOWED_VIDEO_TYPES: ['video/mp4', 'video/webm'],
} as const;

// 动画持续时间
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
} as const;

// 断点
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
} as const;
