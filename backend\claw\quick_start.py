#!/usr/bin/env python3
"""
快速启动脚本 - 简化版
用于快速修改配置和启动爬取
"""

import yaml
from production_crawler import ProductionCrawler

def main():
    """快速启动主函数"""
    print("=== Hanime1.me 快速爬取工具 ===")
    
    # 交互式配置
    print("\n📋 请输入爬取参数:")
    
    # 获取年份
    year = input("请输入年份 [2025]: ").strip()
    if not year:
        year = 2025
    else:
        year = int(year)
    
    # 获取月份
    month = input("请输入月份 [1]: ").strip()
    if not month:
        month = 1
    else:
        month = int(month)
    
    # 获取最大视频数
    max_videos = input("最大视频数 (0=无限制) [0]: ").strip()
    if not max_videos:
        max_videos = 0
    else:
        max_videos = int(max_videos)
    
    # 是否启用剧照下载
    enable_fanart = input("是否下载剧照? (y/n) [y]: ").strip().lower()
    enable_fanart = enable_fanart != 'n'
    
    # 获取域名前缀配置
    domain_prefix = input("域名前缀 (留空使用相对路径) []: ").strip()
    
    print(f"\n🚀 配置确认:")
    print(f"  年份: {year}")
    print(f"  月份: {month}")
    print(f"  最大视频数: {'无限制' if max_videos == 0 else max_videos}")
    print(f"  下载剧照: {'是' if enable_fanart else '否'}")
    print(f"  域名前缀: {domain_prefix if domain_prefix else '使用相对路径'}")
    
    confirm = input("\n确认开始爬取? (y/n) [y]: ").strip().lower()
    if confirm == 'n':
        print("已取消")
        return
    
    # 创建临时配置
    config = {
        'app': {
            'name': "Hanime1.me 快速爬取",
            'version': "1.0.0"
        },
        'crawl': {
            'base_url': "https://hanime1.me",
            'date_filter': {
                'year': year,
                'month': month
            },
            'quality_priority': ["1080", "720", "480"],
            'skip_keywords': ["中字後補", "简中补字", "Chinese Sub", "中文字幕後補"]
        },
        'download': {
            'download_dir': "downloads",
            'organize_by_date': True,
            'enable_video': True,
            'enable_cover': True,
            'enable_fanart': enable_fanart
        },
        'database': {
            'host': "***********",
            'port': 3306,
            'user': "root",
            'password': "123456",
            'database': "hentai",
            'charset': "utf8mb4"
        },
        'strategy': {
            'mode': "full",
            'max_videos_per_month': max_videos,
            'continue_on_error': True,
            'skip_existing': True
        },
        'getchu': {
            'enabled': enable_fanart,
            'search_timeout': 30,
            'download_timeout': 30,
            'max_images_per_video': 50
        },
        'logging': {
            'level': "INFO",
            'format': "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            'file': f"crawl_{year}_{month:02d}.log",
            'console_output': True
        },
        'performance': {
            'request_delay': 2,
            'max_concurrent_downloads': 1
        },
        'network': {
            'timeout': 30,
            'max_retries': 3
        },
        'web_access': {
            'domain_prefix': domain_prefix,
            'base_path': ""
        },
        'selenium': {
            'headless': True,
            'page_load_timeout': 30
        }
    }
    
    # 保存临时配置
    temp_config_file = f"quick_config_{year}_{month:02d}.yml"
    with open(temp_config_file, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"💾 配置已保存到: {temp_config_file}")
    
    # 开始爬取
    print(f"\n🎬 开始爬取 {year}年{month}月 的数据...")
    
    try:
        crawler = ProductionCrawler(temp_config_file)
        success = crawler.run_production_crawl()
        
        if success:
            print(f"\n🎉 {year}年{month}月 爬取完成!")
            print(f"📁 文件保存在: downloads/{year}/{month:02d}/")
            print(f"📄 日志文件: {config['logging']['file']}")
        else:
            print(f"\n❌ {year}年{month}月 爬取失败!")
            print(f"📄 请查看日志文件: {config['logging']['file']}")
            
    except KeyboardInterrupt:
        print(f"\n⏹️ 用户中断爬取")
    except Exception as e:
        print(f"\n💥 爬取过程中发生错误: {e}")

if __name__ == "__main__":
    main()