#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试脚本 - 测试 ikanmh.top 封面和标签提取
"""

import requests
import json
from lxml import etree
from urllib.parse import urljoin

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"配置文件加载失败: {e}")
        return None

def test_specific_manga():
    """测试特定漫画页面 - https://ikanmh.top/book/642"""
    config = load_config()
    if not config:
        return
    
    url = "https://ikanmh.top/book/642"
    headers = config['crawler']['headers']
    
    print(f"测试URL: {url}")
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            response.encoding = 'utf-8'
            print(f"请求成功，状态码: {response.status_code}")
            
            soup = etree.HTML(response.text)
            
            # 测试标题 - 根据实际HTML结构
            title_elements = soup.xpath('//div[@class="info"]/h1/text()')
            title = title_elements[0].strip() if title_elements else "未找到标题"
            print(f"标题: {title}")
            
            # 测试作者
            author_elements = soup.xpath('//p[@class="subtitle"][contains(text(), "作者：")]/text()')
            if author_elements:
                author_text = author_elements[0]
                author = author_text.replace('作者：', '').strip()
                print(f"作者: {author}")
            else:
                print("作者: 未找到作者")
            
            # 测试封面 - 根据实际HTML结构
            cover_elements = soup.xpath('//div[@class="cover"]/img/@src')
            if cover_elements:
                cover = cover_elements[0]
                print(f"封面: {cover}")
            else:
                print("封面: 未找到封面")
            
            # 测试简介 - 根据实际HTML结构
            description_elements = soup.xpath('//p[@class="content"]/text()')
            if description_elements:
                description = description_elements[0].strip()
                print(f"简介: {description[:100]}...")
            else:
                print("简介: 未找到简介")
            
            # 测试标签 - 根据实际HTML结构
            tag_links = soup.xpath('//span[@class="block"][contains(text(), "标签：")]//a/text()')
            if tag_links:
                tags = [tag.strip() for tag in tag_links if tag.strip()]
                print(f"找到标签: {tags}")
                print(f"标签数量: {len(tags)}")
            else:
                print("找到标签: []")
                print("标签数量: 0")
            
            # 保存HTML片段用于分析
            with open('debug_specific.html', 'w', encoding='utf-8') as f:
                f.write(response.text[:10000])  # 保存前10000字符
            print("页面HTML片段已保存到 debug_specific.html")
            
        else:
            print(f"请求失败，状态码: {response.status_code}")
    
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_specific_manga()