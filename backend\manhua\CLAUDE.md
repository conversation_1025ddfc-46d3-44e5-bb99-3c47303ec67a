# Claude Code 项目配置

## 项目架构
- 目标网站https://ikanmh.top/
- 目标页面https://ikanmh.top/booklist?page={}&end=0
- 从目标页面获取漫画详情页https://ikanmh.top/book/642
- 从详情页获取标题，作者，状态，地区，标签，简介
- 从详情页得到章节https://ikanmh.top/chapter/27015 ...
- 获取章节的话数，标题，图片。
- 图片下载为001.jpg，002.jpg...，下载文件夹按照装年/月/日/时间戳这样配置
- 注意防重复设置，不要重复爬取和下载

## 配置项目

1. 通过配置文件将数据转移到数据库，读取anime-website进行正确导入完整数据。
2. 配置文件包含文件的下载路径配置。
3. 爬取配置也在配置文件中

## 总结

爬取漫画图片，导入到anime-website项目的数据库中，请确保数据库与代码贴合