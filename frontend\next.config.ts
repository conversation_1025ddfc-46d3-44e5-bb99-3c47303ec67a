import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // 允许开发环境跨域请求
  allowedDevOrigins: ['************:3000'],
  
  // 生产环境优化设置
  poweredByHeader: false, // 移除X-Powered-By头部
  compress: true, // 启用gzip压缩
  
  // ESLint和TypeScript配置
  eslint: {
    // 在构建时忽略ESLint错误
    ignoreDuringBuilds: true,
  },
  typescript: {
    // 在构建时忽略TypeScript错误（仅对于any类型警告）
    ignoreBuildErrors: true,
  },
  
  // 实验性功能
  experimental: {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-label', '@radix-ui/react-separator'],
  },
  
  // 图片优化配置
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: '**',
      },
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
    unoptimized: false, // 启用Next.js图片优化
  },
  
  // 添加模块解析配置和性能优化
  webpack: (config, { isServer }) => {
    // 确保正确解析node_modules
    config.resolve.symlinks = false;
    return config;
  },
  
  // 环境变量配置
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
};

export default nextConfig;
