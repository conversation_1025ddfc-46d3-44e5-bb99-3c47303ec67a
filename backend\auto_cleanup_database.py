#!/usr/bin/env python3
"""
自动数据库清理脚本 - 直接清空所有表数据并重置自增ID
⚠️  警告：此脚本将自动删除所有数据！
"""
from sqlalchemy import create_engine, text
from sqlalchemy.orm import Session
from app.core.config import settings
from app.core.database import SessionLocal
from app.models import Base, User, Anime, Category, Tag, AnimeTag, Favorite, Comment

def auto_cleanup_all_tables():
    """自动清理所有表数据并重置自增ID"""
    db = SessionLocal()
    try:
        print("🗑️  开始自动清理数据库...")
        
        # 禁用外键检查（MySQL/MariaDB）
        db.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
        
        # 定义表清理顺序（考虑外键约束）
        tables_to_clean = [
            ("comments", "评论"),
            ("favorites", "收藏"),
            ("anime_tags", "动漫-标签关联"),
            ("animes", "动漫"),
            ("categories", "分类"),
            ("tags", "标签"),
            ("users", "用户")
        ]
        
        total_deleted = 0
        
        # 清理每个表
        for table_name, table_desc in tables_to_clean:
            try:
                # 删除所有数据
                result = db.execute(text(f"DELETE FROM {table_name}"))
                deleted_count = result.rowcount
                total_deleted += deleted_count
                
                # 重置自增ID
                db.execute(text(f"ALTER TABLE {table_name} AUTO_INCREMENT = 1"))
                
                print(f"  ✅ {table_desc} ({table_name}): 删除了 {deleted_count} 条记录")
                
            except Exception as e:
                print(f"  ❌ 清理 {table_desc} ({table_name}) 失败: {e}")
        
        # 重新启用外键检查
        db.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
        
        # 提交所有更改
        db.commit()
        
        print(f"\n✅ 数据库清理完成！")
        print(f"📊 总计删除了 {total_deleted} 条记录")
        print(f"🔄 所有表的自增ID已重置为1")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理过程中发生错误: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def check_database_connection():
    """检查数据库连接"""
    try:
        engine = create_engine(settings.DATABASE_URL)
        with engine.connect() as connection:
            result = connection.execute(text("SELECT DATABASE()"))
            db_name = result.fetchone()[0]
            print(f"📊 连接到数据库: {db_name}")
            return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🧹 自动数据库清理工具")
    print("="*40)
    
    # 检查数据库连接
    if not check_database_connection():
        print("❌ 无法连接到数据库，请检查配置")
        return False
    
    # 执行清理
    if auto_cleanup_all_tables():
        print("\n🎉 清理成功完成！现在可以重新导入数据")
        return True
    else:
        print("\n❌ 清理失败")
        return False

if __name__ == "__main__":
    main()
