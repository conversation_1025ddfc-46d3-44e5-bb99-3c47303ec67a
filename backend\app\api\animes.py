from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.deps import get_current_active_user, get_current_admin_user
from app.crud.anime import AnimeCRUD
from app.schemas.anime import Anime, AnimeCreate, AnimeUpdate
from app.models import User, FeaturedAnime
from app.core.config_manager import ConfigManager
from app.middleware.cache import get_cache_decorator, invalidate_cache_pattern

router = APIRouter()

@router.get("/", summary="获取动漫列表")
@get_cache_decorator("anime_basic_info")
async def get_animes(
    skip: int = Query(0, ge=0, description="跳过的数量"),
    limit: int = Query(20, ge=1, le=100, description="返回的数量"),
    category_id: Optional[int] = Query(None, description="分类ID筛选"),
    search: Optional[str] = Query(None, description="搜索关键词（标题或标签）"),
    db: Session = Depends(get_db)
):
    result = AnimeCRUD.get_animes(
        db, skip=skip, limit=limit, category_id=category_id, search=search
    )
    return result

@router.get("/search", response_model=List[Anime], summary="搜索动漫")
@get_cache_decorator("search_results")
async def search_animes(
    title: str = Query(..., description="动漫标题关键词"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db)
):
    animes = AnimeCRUD.search_animes(db, title=title, skip=skip, limit=limit)
    return animes

@router.get("/featured", summary="获取首页推荐动漫")
@get_cache_decorator("featured")
async def get_featured_animes(
    limit: int = Query(6, ge=1, le=20, description="返回的数量"),
    db: Session = Depends(get_db)
):
    """获取首页推荐动漫列表（公开API）"""
    # 获取推荐的动漫ID列表
    featured_animes = db.query(FeaturedAnime).filter(
        FeaturedAnime.is_active == True
    ).order_by(FeaturedAnime.order_index).limit(limit).all()
    
    if not featured_animes:
        # 如果没有配置推荐，返回最新的动漫
        result = AnimeCRUD.get_animes(
            db=db,
            skip=0,
            limit=limit
        )
        return {"animes": result["animes"], "total": len(result["animes"])}
    
    # 获取推荐动漫的详细信息，包含自定义海报URL
    animes = []
    for featured_anime in featured_animes:
        anime = AnimeCRUD.get_anime_by_id(db, featured_anime.anime_id)
        if anime:
            # 将anime转换为字典，添加featured相关字段
            anime_dict = anime.__dict__.copy()
            # 移除SQLAlchemy内部字段
            anime_dict.pop('_sa_instance_state', None)
            # 添加featured相关字段
            anime_dict['custom_poster_url'] = featured_anime.custom_poster_url
            anime_dict['order_index'] = featured_anime.order_index
            animes.append(anime_dict)
    
    return {"animes": animes, "total": len(animes)}

@router.get("/player-config", summary="获取播放器配置（公开）")
def get_player_config_public(db: Session = Depends(get_db)):
    """获取播放器配置（公开接口，前端使用）"""
    try:
        config_manager = ConfigManager(db)
        config = config_manager.get_player_config()
        return {"success": True, "data": config}
    except Exception as e:
        print(f"Error getting player config: {e}")
        return {"success": False, "error": str(e), "data": {}}

@router.get("/{anime_id}", response_model=Anime, summary="获取动漫详情")
@get_cache_decorator("anime_basic_info")
async def get_anime(anime_id: int, db: Session = Depends(get_db)):
    try:
        anime = AnimeCRUD.get_anime_by_id(db, anime_id)
        if not anime:
            raise HTTPException(status_code=404, detail="动漫不存在")
        
        return anime
    except Exception as e:
        print(f"Error getting anime {anime_id}: {e}")
        raise HTTPException(status_code=500, detail="获取动漫详情失败")

@router.get("/{anime_id}/recommendations", summary="获取动漫推荐")
@get_cache_decorator("featured")
async def get_anime_recommendations(
    anime_id: int, 
    limit: int = Query(12, ge=1, le=20, description="推荐数量"),
    db: Session = Depends(get_db)
):
    """获取动漫推荐列表
    
    推荐策略：
    1. 优先推荐同名里番系列
    2. 然后推荐相同标签的动漫
    3. 最后推荐相同分类的动漫
    4. 补充热门动漫
    """
    try:
        recommendations = AnimeCRUD.get_anime_recommendations(db, anime_id, limit)
        return {"recommendations": recommendations, "total": len(recommendations)}
    except Exception as e:
        print(f"Error getting recommendations for anime {anime_id}: {e}")
        raise HTTPException(status_code=500, detail="获取推荐失败")

@router.post("/{anime_id}/view", summary="增加观看次数")
def increment_view_count(anime_id: int, db: Session = Depends(get_db)):
    try:
        anime = AnimeCRUD.get_anime_by_id(db, anime_id)
        if not anime:
            raise HTTPException(status_code=404, detail="动漫不存在")
        
        AnimeCRUD.increment_view_count(db, anime_id)
        return {"message": "观看次数已更新"}
    except Exception as e:
        print(f"Error incrementing view count for anime {anime_id}: {e}")
        raise HTTPException(status_code=500, detail="更新观看次数失败")

# 移除剧照路由，使用 fanart 作为单张预览图

@router.post("/", response_model=Anime, summary="创建动漫（管理员）")
def create_anime(
    anime: AnimeCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    return AnimeCRUD.create_anime(db, anime)

@router.put("/{anime_id}", response_model=Anime, summary="更新动漫（管理员）")
def update_anime(
    anime_id: int,
    anime_update: AnimeUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    anime = AnimeCRUD.update_anime(db, anime_id, anime_update)
    if not anime:
        raise HTTPException(status_code=404, detail="动漫不存在")
    return anime