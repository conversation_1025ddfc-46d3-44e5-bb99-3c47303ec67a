'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { 
  initPagePersistence, 
  cleanupPagePersistence, 
  shouldRestorePageState, 
  restoreScrollPosition,
  savePageState 
} from '@/lib/utils/page-persistence';

interface PagePersistenceProps {
  children: React.ReactNode;
  enabled?: boolean;
}

/**
 * 页面持久化组件
 * 自动保存和恢复页面状态，防止刷新时跳转
 */
export function PagePersistence({ children, enabled = true }: PagePersistenceProps) {
  const pathname = usePathname();

  useEffect(() => {
    if (!enabled) return;

    // 初始化页面持久化
    initPagePersistence();

    // 检查是否需要恢复页面状态
    if (shouldRestorePageState(pathname)) {
      restoreScrollPosition();
    }

    // 定期保存页面状态
    const saveInterval = setInterval(() => {
      savePageState(pathname);
    }, 30000); // 每30秒保存一次

    // 清理函数
    return () => {
      clearInterval(saveInterval);
      cleanupPagePersistence();
    };
  }, [pathname, enabled]);

  // 路径变化时保存当前页面状态
  useEffect(() => {
    if (!enabled) return;
    
    return () => {
      savePageState(pathname);
    };
  }, [pathname, enabled]);

  return <>{children}</>;
}

/**
 * 页面刷新检测Hook
 */
export function usePageRefreshDetection() {
  useEffect(() => {
    // 检测页面是否是通过刷新加载的
    const isPageRefresh = performance.navigation?.type === 1 || 
                         performance.getEntriesByType('navigation')[0]?.type === 'reload';
    
    if (isPageRefresh) {
      console.log('Page was refreshed, maintaining current state');
      
      // 可以在这里添加特殊的刷新处理逻辑
      // 例如显示"页面已刷新"的提示
    }
  }, []);
}

/**
 * 防止意外离开页面的Hook
 */
export function usePreventAccidentalNavigation(
  shouldPrevent: boolean = false,
  message: string = '您确定要离开此页面吗？未保存的更改将丢失。'
) {
  useEffect(() => {
    if (!shouldPrevent) return;

    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      e.preventDefault();
      e.returnValue = message;
      return message;
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [shouldPrevent, message]);
}
