"""Add custom_poster_url to featured_animes

Revision ID: 387c1abccb19
Revises: 82794a845793
Create Date: 2025-08-16 23:58:55.449559

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '387c1abccb19'
down_revision: Union[str, None] = '82794a845793'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('featured_animes', sa.Column('custom_poster_url', sa.String(length=1000), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('featured_animes', 'custom_poster_url')
    # ### end Alembic commands ###
