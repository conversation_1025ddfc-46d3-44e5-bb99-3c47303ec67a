'use client';

import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { MangaChapter } from '@/lib/api';
import { ChevronRight, BookOpen } from 'lucide-react';

interface ChapterListDialogProps {
  isOpen: boolean;
  onClose: () => void;
  chapters: MangaChapter[];
  currentChapterId: number;
  mangaId: number;
}

export function ChapterListDialog({
  isOpen,
  onClose,
  chapters,
  currentChapterId,
  mangaId
}: ChapterListDialogProps) {
  const router = useRouter();
  
  const handleChapterSelect = (chapterNumber: number) => {
    router.push(`/manga/${mangaId}/chapter/${chapterNumber}`);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            <div className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              章节目录
            </div>
          </DialogTitle>
        </DialogHeader>
        
        <div className="max-h-96 overflow-y-auto pr-4">
          <div className="space-y-2">
            {chapters.map((chapter) => (
              <Button
                key={chapter.id}
                variant={chapter.id === currentChapterId ? "default" : "ghost"}
                className="w-full justify-between h-auto py-3 px-4"
                onClick={() => handleChapterSelect(chapter.chapter_number)}
              >
                <div className="flex items-center gap-3">
                  <div className="text-left">
                    <div className="font-medium">
                      第 {chapter.chapter_number} 话
                    </div>
                    {chapter.title && (
                      <div className="text-sm text-muted-foreground truncate max-w-48">
                        {chapter.title}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {chapter.id === currentChapterId && (
                    <Badge variant="secondary" className="text-xs">
                      当前
                    </Badge>
                  )}
                  <ChevronRight className="h-4 w-4" />
                </div>
              </Button>
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}