from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.engine import make_url
from app.core.config import settings

# 解析数据库URL并确保使用正确的MariaDB/MySQL配置
database_url = make_url(settings.DATABASE_URL)

# 创建同步引擎，针对MariaDB云数据库优化
engine = create_engine(
    settings.DATABASE_URL,
    echo=settings.DEBUG,  # 生产环境自动关闭SQL日志
    # MariaDB/MySQL优化配置 - 紧急性能修复
    pool_size=2,                               # 减少连接池大小
    max_overflow=5,                            # 减少溢出连接数
    pool_recycle=1800,                         # 减少连接回收时间到30分钟
    pool_pre_ping=True,                        # 保持连接健康检查
    pool_timeout=10,                           # 减少连接超时时间
    # MariaDB特定连接参数 - 紧急优化
    connect_args={
        "charset": "utf8mb4",
        "use_unicode": True,
        "connect_timeout": 5,          # 减少连接超时
        "read_timeout": 10,            # 减少读取超时
        "write_timeout": 10,           # 减少写入超时
        "autocommit": False,           # 关闭自动提交
    }
)

# 创建会话
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

# 数据库依赖
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()