"""
Cloudflare Turnstile验证服务
"""
import httpx
from typing import Dict, Any
from sqlalchemy.orm import Session
from app.core.config_manager import ConfigManager


class TurnstileService:
    """Turnstile验证服务类"""
    
    VERIFY_URL = "https://challenges.cloudflare.com/turnstile/v0/siteverify"
    
    def __init__(self, db: Session):
        self.db = db
        self.config_manager = ConfigManager(db)
    
    def is_turnstile_enabled(self) -> bool:
        """检查是否启用Turnstile验证"""
        return self.config_manager.get_config("enable_turnstile", "false").lower() == "true"
    
    def get_site_key(self) -> str:
        """获取Turnstile站点密钥"""
        return self.config_manager.get_config("turnstile_site_key", "")
    
    def get_secret_key(self) -> str:
        """获取Turnstile密钥"""
        return self.config_manager.get_config("turnstile_secret_key", "")
    
    async def verify_token(self, token: str, remote_ip: str = None) -> Dict[str, Any]:
        """验证Turnstile令牌"""
        if not self.is_turnstile_enabled():
            return {
                "success": True,
                "message": "Turnstile验证未启用"
            }
        
        secret_key = self.get_secret_key()
        if not secret_key:
            return {
                "success": False,
                "error": "Turnstile密钥未配置"
            }
        
        if not token:
            return {
                "success": False,
                "error": "验证令牌不能为空"
            }
        
        # 构建验证请求
        data = {
            "secret": secret_key,
            "response": token
        }
        
        if remote_ip:
            data["remoteip"] = remote_ip
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.VERIFY_URL,
                    data=data,
                    timeout=10.0
                )
                
                if response.status_code != 200:
                    return {
                        "success": False,
                        "error": f"验证请求失败: HTTP {response.status_code}"
                    }
                
                result = response.json()
                
                if result.get("success", False):
                    return {
                        "success": True,
                        "message": "验证成功",
                        "challenge_ts": result.get("challenge_ts"),
                        "hostname": result.get("hostname")
                    }
                else:
                    error_codes = result.get("error-codes", [])
                    error_message = self._get_error_message(error_codes)
                    
                    return {
                        "success": False,
                        "error": error_message,
                        "error_codes": error_codes
                    }
                    
        except httpx.TimeoutException:
            return {
                "success": False,
                "error": "验证请求超时"
            }
        except httpx.RequestError as e:
            return {
                "success": False,
                "error": f"验证请求失败: {str(e)}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"验证过程中发生错误: {str(e)}"
            }
    
    def _get_error_message(self, error_codes: list) -> str:
        """获取错误消息"""
        error_messages = {
            "missing-input-secret": "缺少密钥参数",
            "invalid-input-secret": "密钥参数无效或格式错误",
            "missing-input-response": "缺少响应参数",
            "invalid-input-response": "响应参数无效或格式错误",
            "bad-request": "请求格式错误",
            "timeout-or-duplicate": "响应已过期或重复提交",
            "internal-error": "内部错误"
        }
        
        if not error_codes:
            return "验证失败"
        
        messages = []
        for code in error_codes:
            message = error_messages.get(code, f"未知错误: {code}")
            messages.append(message)
        
        return "; ".join(messages)
    
    def get_client_config(self) -> Dict[str, Any]:
        """获取客户端配置"""
        return {
            "enabled": self.is_turnstile_enabled(),
            "site_key": self.get_site_key() if self.is_turnstile_enabled() else ""
        }
