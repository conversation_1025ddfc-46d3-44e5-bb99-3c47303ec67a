# 中文搜索问题修复说明

## 问题描述
搜索"冤罪 1"时显示"未找到相关内容"，但数据库中确实存在该动漫。

## 问题根源分析

### 1. MySQL中ILIKE函数的问题
- SQLAlchemy在MySQL中将`ilike()`转换为`lower(field) LIKE lower(value)`
- MySQL的`LOWER()`函数对中文字符处理不佳，可能导致匹配失败
- 中文字符本身没有大小写区别，不需要大小写不敏感搜索

### 2. 搜索模式单一
- 原来只使用`%搜索词%`的模糊匹配
- 无法处理"冤罪 1"这种带空格的搜索词
- 缺少精确匹配和多种匹配模式

## 修复方案

### 1. 使用LIKE替代ILIKE (针对中文)
```python
# 修复前：
Anime.title.ilike(search_term)  # 会调用LOWER()函数

# 修复后：
Anime.title.like(search_term)   # 直接LIKE匹配，避免中文字符问题
```

### 2. 多种搜索模式
```python
search_term = f"%{search}%"          # 模糊匹配：%冤罪 1%
search_exact = search                 # 精确匹配：冤罪 1
search_no_space = search.replace(" ", "")  # 去空格：冤罪1
search_no_space_term = f"%{search_no_space}%"  # 去空格模糊：%冤罪1%
```

### 3. 针对不同字段的优化处理
- **中文标题**: 使用`like()`避免字符转换问题
- **英文标题**: 保持`ilike()`因为英文需要大小写不敏感
- **日文标题**: 使用`like()`避免字符问题
- **标签**: 使用`like()`处理中文标签

### 4. 增强的搜索条件
每个字段都使用多种匹配模式：
- 模糊匹配 (`like('%搜索词%')`)
- 精确匹配 (`== '搜索词'`)
- 包含匹配 (`contains('搜索词')`)
- 去空格匹配 (处理空格问题)

## 修复效果

### 搜索"冤罪 1"现在会匹配到：
1. 标题包含"冤罪 1"的动漫 (精确匹配)
2. 标题包含"冤罪1"的动漫 (去空格匹配)
3. 标题中任何位置包含"冤罪"或"1"的动漫 (模糊匹配)
4. 英文标题中包含相关词汇的动漫
5. 标签中包含相关词汇的动漫

### 改进的搜索质量：
- **准确性**: 修复中文字符匹配问题
- **灵活性**: 支持多种搜索模式
- **容错性**: 处理空格、特殊字符等情况
- **兼容性**: 保持英文搜索的大小写不敏感特性

## 技术细节

### 数据库查询优化
- 使用`distinct()`去重避免JOIN产生的重复结果
- 使用`outerjoin()`确保没有标签的动漫也能被搜索到
- 保持相同的搜索逻辑用于计数和结果获取

### 字符编码兼容
- 确保UTF-8编码的正确处理
- 避免字符转换导致的编码问题
- 保持原有的fanart字段序列化逻辑

## 测试建议

建议测试以下搜索词验证修复效果：
- "冤罪 1" (带空格)
- "冤罪" (不带数字)
- "冤罪1" (不带空格)
- 其他中文动漫标题
- 英文标题搜索
- 标签搜索

## 性能考虑

- 多条件OR查询可能略微影响性能
- 已添加数据库索引来优化查询速度
- 使用相同的搜索逻辑确保一致性
- 可考虑将来添加全文索引进一步优化