import requests
from lxml import etree


url = 'https://ikanmh.top/booklist?page=1&end=0'
response = requests.get(url)
response.encoding = 'utf-8'
soup = etree.HTML(response.text)
manga_list = soup.xpath('//div[@class="mh-item"]/a/@href')[::-1]
headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
    'referer':'https://ikanmh.top'
}
global current_chapter_id
for manga in manga_list:
    manga = 'https://ikanmh.top' + manga
    print(manga)