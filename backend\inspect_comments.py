#!/usr/bin/env python3
"""
详细检查评论内容的脚本
"""

import pymysql
import sys
import os
from urllib.parse import urlparse
import json

# 数据库配置
DATABASE_URL = "mysql+pymysql://sql23721_hentai:507877550%40lihao@************:3306/sql23721_hentai?charset=utf8mb4"

def parse_database_url(url):
    """解析数据库URL"""
    if url.startswith('mysql+pymysql://'):
        url = url.replace('mysql+pymysql://', 'mysql://')
    
    parsed = urlparse(url)
    
    return {
        'host': parsed.hostname,
        'port': parsed.port or 3306,
        'user': parsed.username,
        'password': parsed.password.replace('%40', '@') if parsed.password else None,
        'database': parsed.path.lstrip('/').split('?')[0]
    }

def inspect_comments():
    config = parse_database_url(DATABASE_URL)
    
    try:
        connection = pymysql.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password=config['password'],
            database=config['database'],
            charset='utf8mb4',
            connect_timeout=10
        )
        
        print("评论详细检查")
        print("=" * 40)
        
        with connection.cursor() as cursor:
            # 获取anime_id=1的所有评论
            cursor.execute("""
                SELECT c.id, c.user_id, c.anime_id, c.content, c.attachments, 
                       c.is_deleted, c.created_at, u.username 
                FROM comments c 
                LEFT JOIN users u ON c.user_id = u.id 
                WHERE c.anime_id = 1
                ORDER BY c.created_at DESC
            """)
            comments = cursor.fetchall()
            
            print(f"发现 {len(comments)} 条评论 (anime_id=1):\n")
            
            for i, comment in enumerate(comments, 1):
                print(f"评论 #{i}:")
                print(f"  ID: {comment[0]}")
                print(f"  用户ID: {comment[1]} ({comment[7] or 'Unknown'})")
                print(f"  动漫ID: {comment[2]}")
                print(f"  是否删除: {comment[5]}")
                print(f"  创建时间: {comment[6]}")
                
                # 安全处理内容输出
                content = comment[3]
                print(f"  内容长度: {len(content)} 字符")
                
                # 尝试以不同方式输出内容
                print("  内容 (原始):")
                try:
                    # 尝试直接输出
                    if content:
                        for line_num, line in enumerate(content.split('\n'), 1):
                            if line_num <= 3:  # 只显示前3行
                                try:
                                    print(f"    {line_num}: {line}")
                                except UnicodeEncodeError:
                                    print(f"    {line_num}: [编码问题: {len(line)} chars]")
                            elif line_num == 4 and len(content.split('\n')) > 3:
                                print(f"    ... (省略 {len(content.split('\n')) - 3} 行)")
                                break
                    else:
                        print("    [空内容]")
                except Exception as e:
                    print(f"    [显示错误: {e}]")
                
                # 显示字符编码信息
                try:
                    print(f"  内容编码: UTF-8, 字节长度: {len(content.encode('utf-8'))}")
                    # 检查是否包含emoji
                    has_emoji = any(ord(char) > 127 for char in content)
                    print(f"  包含非ASCII字符: {has_emoji}")
                except Exception as e:
                    print(f"  编码检查失败: {e}")
                
                # 附件信息
                attachments = comment[4]
                if attachments:
                    print(f"  附件: {attachments}")
                else:
                    print("  附件: 无")
                
                print("-" * 30)
        
        connection.close()
        
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

def export_comments_to_file():
    """将评论导出到文件"""
    config = parse_database_url(DATABASE_URL)
    
    try:
        connection = pymysql.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password=config['password'],
            database=config['database'],
            charset='utf8mb4',
            connect_timeout=10
        )
        
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT c.id, c.user_id, c.anime_id, c.content, c.attachments, 
                       c.is_deleted, c.created_at, u.username 
                FROM comments c 
                LEFT JOIN users u ON c.user_id = u.id 
                WHERE c.anime_id = 1
                ORDER BY c.created_at DESC
            """)
            comments = cursor.fetchall()
            
            # 导出为JSON格式
            export_data = []
            for comment in comments:
                export_data.append({
                    'id': comment[0],
                    'user_id': comment[1],
                    'anime_id': comment[2],
                    'content': comment[3],
                    'attachments': comment[4],
                    'is_deleted': comment[5],
                    'created_at': str(comment[6]),
                    'username': comment[7]
                })
            
            # 写入文件
            with open('comments_export.json', 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            print(f"评论已导出到: comments_export.json ({len(export_data)} 条记录)")
        
        connection.close()
        
    except Exception as e:
        print(f"导出失败: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == '--export':
        export_comments_to_file()
    else:
        inspect_comments()
        print("\n使用 'python inspect_comments.py --export' 导出评论到JSON文件")