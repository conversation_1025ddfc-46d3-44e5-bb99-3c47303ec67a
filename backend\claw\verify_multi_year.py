#!/usr/bin/env python3
"""
简化的多年份配置验证测试（不依赖完整爬虫）
"""

import yaml
import os

def test_multi_year_config_loading():
    """测试多年份配置加载逻辑"""
    print("Testing multi-year configuration loading...")
    
    # 测试配置
    test_configs = [
        {
            'name': '单年份单月份',
            'config': {
                'crawl': {
                    'date_filter': {
                        'year': 2025,
                        'month': 1
                    }
                }
            },
            'expected_years': [2025],
            'expected_months': [1]
        },
        {
            'name': '多年份多月份',
            'config': {
                'crawl': {
                    'date_filter': {
                        'year': [2023, 2024, 2025],
                        'month': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
                    }
                }
            },
            'expected_years': [2023, 2024, 2025],
            'expected_months': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
        }
    ]
    
    all_passed = True
    
    for test_case in test_configs:
        print(f"\n=== {test_case['name']} ===")
        
        config = test_case['config']
        crawl_config = config.get('crawl', {})
        date_filter = crawl_config.get('date_filter', {})
        
        # 应用多年份逻辑
        year_config = date_filter.get('year', 2025)
        if isinstance(year_config, list):
            target_years = year_config
        else:
            target_years = [year_config]
        
        month_config = date_filter.get('month', 6)
        if isinstance(month_config, list):
            target_months = month_config
        else:
            target_months = [month_config]
        
        # 验证结果
        years_match = target_years == test_case['expected_years']
        months_match = target_months == test_case['expected_months']
        
        print(f"配置: year={year_config}, month={month_config}")
        print(f"解析结果: years={target_years}, months={target_months}")
        print(f"期望结果: years={test_case['expected_years']}, months={test_case['expected_months']}")
        
        if years_match and months_match:
            print("✓ 测试通过")
        else:
            print("✗ 测试失败")
            all_passed = False
    
    return all_passed

def demonstrate_url_generation():
    """演示URL生成逻辑"""
    print("\n" + "="*50)
    print("URL生成演示")
    print("="*50)
    
    # 示例配置
    years = [2024, 2025]
    months = [1, 2, 3]
    
    print(f"年份: {years}")
    print(f"月份: {months}")
    print(f"总组合数: {len(years)} × {len(months)} = {len(years) * len(months)}")
    print("\n生成的URL列表:")
    
    count = 0
    for year in years:
        for month in months:
            count += 1
            url = f"https://hanime1.me/search?query=&type=&genre=%E8%A3%8F%E7%95%AA&sort=&date={year}+%E5%B9%B4+{month}+%E6%9C%88&duration="
            print(f"{count:2d}. {year}年{month:2d}月: {url}")

def update_config_file():
    """更新配置文件示例"""
    print("\n" + "="*50)
    print("配置文件更新示例")
    print("="*50)
    
    config_examples = [
        {
            'description': '爬取2025年全年',
            'config': {
                'year': 2025,
                'month': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
            }
        },
        {
            'description': '爬取2023-2025年夏季',
            'config': {
                'year': [2023, 2024, 2025],
                'month': [6, 7, 8]
            }
        },
        {
            'description': '爬取2020-2025年全部数据',
            'config': {
                'year': [2020, 2021, 2022, 2023, 2024, 2025],
                'month': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
            }
        }
    ]
    
    for example in config_examples:
        print(f"\n{example['description']}:")
        print("```yaml")
        print("crawl:")
        print("  date_filter:")
        print(f"    year: {example['config']['year']}")
        print(f"    month: {example['config']['month']}")
        print("```")
        
        # 计算组合数
        years = example['config']['year']
        months = example['config']['month']
        if not isinstance(years, list):
            years = [years]
        if not isinstance(months, list):
            months = [months]
        
        total_combinations = len(years) * len(months)
        print(f"总组合数: {len(years)} 年 × {len(months)} 月 = {total_combinations} 个")

if __name__ == "__main__":
    print("多年份配置支持验证")
    print("=" * 60)
    
    # 测试配置加载
    success = test_multi_year_config_loading()
    
    # 演示URL生成
    demonstrate_url_generation()
    
    # 显示配置示例
    update_config_file()
    
    if success:
        print("\n🎉 多年份配置支持验证成功！")
        print("\n主要功能:")
        print("✓ 支持单个年份或年份数组")
        print("✓ 支持单个月份或月份数组") 
        print("✓ 自动处理所有年份和月份的组合")
        print("✓ 兼容现有的单年份配置")
        
        print("\n使用方法:")
        print("1. 编辑 config.yml 文件")
        print("2. 修改 date_filter 部分:")
        print("   - year: [2023, 2024, 2025]  # 多年份")
        print("   - month: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]  # 全年")
        print("3. 运行爬虫程序")
    else:
        print("\n❌ 多年份配置验证失败！")