#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试下载页面内容
"""

import sys
import os
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup

def debug_download_page():
    """调试下载页面的实际内容"""
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--remote-debugging-pipe')
    chrome_options.add_argument('--disable-logging')
    chrome_options.add_argument('--log-level=3')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-logging"])
    chrome_options.add_argument(
        '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
        'AppleWebKit/537.36 (KHTML, like Gecko) '
        'Chrome/********* Safari/537.36'
    )
    
    # 测试URL
    download_url = "https://hanime1.me/download?v=25315"
    print(f"调试URL: {download_url}")
    
    service = Service(log_path=os.devnull)
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        driver.get(download_url)
        time.sleep(5)  # 等待页面完全加载
        
        # 获取页面源码
        page_source = driver.page_source
        
        # 保存HTML到文件以便检查
        with open('debug_download_page.html', 'w', encoding='utf-8') as f:
            f.write(page_source)
        
        print("页面源码已保存到 debug_download_page.html")
        
        # 解析页面
        soup = BeautifulSoup(page_source, 'html.parser')
        
        # 查找所有包含data-url的元素
        print("\n=== 查找data-url属性 ===")
        elements_with_data_url = soup.find_all(attrs={"data-url": True})
        print(f"找到 {len(elements_with_data_url)} 个包含data-url的元素")
        
        for i, elem in enumerate(elements_with_data_url):
            data_url = elem.get('data-url')
            elem_text = elem.get_text(strip=True)
            print(f"{i+1}. 标签: {elem.name}")
            print(f"   data-url: {data_url}")
            print(f"   文本内容: {elem_text}")
            print(f"   所有属性: {dict(elem.attrs)}")
            print()
        
        # 查找所有表格行
        print("\n=== 查找表格结构 ===")
        tables = soup.find_all('table')
        print(f"找到 {len(tables)} 个表格")
        
        for i, table in enumerate(tables):
            print(f"\n表格 {i+1}:")
            rows = table.find_all('tr')
            print(f"  行数: {len(rows)}")
            
            for j, row in enumerate(rows):
                row_text = row.get_text(strip=True)
                data_url = row.get('data-url')
                if data_url or '1080' in row_text or '720' in row_text or '480' in row_text:
                    print(f"    行 {j+1}: {row_text}")
                    if data_url:
                        print(f"           data-url: {data_url}")
        
        # 查找下载相关的链接
        print("\n=== 查找下载链接 ===")
        download_links = soup.find_all('a')
        video_links = []
        
        for link in download_links:
            href = link.get('href', '')
            text = link.get_text(strip=True)
            
            if any(ext in href.lower() for ext in ['.mp4', '.mkv', '.avi']) or \
               any(quality in text for quality in ['1080p', '720p', '480p', '360p', '240p']):
                video_links.append({
                    'href': href,
                    'text': text,
                    'attributes': dict(link.attrs)
                })
        
        print(f"找到 {len(video_links)} 个潜在的视频链接")
        for i, link in enumerate(video_links):
            print(f"{i+1}. 链接: {link['href']}")
            print(f"   文本: {link['text']}")
            print(f"   属性: {link['attributes']}")
            print()
        
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_download_page()