from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, desc, asc, func

from app.models import (
    Manga, MangaChapter, MangaPage, MangaReadingProgress, 
    MangaTag, Tag, Category, User, Favorite, Comment
)
from app.schemas.manga import (
    MangaCreate, MangaUpdate, MangaChapterCreate, MangaChapterUpdate,
    MangaPageCreate, MangaReadingProgressCreate, MangaReadingProgressUpdate
)


class MangaCRUD:
    @staticmethod
    def get_manga(db: Session, manga_id: int) -> Optional[Manga]:
        """获取漫画详情，优化版：只加载章节基本信息，不加载所有页面"""
        return db.query(Manga).options(
            joinedload(Manga.chapters),  # 只加载章节，不加载页面
            joinedload(Manga.tags),
            joinedload(Manga.category)
        ).filter(
            Manga.id == manga_id,
            Manga.is_active == True
        ).first()

    @staticmethod
    def get_manga_basic(db: Session, manga_id: int) -> Optional[Manga]:
        """获取漫画基本信息（不包含章节和页面）"""
        return db.query(Manga).options(
            joinedload(Manga.tags),
            joinedload(Manga.category)
        ).filter(
            Manga.id == manga_id,
            Manga.is_active == True
        ).first()

    @staticmethod
    def get_mangas(
        db: Session, 
        skip: int = 0, 
        limit: int = 20,
        category_id: Optional[int] = None,
        region_code: Optional[str] = None,
        manga_type: Optional[str] = None,
        status: Optional[str] = None,
        search: Optional[str] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc"
    ) -> tuple[List[Manga], int]:
        """获取漫画列表，支持筛选、搜索和排序 - 性能优化版"""
        # 构建基础查询条件（用于count和data查询）
        base_filters = [Manga.is_active == True]
        
        if category_id:
            base_filters.append(Manga.category_id == category_id)
        
        if region_code:
            base_filters.append(Manga.region_code == region_code)
            
        if manga_type:
            base_filters.append(Manga.manga_type == manga_type)
            
        if status:
            base_filters.append(Manga.status == status)
            
        if search:
            search_filter = or_(
                Manga.title.contains(search),
                Manga.title_original.contains(search),
                Manga.author.contains(search),
                Manga.artist.contains(search),
                Manga.description.contains(search)
            )
            base_filters.append(search_filter)

        # 高效的count查询（不加载关系）
        total = db.query(func.count(Manga.id)).filter(and_(*base_filters)).scalar()

        # 数据查询（只在需要时加载关系）
        query = db.query(Manga).options(
            joinedload(Manga.category),
            joinedload(Manga.tags)
        ).filter(and_(*base_filters))

        # 排序
        if sort_order == "asc":
            order_func = asc
        else:
            order_func = desc

        if sort_by == "view_count":
            query = query.order_by(order_func(Manga.view_count))
        elif sort_by == "favorite_count":
            query = query.order_by(order_func(Manga.favorite_count))
        elif sort_by == "chapter_count":
            query = query.order_by(order_func(Manga.chapter_count))
        elif sort_by == "release_date":
            query = query.order_by(order_func(Manga.release_date))
        else:  # created_at
            query = query.order_by(order_func(Manga.created_at))

        mangas = query.offset(skip).limit(limit).all()
        
        return mangas, total

    @staticmethod
    def search_mangas_by_tags(db: Session, tag_names: List[str], skip: int = 0, limit: int = 20) -> tuple[List[Manga], int]:
        """根据标签搜索漫画"""
        query = db.query(Manga).join(MangaTag).join(Tag).filter(
            Manga.is_active == True,
            Tag.name.in_(tag_names)
        ).options(
            joinedload(Manga.category),
            joinedload(Manga.tags)
        ).group_by(Manga.id)

        total = query.count()
        mangas = query.offset(skip).limit(limit).all()
        
        return mangas, total

    @staticmethod
    def create_manga(db: Session, manga_data: MangaCreate) -> Manga:
        """创建新漫画"""
        db_manga = Manga(**manga_data.dict())
        db.add(db_manga)
        db.commit()
        db.refresh(db_manga)
        
        return db_manga

    @staticmethod
    def update_manga(db: Session, manga_id: int, manga_update: MangaUpdate) -> Optional[Manga]:
        """更新漫画信息"""
        db_manga = MangaCRUD.get_manga_basic(db, manga_id)
        if not db_manga:
            return None
            
        update_data = manga_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_manga, field, value)
            
        db.commit()
        db.refresh(db_manga)
        
        return db_manga

    @staticmethod
    def delete_manga(db: Session, manga_id: int) -> bool:
        """软删除漫画"""
        db_manga = MangaCRUD.get_manga_basic(db, manga_id)
        if not db_manga:
            return False
            
        db_manga.is_active = False
        db.commit()
        return True

    @staticmethod
    def increment_view_count(db: Session, manga_id: int) -> bool:
        """增加漫画浏览次数 - 优化版：使用原子更新操作"""
        try:
            # 使用原子操作直接更新，避免查询后更新的竞态条件
            result = db.query(Manga).filter(Manga.id == manga_id).update(
                {Manga.view_count: Manga.view_count + 1}
            )
            db.commit()
            return result > 0
        except Exception:
            db.rollback()
            return False

    @staticmethod
    def get_popular_mangas(db: Session, limit: int = 10) -> List[Manga]:
        """获取热门漫画 - 性能优化版：不加载关系数据"""
        return db.query(Manga).filter(
            Manga.is_active == True
        ).order_by(
            desc(Manga.view_count)
        ).limit(limit).all()

    @staticmethod
    def get_recently_updated_mangas(db: Session, limit: int = 10) -> List[Manga]:
        """获取最近更新的漫画"""
        return db.query(Manga).options(
            joinedload(Manga.category),
            joinedload(Manga.tags)
        ).filter(
            Manga.is_active == True
        ).order_by(
            desc(Manga.updated_at)
        ).limit(limit).all()

    @staticmethod
    def get_related_mangas(db: Session, manga_id: int, limit: int = 10) -> List[Manga]:
        """获取相关推荐漫画"""
        # 先获取当前漫画信息
        current_manga = db.query(Manga).filter(
            Manga.id == manga_id,
            Manga.is_active == True
        ).first()
        
        if not current_manga:
            return []
        
        # 构建推荐查询
        query = db.query(Manga).options(
            joinedload(Manga.tags),
            joinedload(Manga.category)
        ).filter(
            Manga.id != manga_id,
            Manga.is_active == True
        )
        
        # 优先推荐相同分类的漫画
        if current_manga.category_id:
            same_category_query = query.filter(Manga.category_id == current_manga.category_id)
            
            # 按标题相似度和浏览量排序
            if current_manga.title:
                # 简单的标题相似度算法：包含相同关键词
                title_keywords = [word for word in current_manga.title if len(word) > 1]
                if title_keywords:
                    similar_title_condition = or_(*[
                        Manga.title.contains(keyword) for keyword in title_keywords[:3]  # 取前3个关键词
                    ])
                    similar_mangas = same_category_query.filter(similar_title_condition).order_by(
                        desc(Manga.view_count)
                    ).limit(limit // 2).all()
                else:
                    similar_mangas = []
            else:
                similar_mangas = []
            
            # 填充其余推荐（相同分类，按热度排序）
            remaining_limit = limit - len(similar_mangas)
            if remaining_limit > 0:
                category_mangas = same_category_query.filter(
                    ~Manga.id.in_([m.id for m in similar_mangas]) if similar_mangas else True
                ).order_by(desc(Manga.view_count)).limit(remaining_limit).all()
                
                related_mangas = similar_mangas + category_mangas
            else:
                related_mangas = similar_mangas[:limit]
        else:
            # 如果没有分类，按热度推荐
            related_mangas = query.order_by(desc(Manga.view_count)).limit(limit).all()
        
        # 如果推荐数量不足，用最热门的漫画填充
        if len(related_mangas) < limit:
            remaining_limit = limit - len(related_mangas)
            excluded_ids = [manga_id] + [m.id for m in related_mangas]
            
            hot_mangas = db.query(Manga).options(
                joinedload(Manga.tags),
                joinedload(Manga.category)
            ).filter(
                ~Manga.id.in_(excluded_ids),
                Manga.is_active == True
            ).order_by(desc(Manga.view_count)).limit(remaining_limit).all()
            
            related_mangas.extend(hot_mangas)
        
        return related_mangas[:limit]


class MangaChapterCRUD:
    @staticmethod
    def get_chapter(db: Session, chapter_id: int) -> Optional[MangaChapter]:
        """获取章节详情，包含页面信息"""
        return db.query(MangaChapter).options(
            joinedload(MangaChapter.pages),
            joinedload(MangaChapter.manga)
        ).filter(MangaChapter.id == chapter_id).first()

    @staticmethod
    def get_chapter_by_number(db: Session, manga_id: int, chapter_number: float) -> Optional[MangaChapter]:
        """通过漫画ID和章节号获取章节详情"""
        return db.query(MangaChapter).options(
            joinedload(MangaChapter.pages),
            joinedload(MangaChapter.manga)
        ).filter(
            MangaChapter.manga_id == manga_id,
            MangaChapter.chapter_number == chapter_number,
            MangaChapter.status == "published"
        ).first()

    @staticmethod
    def get_chapters_by_manga(db: Session, manga_id: int) -> List[MangaChapter]:
        """获取漫画的所有章节"""
        return db.query(MangaChapter).options(
            joinedload(MangaChapter.pages)
        ).filter(
            MangaChapter.manga_id == manga_id,
            MangaChapter.status == "published"
        ).order_by(MangaChapter.chapter_number).all()

    @staticmethod
    def create_chapter(db: Session, chapter_data: MangaChapterCreate) -> MangaChapter:
        """创建新章节"""
        db_chapter = MangaChapter(**chapter_data.dict())
        db.add(db_chapter)
        db.commit()
        db.refresh(db_chapter)
        
        # 更新漫画章节数
        db.query(Manga).filter(Manga.id == chapter_data.manga_id).update({
            "chapter_count": Manga.chapter_count + 1
        })
        db.commit()
        
        return db_chapter

    @staticmethod
    def update_chapter(db: Session, chapter_id: int, chapter_update: MangaChapterUpdate) -> Optional[MangaChapter]:
        """更新章节信息"""
        db_chapter = db.query(MangaChapter).filter(MangaChapter.id == chapter_id).first()
        if not db_chapter:
            return None
            
        update_data = chapter_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_chapter, field, value)
            
        db.commit()
        db.refresh(db_chapter)
        return db_chapter

    @staticmethod
    def delete_chapter(db: Session, chapter_id: int) -> bool:
        """删除章节及其页面"""
        db_chapter = db.query(MangaChapter).filter(MangaChapter.id == chapter_id).first()
        if not db_chapter:
            return False
            
        manga_id = db_chapter.manga_id
        
        # 删除章节及其相关页面
        db.delete(db_chapter)
        
        # 更新漫画章节数
        remaining_chapters = db.query(func.count(MangaChapter.id)).filter(
            MangaChapter.manga_id == manga_id
        ).scalar()
        
        db.query(Manga).filter(Manga.id == manga_id).update({
            "chapter_count": remaining_chapters
        })
        
        db.commit()
        return True


class MangaPageCRUD:
    @staticmethod
    def get_pages_by_chapter(db: Session, chapter_id: int) -> List[MangaPage]:
        """获取章节的所有页面"""
        return db.query(MangaPage).filter(
            MangaPage.chapter_id == chapter_id
        ).order_by(MangaPage.page_number).all()
    
    @staticmethod
    def get_pages_by_chapter_paginated(db: Session, chapter_id: int, skip: int = 0, limit: int = 50) -> List[MangaPage]:
        """分页获取章节页面 - 性能优化版"""
        return db.query(MangaPage).filter(
            MangaPage.chapter_id == chapter_id
        ).order_by(MangaPage.page_number).offset(skip).limit(limit).all()

    @staticmethod
    def create_page(db: Session, page_data: MangaPageCreate) -> MangaPage:
        """创建新页面"""
        db_page = MangaPage(**page_data.dict())
        db.add(db_page)
        db.commit()
        db.refresh(db_page)
        
        # 更新章节页面数
        page_count = db.query(func.count(MangaPage.id)).filter(
            MangaPage.chapter_id == page_data.chapter_id
        ).scalar()
        
        db.query(MangaChapter).filter(MangaChapter.id == page_data.chapter_id).update({
            "page_count": page_count
        })
        db.commit()
        
        return db_page

    @staticmethod
    def bulk_create_pages(db: Session, pages_data: List[MangaPageCreate]) -> List[MangaPage]:
        """批量创建页面"""
        if not pages_data:
            return []
            
        db_pages = [MangaPage(**page.dict()) for page in pages_data]
        db.add_all(db_pages)
        db.commit()
        
        # 更新章节页面数
        chapter_id = pages_data[0].chapter_id
        page_count = db.query(func.count(MangaPage.id)).filter(
            MangaPage.chapter_id == chapter_id
        ).scalar()
        
        db.query(MangaChapter).filter(MangaChapter.id == chapter_id).update({
            "page_count": page_count
        })
        db.commit()
        
        for db_page in db_pages:
            db.refresh(db_page)
            
        return db_pages


class MangaReadingProgressCRUD:
    @staticmethod
    def get_user_reading_progress(db: Session, user_id: int, manga_id: int) -> Optional[MangaReadingProgress]:
        """获取用户对特定漫画的阅读进度"""
        return db.query(MangaReadingProgress).options(
            joinedload(MangaReadingProgress.manga),
            joinedload(MangaReadingProgress.chapter)
        ).filter(
            MangaReadingProgress.user_id == user_id,
            MangaReadingProgress.manga_id == manga_id
        ).first()

    @staticmethod
    def get_user_reading_history(db: Session, user_id: int, skip: int = 0, limit: int = 20) -> tuple[List[MangaReadingProgress], int]:
        """获取用户阅读历史"""
        query = db.query(MangaReadingProgress).options(
            joinedload(MangaReadingProgress.manga).joinedload(Manga.category),
            joinedload(MangaReadingProgress.chapter)
        ).filter(MangaReadingProgress.user_id == user_id).order_by(
            desc(MangaReadingProgress.last_read_at)
        )
        
        total = query.count()
        progress_list = query.offset(skip).limit(limit).all()
        
        return progress_list, total

    @staticmethod
    def update_reading_progress(db: Session, user_id: int, progress_data: MangaReadingProgressCreate) -> MangaReadingProgress:
        """更新或创建阅读进度"""
        db_progress = db.query(MangaReadingProgress).filter(
            MangaReadingProgress.user_id == user_id,
            MangaReadingProgress.manga_id == progress_data.manga_id
        ).first()
        
        if db_progress:
            # 更新现有进度
            db_progress.chapter_id = progress_data.chapter_id
            db_progress.page_number = progress_data.page_number
            db_progress.total_pages = progress_data.total_pages
            db_progress.progress_percentage = progress_data.progress_percentage
            db_progress.last_read_at = func.now()
        else:
            # 创建新进度记录
            db_progress = MangaReadingProgress(
                user_id=user_id,
                **progress_data.dict()
            )
            db.add(db_progress)
            
        db.commit()
        db.refresh(db_progress)
        return db_progress

    @staticmethod
    def delete_reading_progress(db: Session, user_id: int, manga_id: int) -> bool:
        """删除阅读进度"""
        db_progress = db.query(MangaReadingProgress).filter(
            MangaReadingProgress.user_id == user_id,
            MangaReadingProgress.manga_id == manga_id
        ).first()
        
        if not db_progress:
            return False
            
        db.delete(db_progress)
        db.commit()
        return True