'use client';

import { useEffect, useRef, useState } from 'react';

interface TurnstileProps {
  siteKey: string;
  onVerify: (token: string) => void;
  onError?: (error: string) => void;
  onExpire?: () => void;
  theme?: 'light' | 'dark' | 'auto';
  size?: 'normal' | 'compact';
  className?: string;
}

declare global {
  interface Window {
    turnstile: {
      render: (element: HTMLElement | string, options: any) => string;
      reset: (widgetId?: string) => void;
      remove: (widgetId?: string) => void;
      getResponse: (widgetId?: string) => string;
    };
    onloadTurnstileCallback: () => void;
  }
}

export function Turnstile({
  siteKey,
  onVerify,
  onError,
  onExpire,
  theme = 'auto',
  size = 'normal',
  className = '',
}: TurnstileProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const widgetIdRef = useRef<string | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);

  // 检查脚本是否已加载
  useEffect(() => {
    if (window.turnstile) {
      setIsScriptLoaded(true);
      return;
    }

    // 检查是否已有脚本标签
    const existingScript = document.querySelector('script[src*="challenges.cloudflare.com"]');
    if (existingScript) {
      // 脚本已存在，等待加载完成
      const checkLoaded = () => {
        if (window.turnstile) {
          setIsScriptLoaded(true);
        } else {
          setTimeout(checkLoaded, 100);
        }
      };
      checkLoaded();
      return;
    }

    // 加载Turnstile脚本
    const script = document.createElement('script');
    script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
    script.async = true;
    script.defer = true;
    
    script.onload = () => {
      setIsScriptLoaded(true);
    };
    
    script.onerror = () => {
      console.error('Failed to load Turnstile script');
      onError?.('Failed to load Turnstile script');
    };

    document.head.appendChild(script);

    return () => {
      // 清理脚本（如果需要）
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, [onError]);

  // 渲染Turnstile组件
  useEffect(() => {
    if (!isScriptLoaded || !containerRef.current || isLoaded) {
      return;
    }

    try {
      const widgetId = window.turnstile.render(containerRef.current, {
        sitekey: siteKey,
        theme,
        size,
        callback: (token: string) => {
          onVerify(token);
        },
        'error-callback': (error: string) => {
          console.error('Turnstile error:', error);
          onError?.(error);
        },
        'expired-callback': () => {
          onExpire?.();
        },
      });

      widgetIdRef.current = widgetId;
      setIsLoaded(true);
    } catch (error) {
      console.error('Error rendering Turnstile:', error);
      onError?.('Error rendering Turnstile widget');
    }
  }, [isScriptLoaded, siteKey, theme, size, onVerify, onError, onExpire, isLoaded]);

  // 清理
  useEffect(() => {
    return () => {
      if (widgetIdRef.current && window.turnstile) {
        try {
          window.turnstile.remove(widgetIdRef.current);
        } catch (error) {
          console.error('Error removing Turnstile widget:', error);
        }
      }
    };
  }, []);

  // 重置方法
  const reset = () => {
    if (widgetIdRef.current && window.turnstile) {
      try {
        window.turnstile.reset(widgetIdRef.current);
      } catch (error) {
        console.error('Error resetting Turnstile widget:', error);
      }
    }
  };

  // 获取响应
  const getResponse = () => {
    if (widgetIdRef.current && window.turnstile) {
      try {
        return window.turnstile.getResponse(widgetIdRef.current);
      } catch (error) {
        console.error('Error getting Turnstile response:', error);
        return '';
      }
    }
    return '';
  };

  // 暴露方法给父组件
  useEffect(() => {
    if (containerRef.current) {
      (containerRef.current as any).reset = reset;
      (containerRef.current as any).getResponse = getResponse;
    }
  }, [isLoaded]);

  if (!siteKey) {
    return null;
  }

  return (
    <div className={className}>
      <div ref={containerRef} />
      {!isScriptLoaded && (
        <div className="flex items-center justify-center p-4 text-sm text-muted-foreground">
          正在加载验证组件...
        </div>
      )}
    </div>
  );
}

// Hook for easier usage
export function useTurnstile() {
  const [token, setToken] = useState<string>('');
  const [isVerified, setIsVerified] = useState(false);
  const [error, setError] = useState<string>('');

  const handleVerify = (token: string) => {
    setToken(token);
    setIsVerified(true);
    setError('');
  };

  const handleError = (error: string) => {
    setToken('');
    setIsVerified(false);
    setError(error);
  };

  const handleExpire = () => {
    setToken('');
    setIsVerified(false);
    setError('');
  };

  const reset = () => {
    setToken('');
    setIsVerified(false);
    setError('');
  };

  return {
    token,
    isVerified,
    error,
    handleVerify,
    handleError,
    handleExpire,
    reset,
  };
}
