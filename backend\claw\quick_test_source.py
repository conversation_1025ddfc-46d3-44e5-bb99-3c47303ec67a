#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试 production_crawler.py 的视频源获取功能
只测试视频源提取，不实际下载
"""

import sys
import os
import tempfile
import shutil
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from production_crawler import ProductionCrawler
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from bs4 import BeautifulSoup
import re
import html

def quick_test_video_source():
    """快速测试视频源提取功能"""
    print("=" * 60)
    print("快速测试视频源提取功能")
    print("=" * 60)
    
    # 创建临时测试目录
    temp_dir = tempfile.mkdtemp(prefix="quick_test_")
    
    try:
        # 创建最小配置
        test_config = {
            'crawl': {'quality_priority': ['1080', '720', '480']},
            'download': {'download_dir': temp_dir},
            'database': {
                'host': '************:3306',
                'user': 'sql23721_hentai', 
                'password': '507877550@lihao',
                'database': 'sql23721_hentai'
            }
        }
        
        import yaml
        config_file = os.path.join(temp_dir, "quick_config.yml")
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(test_config, f)
        
        # 创建爬虫实例
        crawler = ProductionCrawler(config_file)
        crawler.initialize()
        
        # 测试视频源提取逻辑（模拟download_video的核心部分）
        test_url = "https://hanime1.me/watch?v=24949"
        print(f"测试视频: {test_url}")
        
        # 直接使用Selenium获取页面内容
        service = Service(log_path=os.devnull)
        driver = webdriver.Chrome(service=service, options=crawler.crawler.chrome_options)
        
        try:
            driver.get(test_url)
            import time
            time.sleep(3)
            soup = BeautifulSoup(driver.page_source, 'html.parser')
            
            # 测试所有视频源提取方法
            src = None
            
            # 方法1: 按质量优先级查找source标签
            print("\n1. 测试source标签方法...")
            for q in crawler.crawler.QUALITY_PRIORITY:
                tag = soup.find('source', {'size': q})
                if tag and tag.get('src'):
                    src = tag['src']
                    print(f"   找到 {q}p source标签")
                    break
            
            # 方法2: 查找所有source标签
            if not src:
                print("\n2. 测试所有source标签...")
                all_sources = soup.find_all('source')
                print(f"   找到 {len(all_sources)} 个source标签")
                
                for source_tag in all_sources:
                    if source_tag.get('src'):
                        src_url = source_tag.get('src')
                        size_attr = source_tag.get('size', 'unknown')
                        print(f"   发现: {size_attr}p")
                        if not src:
                            src = src_url
            
            # 方法3: 正则匹配
            if not src:
                print("\n3. 测试正则匹配...")
                video_url_patterns = [
                    r'"(https://[^"]*\.mp4[^"]*)"',
                    r"'(https://[^']*\.mp4[^']*)'",
                ]
                
                page_source = driver.page_source
                for pattern in video_url_patterns:
                    matches_urls = re.findall(pattern, page_source)
                    if matches_urls:
                        src = matches_urls[0]
                        print(f"   正则匹配成功")
                        break
            
            # 方法4: 下载页面回退
            if not src:
                print("\n4. 测试下载页面回退...")
                src = crawler.crawler.try_download_page(test_url, driver)
                if src:
                    print(f"   下载页面回退成功")
            
            if src:
                # 修复HTML实体编码
                original_src = src
                src = html.unescape(src)
                
                print(f"\n[SUCCESS] 找到视频源:")
                print(f"   原始URL: {original_src[:100]}...")
                print(f"   解码后: {src[:100]}...")
                
                # 测试URL可访问性
                print(f"\n5. 测试URL可访问性...")
                import requests
                try:
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Referer': test_url,
                        'Accept': 'video/mp4,video/*;q=0.9,*/*;q=0.8'
                    }
                    response = requests.head(src, headers=headers, timeout=10)
                    print(f"   状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        content_length = response.headers.get('content-length', '未知')
                        content_type = response.headers.get('content-type', '未知')
                        print(f"   [SUCCESS] URL可访问")
                        print(f"   文件大小: {content_length} bytes")
                        print(f"   内容类型: {content_type}")
                        return True
                    else:
                        print(f"   [WARNING] 状态码不是200")
                        return False
                        
                except Exception as e:
                    print(f"   [ERROR] 访问失败: {e}")
                    return False
            else:
                print(f"\n[FAIL] 未找到视频源")
                return False
                
        finally:
            driver.quit()
    
    except Exception as e:
        print(f"[ERROR] 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)

if __name__ == "__main__":
    success = quick_test_video_source()
    print(f"\n{'='*60}")
    if success:
        print("[SUCCESS] 快速测试通过!")
        print("production_crawler.py 视频源提取功能正常")
    else:
        print("[FAIL] 快速测试失败!")
    print("="*60)