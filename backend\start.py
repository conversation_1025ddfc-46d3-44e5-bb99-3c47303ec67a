#!/usr/bin/env python3
"""
动漫平台后端启动脚本
增强版 - 包含环境检测、配置验证和数据库健康检查
"""
import sys
import os
from datetime import datetime
import uvicorn
from sqlalchemy import text


def print_colored(text, color="white"):
    """彩色控制台输出（兼容Windows）"""
    # Windows环境下可能不支持ANSI颜色，降级为普通输出
    try:
        colors = {
            "red": "\033[91m",
            "green": "\033[92m", 
            "yellow": "\033[93m",
            "blue": "\033[94m",
            "purple": "\033[95m",
            "cyan": "\033[96m",
            "white": "\033[97m",
            "reset": "\033[0m"
        }
        # 尝试彩色输出
        print(f"{colors.get(color, colors['white'])}{text}{colors['reset']}")
    except UnicodeEncodeError:
        # 如果编码失败，使用简化版本（去掉emoji）
        clean_text = text.encode('ascii', 'ignore').decode('ascii')
        print(clean_text)
    except Exception:
        # 最后的回退：纯文本输出
        print(text)


def print_banner():
    """打印启动横幅"""
    banner = """
================================================================
                    动漫平台后端服务                      
                   Anime Platform Backend                  
================================================================
    """
    print_colored(banner, "cyan")


def check_database_connection():
    """检查数据库连接健康状态"""
    try:
        from app.core.database import engine
        print_colored("[INFO] 检查数据库连接...", "yellow")
        
        with engine.connect() as conn:
            # 执行简单的健康检查查询  
            result = conn.execute(text("SELECT VERSION() as version, NOW() as server_time")).fetchone()
            db_version = result.version if result else "Unknown"
            current_time = result.server_time if result else "Unknown"
            
            print_colored("[SUCCESS] 数据库连接成功!", "green")
            print_colored(f"   数据库版本: {db_version}", "white")
            print_colored(f"   服务器时间: {current_time}", "white")
            return True
            
    except Exception as e:
        print_colored("[ERROR] 数据库连接失败!", "red")
        print_colored(f"   错误信息: {str(e)}", "red")
        print_colored("   请检查数据库配置和网络连接", "yellow")
        return False


def display_config_info():
    """显示当前配置信息"""
    try:
        from app.core.config import settings
        
        print_colored("[CONFIG] 当前配置信息:", "blue")
        print_colored("-" * 50, "blue")
        
        # 环境信息
        env_color = "green" if settings.is_production else "yellow"
        env_status = "生产环境" if settings.is_production else "开发环境"
        print_colored(f"[ENV] 运行环境: {env_status} ({settings.ENVIRONMENT})", env_color)
        
        # DEBUG状态
        debug_color = "red" if settings.DEBUG else "green"
        debug_status = "开启 (将输出详细SQL日志)" if settings.DEBUG else "关闭 (已优化日志输出)"
        print_colored(f"[DEBUG] DEBUG模式: {debug_status}", debug_color)
        
        # 数据库配置
        print_colored(f"[DATABASE] 数据库配置:", "white")
        print_colored(f"   连接池大小: {settings.DB_POOL_SIZE}", "white")
        print_colored(f"   最大溢出连接: {settings.DB_MAX_OVERFLOW}", "white")
        print_colored(f"   连接回收时间: {settings.DB_POOL_RECYCLE}秒", "white")
        print_colored(f"   连接健康检查: {'开启' if settings.DB_POOL_PRE_PING else '关闭'}", "white")
        
        # 性能提示
        if settings.is_production:
            print_colored("[PERFORMANCE] 生产环境性能优化:", "green")
            print_colored("   [OK] SQL日志已关闭，大幅减少日志输出", "green")
            print_colored("   [OK] 连接池已优化，支持高并发访问", "green")
            print_colored("   [OK] 连接复用已启用，降低数据库开销", "green")
        else:
            print_colored("[DEVELOPMENT] 开发环境调试信息:", "yellow")
            print_colored("   [WARN] SQL日志已开启，便于调试", "yellow")
            print_colored("   [WARN] 自动重载已启用，代码变更后自动重启", "yellow")
        
        print_colored("-" * 50, "blue")
        return True
        
    except Exception as e:
        print_colored("[ERROR] 配置加载失败!", "red")
        print_colored(f"   错误信息: {str(e)}", "red")
        return False


def get_startup_params():
    """根据环境获取启动参数"""
    try:
        from app.core.config import settings
        
        # 基础参数
        params = {
            "app": "app.main:app",
            "host": "0.0.0.0",
            "port": 8000,
        }
        
        # 根据环境调整参数
        if settings.is_production:
            # 生产环境参数
            params.update({
                "reload": False,              # 关闭自动重载
                "workers": 1,                # 单进程（可根据需要调整）
                "log_level": "info",         # 日志级别
                "access_log": True,          # 访问日志
            })
            print_colored("[CONFIG] 生产环境启动参数已优化", "green")
        else:
            # 开发环境参数  
            params.update({
                "reload": True,              # 开启自动重载
                "reload_dirs": ["app"],      # 监控目录
                "log_level": "debug",        # 详细日志
                "access_log": True,          # 访问日志
            })
            print_colored("[CONFIG] 开发环境调试参数已启用", "yellow")
        
        return params
        
    except Exception as e:
        print_colored("[ERROR] 参数配置失败，使用默认配置", "red")
        print_colored(f"   错误信息: {str(e)}", "red")
        return {
            "app": "app.main:app",
            "host": "0.0.0.0", 
            "port": 8000,
            "reload": True
        }


def main():
    """主启动函数"""
    # 打印启动横幅
    print_banner()
    
    # 显示启动时间
    start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print_colored(f"[TIME] 启动时间: {start_time}", "white")
    print()
    
    # 显示配置信息
    if not display_config_info():
        print_colored("[WARN] 配置验证失败，但将尝试继续启动...", "yellow")
    print()
    
    # 检查数据库连接
    if not check_database_connection():
        print_colored("[WARN] 数据库连接检查失败!", "red")
        print_colored("   服务器将启动，但数据库功能可能不可用", "yellow")
        
        # 询问是否继续启动
        try:
            response = input("\n是否继续启动服务器? (y/N): ").strip().lower()
            if response not in ['y', 'yes', '是']:
                print_colored("[STOP] 启动已取消", "red")
                sys.exit(1)
        except KeyboardInterrupt:
            print_colored("\n[STOP] 启动已取消", "red")
            sys.exit(1)
    print()
    
    # 获取启动参数
    params = get_startup_params()
    
    # 显示访问信息
    print_colored("[SERVER] 服务访问地址:", "green")
    print_colored(f"   API 服务: http://localhost:{params['port']}", "green")
    print_colored(f"   API 文档: http://localhost:{params['port']}/docs", "green")
    print_colored(f"   API 基址: http://localhost:{params['port']}/api/v1", "green")
    print()
    
    print_colored("[START] 服务器启动中...", "green")
    print_colored("   按 Ctrl+C 停止服务", "yellow")
    print_colored("=" * 60, "cyan")
    
    try:
        # 启动服务器
        uvicorn.run(**params)
        
    except KeyboardInterrupt:
        print_colored("\n[STOP] 服务器已停止", "yellow")
        print_colored("[BYE] 感谢使用动漫平台后端服务！", "green")
        sys.exit(0)
        
    except Exception as e:
        print_colored(f"\n[ERROR] 服务器启动失败: {str(e)}", "red")
        sys.exit(1)


if __name__ == "__main__":
    main()