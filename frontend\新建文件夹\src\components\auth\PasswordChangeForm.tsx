import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Key, Eye, EyeOff, RefreshCw } from 'lucide-react'
import { apiClient } from '@/lib/api'

interface PasswordForm {
  old_password: string
  new_password: string
  confirm_password: string
}

interface ShowPasswords {
  old: boolean
  new: boolean
  confirm: boolean
}

interface ToastMessage {
  message: string
  type: 'success' | 'error' | 'info'
}

interface PasswordChangeFormProps {
  passwordForm: PasswordForm
  setPasswordForm: (form: PasswordForm) => void
  showPasswords: ShowPasswords
  setShowPasswords: (show: ShowPasswords) => void
  passwordLoading: boolean
  setToastMessage: (toast: ToastMessage) => void
}

const PasswordChangeForm: React.FC<PasswordChangeFormProps> = ({
  passwordForm,
  setPasswordForm,
  showPasswords,
  setShowPasswords,
  passwordLoading,
  setToastMessage,
}) => {
  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault()

    // Client-side validation
    if (!passwordForm.old_password || !passwordForm.new_password || !passwordForm.confirm_password) {
      setToastMessage({ message: '请填写所有密码字段', type: 'error' })
      return
    }

    if (passwordForm.new_password.length < 6) {
      setToastMessage({ message: '新密码至少需要6个字符', type: 'error' })
      return
    }

    if (!/^(?=.*[A-Za-z])(?=.*\d)/.test(passwordForm.new_password)) {
      setToastMessage({ message: '密码必须包含至少一个字母和一个数字', type: 'error' })
      return
    }

    if (passwordForm.new_password !== passwordForm.confirm_password) {
      setToastMessage({ message: '新密码和确认密码不匹配', type: 'error' })
      return
    }

    try {
      await apiClient.changePassword(passwordForm)
      setPasswordForm({
        old_password: '',
        new_password: '',
        confirm_password: '',
      })
      setToastMessage({ message: '密码修改成功', type: 'success' })
    } catch (error) {
      console.error('Failed to change password:', error)
      const errorMessage = error instanceof Error ? error.message : '修改密码失败，请重试'
      setToastMessage({ message: errorMessage, type: 'error' })
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Key className="h-5 w-5" />
          修改密码
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handlePasswordChange} className="space-y-4">
          <div className="space-y-4">
            <div>
              <Label htmlFor="old_password">当前密码</Label>
              <div className="relative">
                <Input
                  id="old_password"
                  type={showPasswords.old ? 'text' : 'password'}
                  value={passwordForm.old_password}
                  onChange={(e) =>
                    setPasswordForm({ ...passwordForm, old_password: e.target.value })
                  }
                  placeholder="请输入当前密码"
                  disabled={passwordLoading}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPasswords({ ...showPasswords, old: !showPasswords.old })}
                >
                  {showPasswords.old ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div>
              <Label htmlFor="new_password">新密码</Label>
              <div className="relative">
                <Input
                  id="new_password"
                  type={showPasswords.new ? 'text' : 'password'}
                  value={passwordForm.new_password}
                  onChange={(e) =>
                    setPasswordForm({ ...passwordForm, new_password: e.target.value })
                  }
                  placeholder="请输入新密码（至少6位，包含字母和数字）"
                  disabled={passwordLoading}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPasswords({ ...showPasswords, new: !showPasswords.new })}
                >
                  {showPasswords.new ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                密码必须至少包含6个字符，包括至少一个字母和一个数字
              </p>
            </div>

            <div>
              <Label htmlFor="confirm_password">确认新密码</Label>
              <div className="relative">
                <Input
                  id="confirm_password"
                  type={showPasswords.confirm ? 'text' : 'password'}
                  value={passwordForm.confirm_password}
                  onChange={(e) =>
                    setPasswordForm({ ...passwordForm, confirm_password: e.target.value })
                  }
                  placeholder="请再次输入新密码"
                  disabled={passwordLoading}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() =>
                    setShowPasswords({ ...showPasswords, confirm: !showPasswords.confirm })
                  }
                >
                  {showPasswords.confirm ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </div>

          <Button type="submit" disabled={passwordLoading}>
            {passwordLoading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                修改中...
              </>
            ) : (
              <>
                <Key className="h-4 w-4 mr-2" />
                修改密码
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}

export default PasswordChangeForm