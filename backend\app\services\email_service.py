"""
邮件服务模块
基于Python emails库实现邮件发送功能
"""
import emails
from emails.template import JinjaTemplate as T
from typing import Optional, Dict, Any, List, Union
import logging
from sqlalchemy.orm import Session

from app.core.config_manager import ConfigManager


logger = logging.getLogger(__name__)


class EmailService:
    """邮件服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.config_manager = ConfigManager(db)
    
    def get_smtp_settings(self) -> Dict[str, Any]:
        """获取SMTP配置"""
        smtp_config = self.config_manager.get_smtp_config()
        
        # 构建emails库需要的SMTP配置格式
        smtp_settings = {
            "host": smtp_config["host"],
            "port": smtp_config["port"],
            "user": smtp_config["username"],
            "password": smtp_config["password"]
        }
        
        # 添加加密设置
        if smtp_config["use_ssl"]:
            smtp_settings["ssl"] = True
        elif smtp_config["use_tls"]:
            smtp_settings["tls"] = True
        
        return smtp_settings
    
    def create_message(
        self,
        to: Union[str, tuple, List[str]],
        subject: str,
        html_content: Optional[str] = None,
        text_content: Optional[str] = None,
        template_data: Optional[Dict[str, Any]] = None,
        attachments: Optional[List[Dict[str, Any]]] = None
    ) -> emails.Message:
        """创建邮件消息对象"""
        
        smtp_config = self.config_manager.get_smtp_config()
        
        # 处理发件人信息
        from_name = smtp_config["from_name"] or "系统邮件"
        from_address = smtp_config["from_address"]
        mail_from = (from_name, from_address) if from_address else from_address
        
        # 处理模板
        if template_data:
            if html_content:
                html_content = T(html_content)
            if text_content:
                text_content = T(text_content)
            subject = T(subject)
        
        # 创建消息
        message = emails.Message(
            html=html_content,
            text=text_content,
            subject=subject,
            mail_from=mail_from
        )
        
        # 添加附件
        if attachments:
            for attachment in attachments:
                message.attach(**attachment)
        
        return message
    
    def send_email(
        self,
        to: Union[str, tuple, List[str]],
        subject: str,
        html_content: Optional[str] = None,
        text_content: Optional[str] = None,
        template_data: Optional[Dict[str, Any]] = None,
        attachments: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """发送邮件"""
        
        try:
            # 获取SMTP配置
            smtp_settings = self.get_smtp_settings()
            
            # 检查必要配置
            if not smtp_settings["host"] or not smtp_settings["user"]:
                return {
                    "success": False,
                    "error": "SMTP配置不完整，请检查邮件服务器设置",
                    "status_code": None
                }
            
            # 创建消息
            message = self.create_message(
                to=to,
                subject=subject,
                html_content=html_content,
                text_content=text_content,
                template_data=template_data,
                attachments=attachments
            )
            
            # 发送邮件
            response = message.send(
                to=to,
                render=template_data or {},
                smtp=smtp_settings
            )
            
            # 检查发送结果
            success = response.status_code == 250
            
            result = {
                "success": success,
                "status_code": response.status_code,
                "response": str(response)
            }
            
            if not success:
                result["error"] = f"邮件发送失败，状态码: {response.status_code}"
                logger.error(f"Email send failed: {response}")
            else:
                logger.info(f"Email sent successfully to {to}")
            
            return result
            
        except Exception as e:
            error_msg = f"邮件发送异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "status_code": None
            }
    
    def test_smtp_connection(self) -> Dict[str, Any]:
        """测试SMTP连接"""
        try:
            smtp_settings = self.get_smtp_settings()
            
            # 检查必要配置
            if not smtp_settings["host"]:
                return {
                    "success": False,
                    "error": "SMTP服务器地址未配置"
                }
            
            if not smtp_settings["user"]:
                return {
                    "success": False,
                    "error": "SMTP用户名未配置"
                }
            
            # 创建测试消息
            test_message = emails.Message(
                html="<p>这是一封测试邮件，如果您收到此邮件说明SMTP配置正确。</p>",
                text="这是一封测试邮件，如果您收到此邮件说明SMTP配置正确。",
                subject="SMTP配置测试",
                mail_from=("系统测试", smtp_settings["user"])
            )
            
            # 尝试连接但不发送
            # 注意：emails库的连接测试功能有限，这里主要验证配置格式
            if smtp_settings["host"] and smtp_settings["port"]:
                return {
                    "success": True,
                    "message": "SMTP配置格式正确，建议发送测试邮件验证连接"
                }
            else:
                return {
                    "success": False,
                    "error": "SMTP配置不完整"
                }
                
        except Exception as e:
            logger.error(f"SMTP connection test failed: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": f"SMTP连接测试失败: {str(e)}"
            }
    
    def send_test_email(self, test_email: str) -> Dict[str, Any]:
        """发送测试邮件"""
        return self.send_email(
            to=test_email,
            subject="邮件配置测试",
            html_content="""
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #333;">邮件配置测试</h2>
                <p>您好！</p>
                <p>如果您收到这封邮件，说明您的邮件服务器配置正确，系统可以正常发送邮件。</p>
                <p>测试时间: <strong>{{ test_time }}</strong></p>
                <hr style="margin: 20px 0; border: none; border-top: 1px solid #eee;">
                <p style="color: #666; font-size: 12px;">
                    此邮件由系统自动发送，请勿回复。
                </p>
            </div>
            """,
            text_content="""
            邮件配置测试
            
            您好！
            
            如果您收到这封邮件，说明您的邮件服务器配置正确，系统可以正常发送邮件。
            
            测试时间: {{ test_time }}
            
            ---
            此邮件由系统自动发送，请勿回复。
            """,
            template_data={
                "test_time": __import__('datetime').datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        )
    
    def send_welcome_email(self, user_email: str, username: str) -> Dict[str, Any]:
        """发送欢迎邮件"""
        site_name = self.config_manager.get_config("site_name", "动漫网站")
        
        return self.send_email(
            to=user_email,
            subject=f"欢迎加入{site_name}",
            html_content="""
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #333;">欢迎加入{{ site_name }}</h2>
                <p>亲爱的 <strong>{{ username }}</strong>，</p>
                <p>欢迎您注册我们的网站！我们很高兴您加入我们的社区。</p>
                <p>在这里您可以：</p>
                <ul>
                    <li>观看最新的动漫内容</li>
                    <li>收藏您喜欢的动漫</li>
                    <li>与其他动漫爱好者交流</li>
                </ul>
                <p>如果您有任何问题，请随时联系我们。</p>
                <hr style="margin: 20px 0; border: none; border-top: 1px solid #eee;">
                <p style="color: #666; font-size: 12px;">
                    此邮件由{{ site_name }}自动发送。
                </p>
            </div>
            """,
            text_content="""
            欢迎加入{{ site_name }}
            
            亲爱的 {{ username }}，
            
            欢迎您注册我们的网站！我们很高兴您加入我们的社区。
            
            在这里您可以：
            - 观看最新的动漫内容
            - 收藏您喜欢的动漫
            - 与其他动漫爱好者交流
            
            如果您有任何问题，请随时联系我们。
            
            ---
            此邮件由{{ site_name }}自动发送。
            """,
            template_data={
                "site_name": site_name,
                "username": username
            }
        )
    
    def send_password_reset_email(self, user_email: str, username: str, reset_link: str) -> Dict[str, Any]:
        """发送密码重置邮件"""
        site_name = self.config_manager.get_config("site_name", "动漫网站")
        
        return self.send_email(
            to=user_email,
            subject=f"{site_name} - 密码重置",
            html_content="""
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #333;">密码重置</h2>
                <p>亲爱的 <strong>{{ username }}</strong>，</p>
                <p>您请求重置{{ site_name }}的密码。</p>
                <p>请点击下面的链接重置您的密码：</p>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{{ reset_link }}" 
                       style="background-color: #007cba; color: white; padding: 12px 30px; 
                              text-decoration: none; border-radius: 5px; display: inline-block;">
                        重置密码
                    </a>
                </div>
                <p style="color: #666;">如果按钮无法点击，请复制以下链接到浏览器：</p>
                <p style="word-break: break-all; color: #007cba;">{{ reset_link }}</p>
                <p style="color: #ff6b6b;"><strong>注意：</strong>此链接将在24小时后失效。</p>
                <p>如果您没有请求重置密码，请忽略此邮件。</p>
                <hr style="margin: 20px 0; border: none; border-top: 1px solid #eee;">
                <p style="color: #666; font-size: 12px;">
                    此邮件由{{ site_name }}自动发送，请勿回复。
                </p>
            </div>
            """,
            text_content="""
            密码重置
            
            亲爱的 {{ username }}，
            
            您请求重置{{ site_name }}的密码。
            
            请访问以下链接重置您的密码：
            {{ reset_link }}
            
            注意：此链接将在24小时后失效。
            
            如果您没有请求重置密码，请忽略此邮件。
            
            ---
            此邮件由{{ site_name }}自动发送，请勿回复。
            """,
            template_data={
                "site_name": site_name,
                "username": username,
                "reset_link": reset_link
            }
        )