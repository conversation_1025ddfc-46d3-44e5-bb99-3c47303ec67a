#!/usr/bin/env python3
"""
数据库清理脚本 - 清空所有表数据并重置自增ID
⚠️  警告：此脚本将删除所有数据，请谨慎使用！
"""
from sqlalchemy import create_engine, text
from sqlalchemy.orm import Session
from app.core.config import settings
from app.core.database import SessionLocal
from app.models import Base, User, Anime, Category, Tag, AnimeTag, Favorite, Comment
import sys

def confirm_cleanup():
    """确认用户真的要清理数据库"""
    print("⚠️  警告：此操作将删除数据库中的所有数据！")
    print("📋 将要清理的表：")
    print("  - users (用户)")
    print("  - animes (动漫)")
    print("  - categories (分类)")
    print("  - tags (标签)")
    print("  - anime_tags (动漫-标签关联)")
    print("  - favorites (收藏)")
    print("  - comments (评论)")
    print("  - alembic_version (迁移版本)")
    print()
    
    response = input("您确定要继续吗？请输入 'YES' 来确认（区分大小写）: ")
    return response == "YES"

def cleanup_all_tables():
    """清理所有表数据并重置自增ID"""
    db = SessionLocal()
    try:
        print("🗑️  开始清理数据库...")
        
        # 禁用外键检查（MySQL/MariaDB）
        db.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
        
        # 定义表清理顺序（考虑外键约束）
        tables_to_clean = [
            ("comments", "评论"),
            ("favorites", "收藏"),
            ("anime_tags", "动漫-标签关联"),
            ("animes", "动漫"),
            ("categories", "分类"),
            ("tags", "标签"),
            ("users", "用户"),
            ("alembic_version", "迁移版本")
        ]
        
        # 清理每个表
        for table_name, table_desc in tables_to_clean:
            try:
                # 删除所有数据
                result = db.execute(text(f"DELETE FROM {table_name}"))
                deleted_count = result.rowcount
                
                # 重置自增ID（除了alembic_version表）
                if table_name != "alembic_version":
                    db.execute(text(f"ALTER TABLE {table_name} AUTO_INCREMENT = 1"))
                
                print(f"  ✅ {table_desc} ({table_name}): 删除了 {deleted_count} 条记录，自增ID已重置")
                
            except Exception as e:
                print(f"  ❌ 清理 {table_desc} ({table_name}) 失败: {e}")
        
        # 重新启用外键检查
        db.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
        
        # 提交所有更改
        db.commit()
        print("\n✅ 数据库清理完成！所有表数据已删除，自增ID已重置为1")
        
        # 验证清理结果
        print("\n📊 验证清理结果：")
        for table_name, table_desc in tables_to_clean:
            if table_name != "alembic_version":  # 跳过alembic_version表
                result = db.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                count = result.scalar()
                print(f"  - {table_desc}: {count} 条记录")
        
    except Exception as e:
        print(f"❌ 清理过程中发生错误: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def check_database_connection():
    """检查数据库连接"""
    try:
        engine = create_engine(settings.DATABASE_URL)
        with engine.connect() as connection:
            result = connection.execute(text("SELECT VERSION()"))
            version = result.fetchone()[0]
            print(f"✅ 数据库连接成功")
            print(f"🗄️  数据库版本: {version}")
            
            # 检查当前数据库
            result = connection.execute(text("SELECT DATABASE()"))
            db_name = result.fetchone()[0]
            print(f"📊 当前数据库: {db_name}")
            return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def show_current_data_count():
    """显示当前数据统计"""
    db = SessionLocal()
    try:
        print("\n📈 当前数据统计：")
        
        tables_info = [
            (User, "users", "用户"),
            (Anime, "animes", "动漫"),
            (Category, "categories", "分类"),
            (Tag, "tags", "标签"),
            (AnimeTag, "anime_tags", "动漫-标签关联"),
            (Favorite, "favorites", "收藏"),
            (Comment, "comments", "评论")
        ]
        
        total_records = 0
        for model, table_name, table_desc in tables_info:
            count = db.query(model).count()
            total_records += count
            print(f"  - {table_desc}: {count} 条记录")
        
        print(f"  📊 总计: {total_records} 条记录")
        return total_records
        
    except Exception as e:
        print(f"❌ 获取数据统计失败: {e}")
        return 0
    finally:
        db.close()

def main():
    """主函数"""
    print("🧹 数据库清理工具")
    print("="*50)
    
    # 检查数据库连接
    if not check_database_connection():
        print("❌ 无法连接到数据库，请检查配置")
        sys.exit(1)
    
    # 显示当前数据统计
    current_count = show_current_data_count()
    
    if current_count == 0:
        print("\n💡 数据库已经是空的，无需清理")
        sys.exit(0)
    
    print("\n" + "="*50)
    
    # 确认清理操作
    if not confirm_cleanup():
        print("❌ 操作已取消")
        sys.exit(0)
    
    try:
        # 执行清理
        cleanup_all_tables()
        
        print("\n🎉 数据库清理成功完成！")
        print("💡 提示：")
        print("  - 所有表数据已清空")
        print("  - 自增ID已重置为1")
        print("  - 可以重新导入数据或创建新数据")
        
    except Exception as e:
        print(f"\n❌ 清理失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
