/**
 * 页面持久化工具 - 防止刷新时跳转
 */

const STORAGE_KEY = 'page_persistence';

interface PageState {
  path: string;
  timestamp: number;
  scrollPosition: number;
  formData?: Record<string, any>;
}

/**
 * 保存当前页面状态
 */
export function savePageState(
  path: string, 
  additionalData?: Record<string, any>
): void {
  if (typeof window === 'undefined') return;
  
  try {
    const state: PageState = {
      path,
      timestamp: Date.now(),
      scrollPosition: window.scrollY,
      formData: additionalData
    };
    
    sessionStorage.setItem(STORAGE_KEY, JSON.stringify(state));
  } catch (error) {
    console.error('Failed to save page state:', error);
  }
}

/**
 * 获取保存的页面状态
 */
export function getPageState(): PageState | null {
  if (typeof window === 'undefined') return null;
  
  try {
    const stored = sessionStorage.getItem(STORAGE_KEY);
    if (!stored) return null;
    
    const state: PageState = JSON.parse(stored);
    
    // 检查状态是否过期（30分钟）
    const maxAge = 30 * 60 * 1000; // 30分钟
    if (Date.now() - state.timestamp > maxAge) {
      clearPageState();
      return null;
    }
    
    return state;
  } catch (error) {
    console.error('Failed to get page state:', error);
    return null;
  }
}

/**
 * 清除页面状态
 */
export function clearPageState(): void {
  if (typeof window === 'undefined') return;
  
  try {
    sessionStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.error('Failed to clear page state:', error);
  }
}

/**
 * 检查是否应该恢复页面状态
 */
export function shouldRestorePageState(currentPath: string): boolean {
  const state = getPageState();
  if (!state) return false;
  
  // 检查路径是否匹配
  return state.path === currentPath;
}

/**
 * 恢复滚动位置
 */
export function restoreScrollPosition(): void {
  const state = getPageState();
  if (!state) return;
  
  // 延迟恢复滚动位置，确保页面已渲染
  setTimeout(() => {
    window.scrollTo(0, state.scrollPosition);
  }, 100);
}

/**
 * 页面可见性变化处理
 */
export function handleVisibilityChange(): void {
  if (typeof window === 'undefined') return;
  
  if (document.visibilityState === 'hidden') {
    // 页面隐藏时保存状态
    savePageState(window.location.pathname);
  }
}

/**
 * 页面卸载前保存状态
 */
export function handleBeforeUnload(): void {
  if (typeof window === 'undefined') return;
  
  savePageState(window.location.pathname);
}

/**
 * 初始化页面持久化
 */
export function initPagePersistence(): void {
  if (typeof window === 'undefined') return;
  
  // 监听页面可见性变化
  document.addEventListener('visibilitychange', handleVisibilityChange);
  
  // 监听页面卸载
  window.addEventListener('beforeunload', handleBeforeUnload);
  
  // 监听路由变化（Next.js）
  const handleRouteChange = () => {
    savePageState(window.location.pathname);
  };
  
  // 如果有Next.js路由器，监听路由变化
  if (typeof window !== 'undefined' && (window as any).next) {
    (window as any).next.router?.events?.on('routeChangeStart', handleRouteChange);
  }
}

/**
 * 清理页面持久化监听器
 */
export function cleanupPagePersistence(): void {
  if (typeof window === 'undefined') return;
  
  document.removeEventListener('visibilitychange', handleVisibilityChange);
  window.removeEventListener('beforeunload', handleBeforeUnload);
  
  // 清理Next.js路由监听器
  if (typeof window !== 'undefined' && (window as any).next) {
    (window as any).next.router?.events?.off('routeChangeStart', handleBeforeUnload);
  }
}

/**
 * React Hook for page persistence
 */
export function usePagePersistence(path?: string) {
  if (typeof window === 'undefined') {
    return {
      saveState: () => {},
      restoreState: () => {},
      clearState: () => {},
    };
  }
  
  const currentPath = path || window.location.pathname;
  
  const saveState = (data?: Record<string, any>) => {
    savePageState(currentPath, data);
  };
  
  const restoreState = () => {
    if (shouldRestorePageState(currentPath)) {
      restoreScrollPosition();
      return getPageState()?.formData;
    }
    return null;
  };
  
  const clearState = () => {
    clearPageState();
  };
  
  return {
    saveState,
    restoreState,
    clearState,
  };
}
