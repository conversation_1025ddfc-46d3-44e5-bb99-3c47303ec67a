'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/contexts/AuthContext';
import { User, LogOut, Heart, Search, Settings, Moon, Sun, BookOpen } from 'lucide-react';

export const Navbar = () => {
  const { user, logout, isAuthenticated, isAdmin } = useAuth();
  const router = useRouter();
  const [theme, setTheme] = useState<'light' | 'dark'>(() => (typeof window !== 'undefined' && document.documentElement.classList.contains('dark') ? 'dark' : 'light'));
  const [query, setQuery] = useState('');

  const handleLogout = () => {
    logout();
    router.push('/');
  };

  // 初始化主题
  useEffect(() => {
    if (typeof window === 'undefined') return;
    const saved = localStorage.getItem('theme');
    if (saved === 'dark') {
      document.documentElement.classList.add('dark');
      setTheme('dark');
    } else if (saved === 'light') {
      document.documentElement.classList.remove('dark');
      setTheme('light');
    }
  }, []);

  const toggleTheme = () => {
    if (theme === 'dark') {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
      setTheme('light');
    } else {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
      setTheme('dark');
    }
  };

  const submitSearch = (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    const trimmed = query.trim();
    if (!trimmed) return;
    router.push(`/search?search=${encodeURIComponent(trimmed)}`);
  };

  return (
    <nav className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center h-16 gap-4">
          {/* 左：Logo + 菜单 */}
          <div className="flex items-center gap-6 flex-1">
            <Link href="/" className="flex items-center gap-2">
              <div className="w-8 h-8 bg-primary rounded-md flex items-center justify-center">
                <span className="text-primary-foreground font-bold">H</span>
              </div>
              <span className="font-bold text-xl">xsl.org</span>
            </Link>
            <nav className="hidden md:flex items-center gap-6">
              <Link href="/" className="text-foreground/80 hover:text-foreground transition-colors">首页</Link>
              <Link href="/rifan" className="text-foreground/80 hover:text-foreground transition-colors">里番</Link>
              <Link href="/manga" className="text-foreground/80 hover:text-foreground transition-colors">漫画</Link>
              {isAuthenticated && (
                <>
                  <Link href="/profile" className="text-foreground/80 hover:text-foreground transition-colors">个人中心</Link>
                </>
              )}
              {isAdmin && (
                <Link href="/admin" className="text-foreground/80 hover:text-foreground transition-colors">管理</Link>
              )}
            </nav>
          </div>

          {/* 右：搜索 + 主题切换 + 用户 */}
          <div className="flex items-center gap-2">
            <form onSubmit={submitSearch} className="hidden md:flex items-center gap-2">
              <Input
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="搜索..."
                className="w-56"
              />
              <Button type="submit" variant="ghost" size="icon" title="搜索">
                <Search className="h-4 w-4" />
              </Button>
            </form>

            <Button variant="ghost" size="icon" onClick={toggleTheme} title="切换主题">
              {theme === 'dark' ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
            </Button>

            {isAuthenticated ? (
              <div className="flex items-center gap-2">
                <span className="text-sm text-foreground/80 hidden sm:inline">
                  {user?.username}
                  {isAdmin && <span className="text-primary ml-1">(管理员)</span>}
                </span>
                <Button variant="ghost" size="icon" onClick={handleLogout} title="退出登录">
                  <LogOut className="h-4 w-4" />
                </Button>
              </div>
            ) : (
              <Link href="/auth">
                <Button variant="default">
                  <User className="h-4 w-4 mr-2" />登录
                </Button>
              </Link>
            )}
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden pb-4">
          <form onSubmit={submitSearch} className="flex gap-2 mb-2">
            <Input value={query} onChange={(e) => setQuery(e.target.value)} placeholder="搜索..." />
            <Button type="submit" size="sm"><Search className="h-4 w-4" /></Button>
          </form>
          <div className="flex flex-wrap gap-2">
            <Link href="/"><Button variant="ghost" size="sm">首页</Button></Link>
            <Link href="/rifan"><Button variant="ghost" size="sm">里番</Button></Link>
            <Link href="/manga"><Button variant="ghost" size="sm"><BookOpen className="h-4 w-4 mr-1" />漫画</Button></Link>
            {isAuthenticated && (
              <>
                <Link href="/profile"><Button variant="ghost" size="sm"><User className="h-4 w-4 mr-1" />个人中心</Button></Link>
              </>
            )}
            {isAdmin && (
              <Link href="/admin"><Button variant="ghost" size="sm"><Settings className="h-4 w-4 mr-1" />管理</Button></Link>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};