#!/bin/bash
# 生产环境一键部署脚本

echo "🚀 开始部署动漫网站到生产环境..."

# 检查Node.js版本
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 请先安装Node.js"
    exit 1
fi

echo "✅ Node.js版本: $(node --version)"

# 进入前端目录
cd frontend || { echo "❌ 前端目录不存在"; exit 1; }

# 安装依赖
echo "📦 安装前端依赖..."
npm ci

# 构建生产版本
echo "🔨 构建生产版本..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ 前端构建成功!"
else
    echo "❌ 前端构建失败!"
    exit 1
fi

# 启动生产服务器
echo "🌟 启动生产服务器..."
echo "📍 前端服务将运行在: http://localhost:3000"
echo "🛑 按Ctrl+C停止服务"

npm run start