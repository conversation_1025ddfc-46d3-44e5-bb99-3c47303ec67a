import React from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { UserX, AlertCircle, AlertTriangle, RefreshCw } from 'lucide-react'
import { apiClient } from '@/lib/api'
import type { User } from '@/lib/api'

interface DeletionForm {
  username_confirmation: string
}

interface ToastMessage {
  message: string
  type: 'success' | 'error' | 'info'
}

interface AccountDeletionFlowProps {
  user: User | null
  deletionForm: DeletionForm
  setDeletionForm: (form: DeletionForm) => void
  deletionStep: number
  setDeletionStep: (step: number) => void
  deletionLoading: boolean
  setToastMessage: (toast: ToastMessage) => void
}

const AccountDeletionFlow: React.FC<AccountDeletionFlowProps> = ({
  user,
  deletionForm,
  setDeletionForm,
  deletionStep,
  setDeletionStep,
  deletionLoading,
  setToastMessage,
}) => {
  const router = useRouter()

  const handleDeletionStepOne = () => {
    if (!deletionForm.username_confirmation.trim()) {
      setToastMessage({ message: '请输入用户名确认', type: 'error' })
      return
    }

    if (deletionForm.username_confirmation.trim() !== user?.username) {
      setToastMessage({ message: '用户名确认不匹配', type: 'error' })
      return
    }

    setDeletionStep(1)
  }

  const handleDeletionStepTwo = () => {
    setDeletionStep(2)
  }

  const handleAccountDeletion = async () => {
    try {
      await apiClient.deleteAccount(deletionForm.username_confirmation)
      setToastMessage({ message: '账户删除成功，正在跳转...', type: 'success' })

      // Clear local storage and redirect after a short delay
      setTimeout(() => {
        localStorage.removeItem('access_token')
        router.push('/auth')
      }, 2000)
    } catch (error) {
      console.error('Failed to delete account:', error)
      const errorMessage = error instanceof Error ? error.message : '删除账户失败，请重试'
      setToastMessage({ message: errorMessage, type: 'error' })
      resetDeletionFlow()
    }
  }

  const resetDeletionFlow = () => {
    setDeletionStep(0)
    setDeletionForm({ username_confirmation: '' })
  }

  return (
    <Card className="border-red-200 dark:border-red-800">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-600 dark:text-red-400">
          <UserX className="h-5 w-5" />
          删除账户
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
            <div className="space-y-2">
              <h4 className="font-medium text-red-700 dark:text-red-300">
                危险操作 - 此操作不可逆
              </h4>
              <p className="text-sm text-red-600 dark:text-red-400">删除账户将永久删除您的：</p>
              <ul className="text-sm text-red-600 dark:text-red-400 list-disc list-inside space-y-1">
                <li>个人信息和头像</li>
                <li>所有收藏记录</li>
                <li>阅读历史和进度</li>
                <li>评论和互动记录</li>
                <li>所有相关数据</li>
              </ul>
            </div>
          </div>
        </div>

        {deletionStep === 0 && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="username_confirmation">
                确认删除，请输入您的用户名：<strong>{user?.username}</strong>
              </Label>
              <Input
                id="username_confirmation"
                value={deletionForm.username_confirmation}
                onChange={(e) => setDeletionForm({ username_confirmation: e.target.value })}
                placeholder={`请输入 "${user?.username}" 以确认删除`}
                disabled={deletionLoading}
              />
            </div>

            <Button
              variant="destructive"
              onClick={handleDeletionStepOne}
              disabled={
                deletionLoading || deletionForm.username_confirmation.trim() !== user?.username
              }
            >
              <UserX className="h-4 w-4 mr-2" />
              继续删除账户
            </Button>
          </div>
        )}

        {deletionStep === 1 && (
          <div className="space-y-4">
            <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
                <div>
                  <h4 className="font-medium text-yellow-700 dark:text-yellow-300">第二次确认</h4>
                  <p className="text-sm text-yellow-600 dark:text-yellow-400">
                    您确定要删除账户 <strong>{user?.username}</strong>{' '}
                    吗？这将删除所有相关数据，且无法恢复。
                  </p>
                </div>
              </div>
            </div>

            <div className="flex gap-3">
              <Button variant="outline" onClick={resetDeletionFlow}>
                取消
              </Button>
              <Button variant="destructive" onClick={handleDeletionStepTwo}>
                <UserX className="h-4 w-4 mr-2" />
                我确定要删除
              </Button>
            </div>
          </div>
        )}

        {deletionStep === 2 && (
          <div className="space-y-4">
            <div className="p-4 bg-red-100 dark:bg-red-900/30 border-2 border-red-300 dark:border-red-700 rounded-lg">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-red-800 dark:text-red-200">最终确认</h4>
                  <p className="text-sm text-red-700 dark:text-red-300">
                    这是最后一次确认。点击下方按钮将立即删除您的账户，此操作
                    <strong>无法撤销</strong>。
                  </p>
                </div>
              </div>
            </div>

            <div className="flex gap-3">
              <Button variant="outline" onClick={resetDeletionFlow}>
                取消删除
              </Button>
              <Button variant="destructive" onClick={handleAccountDeletion} disabled={deletionLoading}>
                {deletionLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    删除中...
                  </>
                ) : (
                  <>
                    <UserX className="h-4 w-4 mr-2" />
                    永久删除账户
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default AccountDeletionFlow