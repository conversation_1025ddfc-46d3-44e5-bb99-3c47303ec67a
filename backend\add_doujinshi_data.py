#!/usr/bin/env python3
"""
添加更多同人志测试数据
将一些现有的漫画转换为doujinshi类型
"""

import sys
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.models import Manga, MangaType

def add_doujinshi_data():
    """将一些现有漫画转换为同人志类型"""
    engine = create_engine(
        settings.DATABASE_URL,
        pool_pre_ping=True,
        echo=False
    )
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # 获取一些现有的serial类型漫画，转换为doujinshi
        print("=== 查找适合转换为同人志的漫画 ===")
        
        # 选择一些看起来像同人志的漫画（通常标题较短，或者包含特定关键词）
        candidate_mangas = session.query(Manga).filter(
            Manga.manga_type == 'serial'
        ).limit(20).all()
        
        # 手动选择一些适合的漫画转换为同人志
        doujinshi_candidates = []
        for manga in candidate_mangas:
            # 选择一些标题较短或看起来像同人志的作品
            if (len(manga.title) < 15 or 
                any(keyword in manga.title for keyword in ['秘密', '調教', '愛愛', '人妻', '辣妹', '情侶']) or
                manga.id % 5 == 0):  # 每5个选一个
                doujinshi_candidates.append(manga)
                if len(doujinshi_candidates) >= 10:  # 最多转换10个
                    break
        
        print(f"找到 {len(doujinshi_candidates)} 个候选漫画")
        
        # 转换为doujinshi类型
        converted_count = 0
        for manga in doujinshi_candidates:
            print(f"转换漫画: ID={manga.id}, 标题={manga.title}")
            manga.manga_type = MangaType.DOUJINSHI
            converted_count += 1
        
        # 提交更改
        session.commit()
        print(f"\n成功转换了 {converted_count} 个漫画为同人志类型")
        
        # 验证结果
        print("\n=== 验证转换结果 ===")
        doujinshi_count = session.query(Manga).filter(Manga.manga_type == 'doujinshi').count()
        print(f"现在总共有 {doujinshi_count} 个同人志类型的漫画")
        
        # 显示所有同人志
        print("\n所有同人志漫画:")
        doujinshi_mangas = session.query(Manga).filter(Manga.manga_type == 'doujinshi').all()
        for manga in doujinshi_mangas:
            print(f"ID: {manga.id}, 标题: {manga.title}")
            
    except Exception as e:
        print(f"操作出错: {e}")
        session.rollback()
    finally:
        session.close()

if __name__ == "__main__":
    add_doujinshi_data()
