"""add_search_indexes_for_enhanced_search

Revision ID: d937dce08feb
Revises: 387c1abccb19
Create Date: 2025-08-18 21:42:55.312356

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd937dce08feb'
down_revision: Union[str, None] = '387c1abccb19'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema - Add indexes for enhanced search functionality."""
    # Add index for title_english on animes table (for subtitle search)
    op.create_index('idx_animes_title_english', 'animes', ['title_english'])
    
    # Add index for title_japanese on animes table (for Japanese title search)
    op.create_index('idx_animes_title_japanese', 'animes', ['title_japanese'])
    
    # Add index for name_english on tags table (for English tag search)
    op.create_index('idx_tags_name_english', 'tags', ['name_english'])


def downgrade() -> None:
    """Downgrade schema - Remove search indexes."""
    # Remove indexes in reverse order
    op.drop_index('idx_tags_name_english', 'tags')
    op.drop_index('idx_animes_title_japanese', 'animes')
    op.drop_index('idx_animes_title_english', 'animes')
