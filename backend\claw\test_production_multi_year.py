#!/usr/bin/env python3
"""
测试生产环境多年份配置
"""

import sys
import yaml
import os
sys.path.append('.')

def test_production_config():
    """测试生产环境配置文件"""
    print("Testing production configuration...")
    
    config_file = 'production_config.yml'
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    try:
        # 加载配置
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查配置结构
        crawl_config = config.get('crawl', {})
        date_filter = crawl_config.get('date_filter', {})
        
        year_config = date_filter.get('year')
        month_config = date_filter.get('month')
        
        print(f"✅ 配置文件加载成功")
        print(f"当前年份配置: {year_config}")
        print(f"当前月份配置: {month_config}")
        
        # 应用多年份逻辑
        if isinstance(year_config, list):
            target_years = year_config
        else:
            target_years = [year_config]
        
        if isinstance(month_config, list):
            target_months = month_config
        else:
            target_months = [month_config]
        
        print(f"解析后年份: {target_years}")
        print(f"解析后月份: {target_months}")
        print(f"总组合数: {len(target_years)} × {len(target_months)} = {len(target_years) * len(target_months)}")
        
        # 显示示例URL
        print("\n示例爬取URL:")
        count = 0
        for year in target_years[:2]:  # 只显示前2年
            for month in target_months[:3]:  # 只显示前3个月
                count += 1
                url = f"https://hanime1.me/search?query=&type=&genre=%E8%A3%8F%E7%95%AA&sort=&date={year}+%E5%B9%B4+{month}+%E6%9C%88&duration="
                print(f"  {count}. {year}年{month:02d}月: {url}")
                if count >= 6:  # 限制显示数量
                    break
            if count >= 6:
                break
        
        if len(target_years) * len(target_months) > 6:
            print(f"  ... (总共 {len(target_years) * len(target_months)} 个组合)")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def demonstrate_multi_year_configs():
    """演示多年份配置示例"""
    print("\n" + "="*60)
    print("多年份配置示例")
    print("="*60)
    
    examples = [
        {
            'name': '爬取2003年全年',
            'config': {
                'year': 2003,
                'month': [1,2,3,4,5,6,7,8,9,10,11,12]
            }
        },
        {
            'name': '爬取2003-2005年前6个月',
            'config': {
                'year': [2003, 2004, 2005],
                'month': [1,2,3,4,5,6]
            }
        },
        {
            'name': '爬取2000-2010年全部数据（大范围）',
            'config': {
                'year': [2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010],
                'month': [1,2,3,4,5,6,7,8,9,10,11,12]
            }
        }
    ]
    
    for example in examples:
        print(f"\n{example['name']}:")
        print("```yaml")
        print("crawl:")
        print("  date_filter:")
        print(f"    year: {example['config']['year']}")
        print(f"    month: {example['config']['month']}")
        print("```")
        
        # 计算组合数
        years = example['config']['year']
        months = example['config']['month']
        if not isinstance(years, list):
            years = [years]
        if not isinstance(months, list):
            months = [months]
        
        total = len(years) * len(months)
        print(f"组合数: {len(years)} 年 × {len(months)} 月 = {total} 个")

def check_production_crawler():
    """检查生产环境爬虫"""
    print("\n" + "="*60)
    print("检查生产环境爬虫支持")
    print("="*60)
    
    if not os.path.exists('production_crawler.py'):
        print("❌ production_crawler.py 文件不存在")
        return False
    
    # 简单检查文件内容
    with open('production_crawler.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否包含多年份支持
    if 'TARGET_YEARS' in content:
        print("✅ production_crawler.py 支持多年份")
        if 'total_combinations' in content:
            print("✅ 包含进度跟踪功能")
        return True
    else:
        print("❌ production_crawler.py 缺少多年份支持")
        return False

if __name__ == "__main__":
    print("生产环境多年份配置测试")
    print("="*60)
    
    # 测试配置文件
    config_ok = test_production_config()
    
    # 演示配置示例
    demonstrate_multi_year_configs()
    
    # 检查爬虫文件
    crawler_ok = check_production_crawler()
    
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    
    if config_ok and crawler_ok:
        print("✅ 生产环境多年份配置完全就绪！")
        print("\n使用方法:")
        print("1. 编辑 production_config.yml")
        print("2. 修改 date_filter 部分:")
        print("   year: [2003, 2004, 2005]  # 多年份")
        print("   month: [1,2,3,4,5,6,7,8,9,10,11,12]  # 全年")
        print("3. 运行: python production_crawler.py")
        print("\n⚠️ 建议先用小范围测试，如1-2个月")
    else:
        print("❌ 生产环境配置存在问题")
        if not config_ok:
            print("- 配置文件问题")
        if not crawler_ok:
            print("- 爬虫文件问题")