import requests
import re
import os
from bs4 import BeautifulSoup
from lxml import etree
import html
import mysql.connector
import time
from datetime import datetime
current_chapter_id = 1
def main():
    response = requests.get(url)
    response.encoding = 'utf-8'
    soup = etree.HTML(response.text)
    manga_list = soup.xpath('//div[@class="mh-item"]/a/@href')[::-1]
    proxy = {
        'http': '************************************************',
        'https': '************************************************'
    }
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'referer':'https://mxs13.cc'
    }
    global current_chapter_id
    for manga in manga_list:
        manga = 'https://mxs13.cc' + manga
        response = requests.get(manga)
        response.encoding = 'utf-8'
        soup = etree.HTML(response.text)
        title1 = soup.xpath("//h1/text()")[0]
        title2 = soup.xpath("//div[@class='info']/p[@class='subtitle'][1]/text()")[0]
        title2 = title2.split('：')[-1].strip()
        title1 = title1.replace('&','&amp;')
        artists_elements = soup.xpath("//div[@class='info']/p[@class='subtitle'][2]/text()")
        if artists_elements:
            artists = artists_elements[0].split('：')[-1].strip().replace('freexcomic.com', 'hanime.men').replace('FreeXcomic', 'Hanime')
        else:
            artists = 'admin'
        cover = soup.xpath("//div[@class='banner_border_bg']/img/@src")[0]
        manga_id = cover.split('/')[-2]
        save_path = f"manhua/hanman/{manga_id}/"
        filename = cover.split('/')[-1]
        full_path = os.path.join(save_path, filename)
        pics = 'https://media.hanime.men/' + full_path
        conections = ''
        conections = soup.xpath("//p[@class='content']/span/span/text()")
        if conections and conections[0].strip():
            conections = conections[0].strip()
        else:
            conections = title1
        if repeat(title1):
            print(f"漫画 '{title1}' 已存在，跳过发布。但继续检查章节。")
        else:
            if not os.path.exists(save_path):
                os.makedirs(save_path)
            response = make_request_with_retry(cover, headers=headers,proxies=proxy,)
            if response.status_code == 200:
                with open(full_path, 'wb') as f:
                    f.write(response.content)
                print("图片下载成功，保存至：", full_path)
            else:
                print("图片下载失败，状态码：", response.status_code)
            post_manga(title1, conections, title2, pics, artists)
        current_chapter_id = 1
        chapters = soup.xpath("//div[@id='chapterlistload']/ul/li/a/@href")
        for chapter in chapters:
            chapter_url = 'https://mxs13.cc' + chapter
            chapter_response = requests.get(chapter_url)
            chapter_response.encoding = 'utf-8'
            chapter_soup = etree.HTML(chapter_response.text)
            chapter_title = chapter_soup.xpath("//h1/text()")[0]
            wordpress_title = chapter_soup.xpath("//h1/text()")[0].replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;').replace('"', '&quot;')
            ID = current_chapter_id
            current_chapter_id += 1
            if repeat_chapter(wordpress_title):
                print(f"文章 '{chapter_title}' 已存在，跳过发布。")
            else:
                chapter_content = chapter_soup.xpath("//div[@class='comicpage']/div/img/@data-original")
                images_html = ""
                for image in chapter_content:
                    name_first = image.split('/')[-3]
                    name_second = ID
                    if "static.hentai18t.com" in image:
                        path = "images/"
                    elif "mxs13.cc" in image:
                        path = f"manhua/hanman/{name_first}/{name_second}/"
                    else:
                        continue  # 未知的URL，跳过处理
                    # 从URL获取图片名称
                    filename = image.split('/')[-1]
                    path = f"manhua/hanman/{name_first}/{name_second}/"
                    # 创建完整的文件路径
                    full_path = os.path.join(path, filename)
                    # 确保目录存在
                    os.makedirs(path, exist_ok=True)
                    response = make_request_with_retry(image, headers=headers,proxies=proxy,)
                    # 检查是否存在响应对象
                    if response is None:
                        print("未收到有效响应，跳过当前采集")
                        continue  # 跳过当前循环，继续下一个采集
                    if response.status_code == 200:
                        with open(full_path, 'wb') as f:
                            f.write(response.content)
                        print("图片下载成功，保存至：", full_path)
                    else:
                        print("图片下载失败，状态码：", response.status_code)
                    chapter_html = 'https://media.hanime.men/' + full_path
                    images_html += f'<img src="{chapter_html}" alt="{wordpress_title}">' + "\n"
                    time.sleep(1)
                post_chapter(wordpress_title,chapter_title, title1, ID, images_html)


def repeat(title1):
    db_config = {
        "host": "**************",
        "user": "hanime_men",
        "password": "bWcjxkNYDrsDN3Df",
        "database": "hanime_men"
    }
    try:
        # 连接数据库
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()

        # 执行精确匹配查询
        cursor.execute("SELECT ID FROM wp_posts WHERE post_title = %s AND post_status = 'publish'", (title1,))
        result = cursor.fetchone()

        # 确保读取所有结果
        while cursor.fetchone() is not None:
            pass

        # 检查是否找到文章
        return result is not None
    except mysql.connector.Error as error:
        print(f"数据库操作发生错误：{error}")
        return False
    finally:
        # 关闭游标和连接
        if cursor is not None:
            cursor.close()
        if conn is not None:
            conn.close()


def extract_info(tags):
    # 初始化存储结果的列表
    artists = 'admin'  # 默认值为'admin'
    tags = []
    categories = []

    # 遍历URL列表
    for tag in tags:
        if '/artist/' in tag:
            # 有artist时，提取artist信息，并更新默认值
            artist_name = tag.split('/artist/')[1]
            artists = artist_name  # 更新artists为具体内容
        elif '/tag/' in tag:
            # 提取tag信息
            tag_name = tag.split('/tag/')[1]
            tags.append(tag_name)
        elif '/category/' in tag:
            # 提取category信息
            category_name = tag.split('/category/')[1]
            categories.append(category_name)

    # 将列表转换为字符串形式
    tags_str = ', '.join(tags)
    categories_str = ', '.join(categories)

    # 构造并返回结果字符串
    return {"artists": artists, "tags_str": tags_str, "categories_str": categories_str}


def post_manga(title1, conections, title2, pics, artists):
    query = "http://hanime.men/Locoy.php?action=save&secret=123123"
    data_form = {
        "post_title": title1,
        "post_content": conections,
        "post_excerpt": conections,
        "tag": "",
        "post_category": "",
        "fifu_input_url": pics,
        "post_meta[ero_hot]": "0",
        "post_meta[ero_project]": "0",
        "post_meta[ero_japanese]": title2,
        "post_type": "manga",
        "post_meta[ero_artist]": artists,
        "post_meta[ero_author]": artists,
        "post_meta[ero_status]": "Ongoing",
        "post_meta[ero_type]": "Manhua",
        "post_author": "admin",
        "post_meta[ero_score]": "7.00",
    }
    response = requests.post(query, data=data_form)
    config = {
        "host": "**************",
        "user": "hanime_men",
        "password": "bWcjxkNYDrsDN3Df",
        "database": "hanime_men"
    }
    # 连接到数据库
    try:
        mydb = mysql.connector.connect(**config)
        # 创建一个cursor对象，用于执行SQL语句
        cursor = mydb.cursor(buffered=True)
        # print(result)
        queryid = "SELECT ID, post_title FROM wp_posts WHERE post_title = %s "
        cursor.execute(queryid, (title1,))
        result = cursor.fetchone()
        if result:
            post_id = result[0]
        else:
            print("未找到文章")
            return

        cursor.close()
        mydb.close()
        print(f"{title1}发布成功")
    except mysql.connector.Error as error:
        print(f"数据库连接失败或查询执行失败：{error}")
    finally:
        # 检查 cursor 是否已初始化并关闭
        if 'cursor' in locals() and cursor is not None:
            cursor.close()
        # 检查 mydb 是否已连接并关闭
        if 'mydb' in locals() and mydb.is_connected():
            mydb.close()
def repeat_chapter(chapter_title):
    db_config = {
        "host": "**************",
        "user": "hanime_men",
        "password": "bWcjxkNYDrsDN3Df",
        "database": "hanime_men"
    }
    try:
        # 连接数据库
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()

        # 执行精确匹配查询
        cursor.execute("SELECT ID FROM wp_posts WHERE post_title = %s AND post_status = 'publish'", (chapter_title,))
        result = cursor.fetchone()

        # 确保读取所有结果
        while cursor.fetchone() is not None:
            pass

        # 检查是否找到文章
        return result is not None
    except mysql.connector.Error as error:
        print(f"数据库操作发生错误：{error}")
        return False
    finally:
        # 关闭游标和连接
        if cursor is not None:
            cursor.close()
        if conn is not None:
            conn.close()


def post_chapter(wordpress_title,chapter_title,title1, ID, images_html):
    query = "http://hanime.men/Locoy.php?action=save&secret=123123"
    config = {
        "host": "**************",
        "user": "hanime_men",
        "password": "bWcjxkNYDrsDN3Df",
        "database": "hanime_men"
    }
    try:
        mydb = mysql.connector.connect(**config)
        # 创建一个cursor对象，用于执行SQL语句
        cursor = mydb.cursor(buffered=True)
        # print(result)
        queryid = "SELECT ID, post_title FROM wp_posts WHERE post_title = %s "
        cursor.execute(queryid, (title1,))
        result = cursor.fetchone()
        if result:
            post_id = result[0]
        else:
            print("未找到文章")
            return
        title = title1.replace(',', '-')
        data_form = {
            "post_title": wordpress_title,
            "post_content": images_html,
            "post_excerpt": "",
            "post_name": chapter_title,
            "tag": "",
            "post_category": rf'{title}',
            "post_type": "post",
            "post_meta[ero_seri]": post_id,
            "post_meta[ero_chaptertitle]": "",
            "post_author": "admin",
            "post_meta[ero_chapter]": ID,
        }
        response = requests.post(query, data=data_form)
        update_post_modified_time(post_id)
        print(f"{chapter_title}发布成功")
    except mysql.connector.Error as error:
        print(f"数据库连接失败或查询执行失败：{error}")
    finally:
        # 检查 cursor 是否已初始化并关闭
        if 'cursor' in locals() and cursor is not None:
            cursor.close()
        # 检查 mydb 是否已连接并关闭
        if 'mydb' in locals() and mydb.is_connected():
            mydb.close()

def make_request_with_retry(url, retries=3, backoff_factor=1, proxies=None, headers=None):
    """
    发起请求并在失败时重试。
    :param url: 请求的URL。
    :param retries: 最大重试次数。
    :param backoff_factor: 每次重试间的退避时间增加因子。
    :param proxies: 使用的代理。
    :param headers: 自定义请求头。
    :return: requests.Response 对象。
    """
    for attempt in range(retries):
        try:
            response = requests.get(url, proxies=proxies, headers=headers)
            if response.status_code == 200:
                return response  # 请求成功，返回响应
            else:
                print(f"请求失败，状态码：{response.status_code}，正在重试...")
        except requests.exceptions.RequestException as e:
            print(f"请求异常：{e}，正在重试...")

        # 计算退避时间并等待
        time.sleep(backoff_factor * (2 ** attempt))

    # 超过重试次数后，返回None
    print("达到最大重试次数，请求失败。")
    return None

def update_post_modified_time(post_id):
    # 连接到数据库
    db_config = {
        "host": "**************",
        "user": "hanime_men",
        "password": "bWcjxkNYDrsDN3Df",
        "database": "hanime_men"
    }
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()

    try:
        # 获取当前本地时间和对应的 GMT 时间
        local_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        gmt_time = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')

        # 构建更新文章时间的 SQL 查询语句
        sql = "UPDATE wp_posts SET post_modified = %s, post_modified_gmt = %s WHERE ID = %s"
        cursor.execute(sql, (local_time, gmt_time, post_id))
        conn.commit()
        print(f"文章 {post_id} 的修改时间已成功更新为本地时间: {local_time}，GMT 时间: {gmt_time}")
    except mysql.connector.Error as error:
        print(f"更新文章 {post_id} 的修改时间失败: {error}")
    finally:
        # 关闭数据库连接
        if conn.is_connected():
            cursor.close()
            conn.close()


if __name__ == '__main__':
    for i in range(1,0,-1):
        url= f'https://mxs13.cc/booklist?end=0&page={i}'
        main()