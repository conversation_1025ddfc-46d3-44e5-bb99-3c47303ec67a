"""
Redis缓存中间件 - 提升API响应速度
"""
import json
import hashlib
from typing import Optional, Any
from functools import wraps
import time
import logging
from app.core.redis_client import redis_client
from app.core.config import settings

logger = logging.getLogger(__name__)

def cache_key_generator(*args, **kwargs) -> str:
    """生成缓存键（绝对安全版本，任何异常都不会中断程序）"""
    try:
        key_data = {
            'args': args,
            'kwargs': {k: v for k, v in kwargs.items() if k != 'db'}  # 排除db对象
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
    except Exception as e:
        logger.warning(f"Failed to generate cache key: {e}, using safe fallback key")
        # 绝对安全的后备方案：只使用函数名和时间戳，避免任何序列化操作
        try:
            import time
            return f"safe_fallback_{int(time.time() * 1000000) % 1000000}"
        except Exception as e2:
            logger.error(f"Even safe fallback failed: {e2}, using minimal key")
            # 最后的安全网：静态键名加随机数
            import random
            return f"emergency_{random.randint(1000000, 9999999)}"

def cached_api(ttl: int = 300, key_prefix: str = ""):
    """完全安全的Redis API缓存装饰器（绝不影响原始功能）"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 全局缓存开关检查 - 如果禁用缓存，直接执行原函数
            if not getattr(settings, 'ENABLE_CACHE', True):
                return await func(*args, **kwargs)
            
            cache_key = None
            
            # 第一步：尝试生成缓存键（失败也不影响主流程）
            try:
                cache_key = f"{key_prefix}:{func.__name__}:{cache_key_generator(*args, **kwargs)}"
            except Exception as e:
                logger.warning(f"Failed to generate cache key for {func.__name__}: {e}, skipping cache")
                cache_key = None
            
            # 第二步：尝试获取Redis缓存（任何错误都不能影响主流程）
            if cache_key:
                try:
                    if redis_client.is_available:
                        cached_result = await redis_client.get(cache_key)
                        if cached_result is not None:
                            logger.info(f"Redis cache hit for {cache_key}")
                            return cached_result
                except Exception as e:
                    logger.warning(f"Redis cache read failed for {func.__name__}: {e}")
                
                # 第三步：尝试内存缓存降级（任何错误都不能影响主流程）  
                try:
                    cached_result = fallback_cache.get(cache_key)
                    if cached_result is not None:
                        logger.info(f"Fallback cache hit for {cache_key}")
                        return cached_result
                except Exception as e:
                    logger.warning(f"Fallback cache read failed for {func.__name__}: {e}")
            
            # 第四步：执行原函数（这个必须成功）
            result = await func(*args, **kwargs)
            
            # 第五步：尝试缓存结果（任何错误都不能影响主流程的返回）
            if cache_key and result is not None:
                try:
                    # 尝试Redis缓存
                    if redis_client.is_available:
                        try:
                            success = await redis_client.set(cache_key, result, ttl)
                            if success:
                                logger.debug(f"Redis cache set for {cache_key}")
                            else:
                                # Redis设置失败，尝试内存缓存
                                fallback_cache.set(cache_key, result, ttl)
                                logger.debug(f"Fallback to memory cache for {cache_key}")
                        except Exception as e:
                            logger.warning(f"Redis cache set failed for {cache_key}: {e}")
                            # Redis异常，尝试内存缓存
                            try:
                                fallback_cache.set(cache_key, result, ttl)
                                logger.debug(f"Fallback cache set for {cache_key}")
                            except Exception as e2:
                                logger.warning(f"Fallback cache set also failed for {cache_key}: {e2}")
                    else:
                        # Redis不可用，直接使用内存缓存
                        try:
                            fallback_cache.set(cache_key, result, ttl)
                            logger.debug(f"Fallback cache set for {cache_key}")
                        except Exception as e:
                            logger.warning(f"Fallback cache set failed for {cache_key}: {e}")
                except Exception as e:
                    logger.warning(f"All cache operations failed for {func.__name__}: {e}")
            
            # 无论缓存操作是否成功，都要返回结果
            return result
        
        return wrapper
    return decorator

async def invalidate_cache_pattern(pattern: str):
    """根据模式清除缓存（支持Redis和内存缓存降级）"""
    deleted_count = 0
    
    # 尝试清除Redis缓存
    if redis_client.is_available:
        try:
            deleted_count = await redis_client.clear_pattern(f"*{pattern}*")
            logger.info(f"Invalidated {deleted_count} Redis cache entries matching pattern: {pattern}")
        except Exception as e:
            logger.error(f"Failed to invalidate Redis cache pattern {pattern}: {e}")
    
    # 清除内存缓存降级
    try:
        memory_keys_to_delete = [key for key in fallback_cache._cache.keys() if pattern in key]
        for key in memory_keys_to_delete:
            fallback_cache.delete(key)
        if memory_keys_to_delete:
            logger.info(f"Invalidated {len(memory_keys_to_delete)} fallback cache entries matching pattern: {pattern}")
        deleted_count += len(memory_keys_to_delete)
    except Exception as e:
        logger.error(f"Failed to invalidate fallback cache pattern {pattern}: {e}")
    
    return deleted_count

class CacheStats:
    """缓存统计"""
    
    @staticmethod
    async def get_stats() -> dict:
        """获取缓存统计信息（Redis + 内存降级）"""
        stats = {
            "redis": {"status": "unavailable"},
            "fallback": {"status": "available"}
        }
        
        # Redis缓存统计
        if redis_client.is_available:
            try:
                info = await redis_client.redis_client.info()
                stats["redis"] = {
                    "status": "available",
                    "connected_clients": info.get("connected_clients", 0),
                    "used_memory_human": info.get("used_memory_human", "N/A"),
                    "total_commands_processed": info.get("total_commands_processed", 0),
                    "keyspace_hits": info.get("keyspace_hits", 0),
                    "keyspace_misses": info.get("keyspace_misses", 0),
                    "hit_ratio": (
                        round(info.get("keyspace_hits", 0) / 
                             max(info.get("keyspace_hits", 0) + info.get("keyspace_misses", 0), 1) * 100, 2)
                    )
                }
            except Exception as e:
                logger.error(f"Failed to get Redis stats: {e}")
                stats["redis"] = {
                    "status": "error",
                    "message": str(e)
                }
        else:
            stats["redis"] = {
                "status": "unavailable",
                "message": "Redis连接不可用"
            }
        
        # 内存缓存统计
        try:
            fallback_cache.cleanup_expired()
            stats["fallback"] = {
                "status": "available",
                "total_items": len(fallback_cache._cache),
                "memory_usage_estimate": sum(
                    len(str(key)) + len(str(value)) 
                    for key, value in fallback_cache._cache.items()
                ),
                "oldest_item_age": (
                    time.time() - min(fallback_cache._timestamps.values()) 
                    if fallback_cache._timestamps else 0
                )
            }
        except Exception as e:
            logger.error(f"Failed to get fallback cache stats: {e}")
            stats["fallback"] = {
                "status": "error",
                "message": str(e)
            }
            
        return stats
    
    @staticmethod
    async def clear_all():
        """清空所有缓存（Redis + 内存降级）"""
        redis_cleared = False
        fallback_cleared = False
        
        # 清空Redis缓存
        if redis_client.is_available:
            try:
                await redis_client.redis_client.flushdb()
                logger.info("All Redis cache cleared")
                redis_cleared = True
            except Exception as e:
                logger.error(f"Failed to clear Redis cache: {e}")
        
        # 清空内存缓存
        try:
            fallback_cache.clear()
            logger.info("All fallback cache cleared")
            fallback_cleared = True
        except Exception as e:
            logger.error(f"Failed to clear fallback cache: {e}")
        
        return {
            "redis_cleared": redis_cleared,
            "fallback_cleared": fallback_cleared,
            "success": redis_cleared or fallback_cleared
        }

# 重新设计的缓存策略 - Redis作为"增强项"而非"依赖项"
CACHE_STRATEGIES = {
    # 热点数据缓存 - 基本信息类，对导航功能影响小
    "manga_basic_info": {"ttl": 3600, "key_prefix": "manga_basic"},     # 1小时 - 漫画基本信息(标题、封面、简介)
    "anime_basic_info": {"ttl": 3600, "key_prefix": "anime_basic"},     # 1小时 - 动漫基本信息
    "featured": {"ttl": 600, "key_prefix": "featured"},                 # 10分钟 - 特色推荐
    "rankings": {"ttl": 600, "key_prefix": "rankings"},                 # 10分钟 - 热门排行榜
    
    # 用户会话缓存 - 不影响核心导航
    "user_reading_history": {"ttl": 1440, "key_prefix": "user_history"}, # 24小时 - 阅读历史
    "search_results": {"ttl": 300, "key_prefix": "search"},              # 5分钟 - 搜索结果
    
    # ⚠️ 绝对不缓存的数据类型 ⚠️
    # - 章节列表 (影响导航)
    # - 章节详情 (影响导航) 
    # - 页面列表 (影响阅读)
    # - 用户状态 (影响登录)
}

def get_cache_decorator(strategy: str):
    """根据策略获取缓存装饰器"""
    if strategy not in CACHE_STRATEGIES:
        raise ValueError(f"Unknown cache strategy: {strategy}")
    
    config = CACHE_STRATEGIES[strategy]
    return cached_api(ttl=config["ttl"], key_prefix=config["key_prefix"])

# 保留旧的内存缓存作为后备方案
class SimpleMemoryCache:
    """简单的内存缓存实现（降级方案）"""
    
    def __init__(self, default_ttl: int = 300):
        self._cache = {}
        self._timestamps = {}
        self.default_ttl = default_ttl
    
    def _is_expired(self, key: str) -> bool:
        """检查缓存项是否过期"""
        if key not in self._timestamps:
            return True
        return time.time() - self._timestamps[key] > self.default_ttl
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        if key in self._cache and not self._is_expired(key):
            return self._cache[key]
        
        # 清理过期项
        if key in self._cache:
            del self._cache[key]
            del self._timestamps[key]
        
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存项"""
        self._cache[key] = value
        self._timestamps[key] = time.time()
    
    def delete(self, key: str) -> None:
        """删除缓存项"""
        if key in self._cache:
            del self._cache[key]
            del self._timestamps[key]
    
    def clear(self) -> None:
        """清空所有缓存"""
        self._cache.clear()
        self._timestamps.clear()
    
    def cleanup_expired(self) -> None:
        """清理所有过期缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self._timestamps.items()
            if current_time - timestamp > self.default_ttl
        ]
        
        for key in expired_keys:
            del self._cache[key]
            del self._timestamps[key]

# 降级缓存实例
fallback_cache = SimpleMemoryCache(default_ttl=300)