                                <!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
<title>漫小肆韓漫-92韩漫-桃桃漫画-Toptoon破解-顶通免费阅读-不收费玩漫-韩国无删减漫画-免费无修韩漫大全-歪歪最新地址</title>
<meta name="keywords" content="漫小肆韓漫-92韩漫-桃桃漫画-Toptoon破解-顶通免费阅读-不收费玩漫-免费看顶通-秘密教学在线看-最新韩漫">
<meta name="description" content="漫小肆韓漫为大家带来最新最全韩国漫画,这里有超多韩漫资源,主打韩国都市系列漫画，立志于做韩漫免费韩国漫画第一站,韩漫免费全集就来漫小肆韓漫">

    <link rel="shortcut icon" href="/static/images/favicon.ico">
    <link href="/static/css/style.css" rel="stylesheet" type="text/css">
    <link href="/static/css/userinfo-vendor.css" rel="stylesheet" type="text/css">
    <link href="/static/css/dm5_style.css" rel="stylesheet" type="text/css">
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
    <script src="/static/js/index.js"></script>
</head>

<body class="white">
<!-- 页面头部 -->
<header class="header container-fluid " style="left: 0px;">
    <div class="container">
        <!-- 左侧logo -->
        <a href="/">
            <img class="header-logo" src="/static/images/header-logo.png">
        </a>
        <!-- 左侧菜单标题 -->
        <ul class="header-title">
            <li><a href="/">首页</a></li>
            <li><a href="/booklist?end=0">连载</a></li>
            <li><a href="/booklist?end=1">完结</a></li>
            <li><a href="/update">更新</a></li>
            <li><a href="/rank">排行</a></li>
            <li><a href="/booklist"><i class="icon icon-cat" style="font-size:19px;vertical-align: sub;"></i></a></li>
        </ul>
        <script>
            var url = location.href;
            if (url.indexOf('booklist') > -1) {
                if (url.indexOf('end=0') > -1) {
                    $('.header-title li a').eq(1).addClass('active');
                } else if (url.indexOf('end=1') > -1) {
                    $('.header-title li a').eq(2).addClass('active');
                } else {
                    $('.header-title li a').eq(5).addClass('active');
                }
            }  else if (url.indexOf('rank') > -1) {
                $('.header-title li a').eq(4).addClass('active');
            } else if (url.indexOf('update') > -1) {
                $('.header-title li a').eq(3).addClass('active');
            } else {
                $('.header-title li a').eq(0).addClass('active');
            }
        </script>
        <!-- 搜索栏 -->
        <div class="header-search">
            <input id="txtKeywords" type="text" value="">
            <a id="btnSearch">搜索</a>
            <script>
                $('#btnSearch').click(function () {
                    var keyword = $('#txtKeywords').val();
                    window.location.href = '/search?keyword=' + keyword;
                })
            </script>
        </div>
        <ul class="header-bar">

            <li class="hover">
                <a href="/history" target="_blank">
                    <i class="icon icon-clock"></i>
                    <p>历史</p>
                </a>
            </li>
            <li class="hover">
                <a href="/bookshelf" data-isload="0" target="_blank">
                    <i class="icon icon-fav"></i>
                    <p>收藏</p>
                    <span class="red-sign"></span>
                </a>
            </li>
        </ul>
        <div class="header_login hover">
                        <a href="/login" class="js_header_login_btn">
                            <img data-isload="0" class="header-avatar" src="/static/images/mrtx.gif">
            </a>
        </div>
    </div>
</header>

<section class="container-fluid bg-gray">
    <div class="container">
        <figure class="cat-filter">
            <dl class="cat-list" id="tags">
                <dt>题 材:</dt>
                <dd class="active" onclick="active(this)" data-val="全部"><a>全部</a></dd>
                                <dd class="" onclick="active(this)" data-val="青春"><a>青春</a></dd>
                                <dd class="" onclick="active(this)" data-val="性感"><a>性感</a></dd>
                                <dd class="" onclick="active(this)" data-val="长腿"><a>长腿</a></dd>
                                <dd class="" onclick="active(this)" data-val="多人"><a>多人</a></dd>
                                <dd class="" onclick="active(this)" data-val="御姐"><a>御姐</a></dd>
                                <dd class="" onclick="active(this)" data-val="巨乳"><a>巨乳</a></dd>
                                <dd class="" onclick="active(this)" data-val="新婚"><a>新婚</a></dd>
                                <dd class="" onclick="active(this)" data-val="媳妇"><a>媳妇</a></dd>
                                <dd class="" onclick="active(this)" data-val="暧昧"><a>暧昧</a></dd>
                                <dd class="" onclick="active(this)" data-val="清纯"><a>清纯</a></dd>
                                <dd class="" onclick="active(this)" data-val="调教"><a>调教</a></dd>
                                <dd class="" onclick="active(this)" data-val="少妇"><a>少妇</a></