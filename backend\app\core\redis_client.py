"""
动态Redis客户端模块
支持从数据库读取配置并实时更新
"""
import json
import pickle
from typing import Any, Optional, Union
import redis.asyncio as redis
from redis.exceptions import RedisError, ConnectionError
import logging
from app.core.serializers import serialize_for_cache, CacheJSONEncoder
from contextlib import asynccontextmanager
import asyncio

logger = logging.getLogger(__name__)

class DynamicRedisClient:
    def __init__(self):
        self.redis_pool: Optional[redis.ConnectionPool] = None
        self.redis_client: Optional[redis.Redis] = None
        self._config_cache = {}
        self._last_config_refresh = 0
        self._config_refresh_interval = 60  # 60秒刷新一次配置
        self._lock = asyncio.Lock()
        
    async def _get_config_from_db(self):
        """从数据库获取配置"""
        try:
            # 延迟导入避免循环依赖
            from app.core.database import SessionLocal
            from app.core.redis_config import RedisConfigManager
            
            db = SessionLocal()
            try:
                config = RedisConfigManager.get_all_config(db)
                self._config_cache = config
                return config
            finally:
                db.close()
        except Exception as e:
            logger.error(f"从数据库获取Redis配置失败: {e}")
            # 返回缓存的配置或默认配置
            return self._config_cache or {
                'redis_enabled': False,
                'redis_host': 'localhost',
                'redis_port': 6379,
                'redis_db': 0,
                'redis_password': '',
                'redis_max_memory': 0,
                'redis_expire_time': 3600,
                'redis_max_connections': 50
            }
    
    async def _should_refresh_config(self) -> bool:
        """检查是否需要刷新配置"""
        import time
        current_time = time.time()
        return current_time - self._last_config_refresh > self._config_refresh_interval
    
    async def init_redis(self, force_reconnect: bool = False):
        """初始化或重新初始化Redis连接"""
        async with self._lock:
            try:
                # 获取最新配置
                config = await self._get_config_from_db()
                import time
                self._last_config_refresh = time.time()
                
                # 检查是否启用Redis
                if not config.get('redis_enabled', False):
                    logger.info("Redis缓存已禁用")
                    if self.redis_client:
                        await self.close_redis()
                    return
                
                # 如果已经连接且不强制重连，检查连接是否有效
                if self.redis_client and not force_reconnect:
                    try:
                        await self.redis_client.ping()
                        # 连接有效，只更新内存限制
                        await self._update_memory_limit(config.get('redis_max_memory', 0))
                        return
                    except:
                        pass  # 连接失效，继续重建
                
                # 关闭旧连接
                if self.redis_client:
                    await self.close_redis()
                
                # 构建Redis URL
                password = config.get('redis_password', '')
                host = config.get('redis_host', 'localhost')
                port = config.get('redis_port', 6379)
                db_num = config.get('redis_db', 0)
                max_connections = config.get('redis_max_connections', 50)
                
                if password:
                    redis_url = f"redis://:{password}@{host}:{port}/{db_num}"
                else:
                    redis_url = f"redis://{host}:{port}/{db_num}"
                
                # 创建连接池
                self.redis_pool = redis.ConnectionPool.from_url(
                    redis_url,
                    max_connections=max_connections,
                    retry_on_timeout=True,
                    decode_responses=True
                )
                self.redis_client = redis.Redis(connection_pool=self.redis_pool)
                
                # 测试连接
                await self.redis_client.ping()
                logger.info(f"Redis连接成功: {host}:{port}/{db_num}")
                
                # 设置内存限制
                await self._update_memory_limit(config.get('redis_max_memory', 0))
                
            except Exception as e:
                logger.warning(f"Redis连接失败: {e}，将使用降级模式")
                self.redis_client = None
    
    async def _update_memory_limit(self, max_memory_mb: int):
        """更新Redis内存限制"""
        if not self.redis_client:
            return
        
        try:
            if max_memory_mb > 0:
                # 设置内存限制（转换为字节）
                max_memory_bytes = max_memory_mb * 1024 * 1024
                await self.redis_client.config_set('maxmemory', max_memory_bytes)
                # 设置内存淘汰策略为LRU
                await self.redis_client.config_set('maxmemory-policy', 'allkeys-lru')
                logger.info(f"Redis内存限制设置为: {max_memory_mb}MB")
            else:
                # 取消内存限制
                await self.redis_client.config_set('maxmemory', 0)
                logger.info("Redis内存限制已取消")
        except Exception as e:
            logger.error(f"设置Redis内存限制失败: {e}")
    
    async def _ensure_connected(self):
        """确保Redis已连接并刷新配置"""
        # 检查是否需要刷新配置
        if await self._should_refresh_config():
            await self.init_redis(force_reconnect=False)
        
        # 如果未连接，尝试连接
        if not self.redis_client:
            await self.init_redis()
    
    async def close_redis(self):
        """关闭Redis连接"""
        if self.redis_client:
            await self.redis_client.close()
            self.redis_client = None
            self.redis_pool = None
            logger.info("Redis连接已关闭")
    
    async def get_memory_info(self) -> Optional[dict]:
        """获取Redis内存使用信息"""
        if not self.redis_client:
            return None
        
        try:
            info = await self.redis_client.info('memory')
            return {
                'used_memory': info.get('used_memory', 0),
                'used_memory_human': info.get('used_memory_human', '0B'),
                'used_memory_peak': info.get('used_memory_peak', 0),
                'used_memory_peak_human': info.get('used_memory_peak_human', '0B'),
                'maxmemory': info.get('maxmemory', 0),
                'maxmemory_human': info.get('maxmemory_human', '0B'),
                'maxmemory_policy': info.get('maxmemory_policy', 'noeviction'),
                'mem_fragmentation_ratio': info.get('mem_fragmentation_ratio', 1.0)
            }
        except Exception as e:
            logger.error(f"获取Redis内存信息失败: {e}")
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        expire: Optional[int] = None,
        serialize: str = "json"
    ) -> bool:
        """设置缓存值（支持动态配置）"""
        await self._ensure_connected()
        
        if not self.redis_client:
            return False
            
        try:
            # 如果未指定过期时间，使用配置中的默认值
            if expire is None:
                expire = self._config_cache.get('redis_expire_time', 3600)
                
            # 序列化数据
            if serialize == "pickle":
                serialized_value = pickle.dumps(value)
            else:
                serialized_data = serialize_for_cache(value)
                serialized_value = json.dumps(serialized_data, cls=CacheJSONEncoder, ensure_ascii=False)
            
            await self.redis_client.set(key, serialized_value, ex=expire)
            return True
        except RedisError as e:
            logger.error(f"Redis set操作失败 {key}: {e}")
            return False
    
    async def get(
        self, 
        key: str, 
        serialize: str = "json"
    ) -> Optional[Any]:
        """获取缓存值"""
        await self._ensure_connected()
        
        if not self.redis_client:
            return None
            
        try:
            value = await self.redis_client.get(key)
            if value is None:
                return None
                
            # 反序列化数据
            if serialize == "pickle":
                return pickle.loads(value)
            else:
                return json.loads(value)
        except RedisError as e:
            logger.error(f"Redis get操作失败 {key}: {e}")
            return None
        except (json.JSONDecodeError, pickle.PickleError) as e:
            logger.error(f"反序列化失败 {key}: {e}")
            return None
    
    async def delete(self, key: str) -> bool:
        """删除缓存键"""
        await self._ensure_connected()
        
        if not self.redis_client:
            return False
            
        try:
            result = await self.redis_client.delete(key)
            return result > 0
        except RedisError as e:
            logger.error(f"Redis delete操作失败 {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        await self._ensure_connected()
        
        if not self.redis_client:
            return False
            
        try:
            return await self.redis_client.exists(key) > 0
        except RedisError as e:
            logger.error(f"Redis exists操作失败 {key}: {e}")
            return False
    
    async def expire(self, key: str, time: int) -> bool:
        """设置键的过期时间"""
        await self._ensure_connected()
        
        if not self.redis_client:
            return False
            
        try:
            return await self.redis_client.expire(key, time)
        except RedisError as e:
            logger.error(f"Redis expire操作失败 {key}: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """按模式删除键"""
        await self._ensure_connected()
        
        if not self.redis_client:
            return 0
            
        try:
            keys = await self.redis_client.keys(pattern)
            if keys:
                return await self.redis_client.delete(*keys)
            return 0
        except RedisError as e:
            logger.error(f"Redis clear_pattern操作失败 {pattern}: {e}")
            return 0
    
    async def flush_db(self) -> bool:
        """清空当前数据库"""
        await self._ensure_connected()
        
        if not self.redis_client:
            return False
            
        try:
            await self.redis_client.flushdb()
            logger.info("Redis数据库已清空")
            return True
        except RedisError as e:
            logger.error(f"Redis flush_db操作失败: {e}")
            return False
    
    @property
    def is_available(self) -> bool:
        """检查Redis是否可用"""
        return self.redis_client is not None

# 全局Redis客户端实例
redis_client = DynamicRedisClient()

# 便捷的缓存装饰器
def cache_result(
    key_prefix: str,
    expire: Optional[int] = None,
    serialize: str = "json"
):
    """
    缓存函数结果的装饰器
    
    Args:
        key_prefix: 缓存键前缀
        expire: 过期时间(秒)
        serialize: 序列化方式
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # 尝试从缓存获取
            cached_result = await redis_client.get(cache_key, serialize)
            if cached_result is not None:
                logger.debug(f"缓存命中: {cache_key}")
                return cached_result
            
            # 执行原函数
            result = await func(*args, **kwargs)
            
            # 缓存结果
            if result is not None:
                await redis_client.set(cache_key, result, expire, serialize)
                logger.debug(f"缓存设置: {cache_key}")
            
            return result
        return wrapper
    return decorator