"""
简单的内存缓存机制 - 用于提高API响应速度
"""
import time
from typing import Dict, Any, Optional
from functools import wraps

class SimpleCache:
    def __init__(self, default_ttl: int = 300):  # 默认5分钟
        self._cache: Dict[str, Dict[str, Any]] = {}
        self.default_ttl = default_ttl
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key not in self._cache:
            return None
        
        entry = self._cache[key]
        if time.time() > entry['expires_at']:
            del self._cache[key]
            return None
        
        entry['last_accessed'] = time.time()
        return entry['value']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        ttl = ttl or self.default_ttl
        self._cache[key] = {
            'value': value,
            'expires_at': time.time() + ttl,
            'created_at': time.time(),
            'last_accessed': time.time()
        }
    
    def delete(self, key: str) -> None:
        """删除缓存值"""
        if key in self._cache:
            del self._cache[key]
    
    def clear(self) -> None:
        """清空所有缓存"""
        self._cache.clear()
    
    def cleanup_expired(self) -> int:
        """清理过期的缓存项"""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self._cache.items()
            if current_time > entry['expires_at']
        ]
        
        for key in expired_keys:
            del self._cache[key]
        
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        current_time = time.time()
        active_entries = 0
        expired_entries = 0
        
        for entry in self._cache.values():
            if current_time > entry['expires_at']:
                expired_entries += 1
            else:
                active_entries += 1
        
        return {
            'total_entries': len(self._cache),
            'active_entries': active_entries,
            'expired_entries': expired_entries
        }

# 全局缓存实例
manga_cache = SimpleCache(default_ttl=300)  # 漫画数据缓存5分钟
popular_cache = SimpleCache(default_ttl=600)  # 热门数据缓存10分钟

def cache_manga_list(ttl: int = 300):
    """漫画列表缓存装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存key
            cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # 尝试获取缓存
            cached_result = manga_cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            manga_cache.set(cache_key, result, ttl)
            return result
        
        return wrapper
    return decorator

def cache_popular_manga(ttl: int = 600):
    """热门漫画缓存装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            cached_result = popular_cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            result = func(*args, **kwargs)
            popular_cache.set(cache_key, result, ttl)
            return result
        
        return wrapper
    return decorator

def invalidate_manga_cache(manga_id: Optional[int] = None):
    """失效漫画相关缓存"""
    if manga_id:
        # 删除特定漫画的缓存
        keys_to_delete = [
            key for key in manga_cache._cache.keys() 
            if f"manga_id:{manga_id}" in key or f"get_manga:{manga_id}" in str(key)
        ]
        for key in keys_to_delete:
            manga_cache.delete(key)
    else:
        # 清空所有漫画缓存
        manga_cache.clear()
    
    # 清空热门缓存（因为数据可能已经改变）
    popular_cache.clear()