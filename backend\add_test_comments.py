#!/usr/bin/env python3
"""
为anime_id=1添加更多测试评论的脚本
"""

import pymysql
from datetime import datetime
from urllib.parse import urlparse

DATABASE_URL = "mysql+pymysql://sql23721_hentai:507877550%40lihao@************:3306/sql23721_hentai?charset=utf8mb4"

def parse_database_url(url):
    if url.startswith('mysql+pymysql://'):
        url = url.replace('mysql+pymysql://', 'mysql://')
    
    parsed = urlparse(url)
    
    return {
        'host': parsed.hostname,
        'port': parsed.port or 3306,
        'user': parsed.username,
        'password': parsed.password.replace('%40', '@') if parsed.password else None,
        'database': parsed.path.lstrip('/').split('?')[0]
    }

def add_test_comments():
    """为anime_id=1添加测试评论"""
    config = parse_database_url(DATABASE_URL)
    
    try:
        connection = pymysql.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password=config['password'],
            database=config['database'],
            charset='utf8mb4'
        )
        
        print("准备为anime_id=1添加测试评论...")
        
        with connection.cursor() as cursor:
            # 检查anime_id=1是否存在
            cursor.execute("SELECT id, title FROM animes WHERE id = 1")
            anime = cursor.fetchone()
            
            if not anime:
                print("[ERROR] anime_id=1不存在，无法添加评论")
                return False
            
            print(f"目标动漫: {anime[1]}")
            
            # 获取用户
            cursor.execute("SELECT id, username FROM users ORDER BY id")
            users = cursor.fetchall()
            
            if not users:
                print("[ERROR] 没有用户，无法添加评论")
                return False
            
            print(f"可用用户: {[f'{u[1]}(ID:{u[0]})' for u in users]}")
            
            # 检查现有评论数
            cursor.execute("SELECT COUNT(*) FROM comments WHERE anime_id = 1")
            existing_count = cursor.fetchone()[0]
            print(f"现有评论数: {existing_count}")
            
            # 测试评论内容
            test_comments = [
                {
                    'content': '这部动漫太棒了！画面精美，剧情引人入胜。',
                    'user_id': users[0][0]  # admin
                },
                {
                    'content': '角色设计很不错，特别是主角的成长历程很感人。',
                    'user_id': users[1][0] if len(users) > 1 else users[0][0]  # test or admin
                },
                {
                    'content': '音乐和配音都很出色，整体制作质量很高。',
                    'user_id': users[0][0]  # admin
                },
                {
                    'content': '虽然有些地方略显拖沓，但总体来说是一部优秀的作品。',
                    'user_id': users[1][0] if len(users) > 1 else users[0][0]  # test or admin
                },
                {
                    'content': '期待续集！希望能够延续这个精彩的故事。',
                    'user_id': users[0][0]  # admin
                }
            ]
            
            added_count = 0
            now = datetime.now()
            
            for comment_data in test_comments:
                try:
                    cursor.execute("""
                        INSERT INTO comments (user_id, anime_id, content, created_at, updated_at, is_deleted)
                        VALUES (%s, %s, %s, %s, %s, %s)
                    """, (
                        comment_data['user_id'], 
                        1, 
                        comment_data['content'], 
                        now, 
                        now, 
                        0
                    ))
                    added_count += 1
                except Exception as e:
                    print(f"添加评论失败: {e}")
            
            connection.commit()
            
            # 检查最终结果
            cursor.execute("SELECT COUNT(*) FROM comments WHERE anime_id = 1")
            final_count = cursor.fetchone()[0]
            
            print(f"成功添加 {added_count} 条评论")
            print(f"总评论数: {existing_count} -> {final_count}")
            
            # 显示所有评论
            cursor.execute("""
                SELECT c.id, c.content, u.username, c.created_at
                FROM comments c 
                LEFT JOIN users u ON c.user_id = u.id 
                WHERE c.anime_id = 1 
                ORDER BY c.created_at ASC
            """)
            all_comments = cursor.fetchall()
            
            print(f"\nanime_id=1 的所有评论:")
            for i, comment in enumerate(all_comments, 1):
                print(f"  {i}. [{comment[2]}] {comment[1][:50]}... ({comment[3]})")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"操作失败: {e}")
        return False

if __name__ == "__main__":
    print("测试评论添加工具")
    print("=" * 40)
    
    choice = input("是否为anime_id=1添加测试评论? (y/n): ").lower().strip()
    if choice == 'y':
        add_test_comments()
    else:
        print("操作已取消")