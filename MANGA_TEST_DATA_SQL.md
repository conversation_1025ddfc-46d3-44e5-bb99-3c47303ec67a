# 📝 漫画测试数据SQL脚本

## 🎯 测试数据信息

- **标题**：[卿卿啊] 诛仙荡魔新传8-情窦篇叁
- **类型**：同人志 (doujinshi)
- **国家**：日本 (jp) 
- **语言**：中文
- **页数**：217页
- **分类**：同人志

## 📋 完整SQL脚本

```sql
-- ================================
-- 1. 先确保分类存在
-- ================================
INSERT IGNORE INTO categories (name, num) VALUES ('同人志', 0);

-- 获取同人志分类ID（假设为4，实际使用时请查询确认）
SET @category_id = (SELECT id FROM categories WHERE name = '同人志' LIMIT 1);

-- ================================
-- 2. 插入漫画主表数据
-- ================================
INSERT INTO mangas (
    title,
    region_code,
    manga_type,
    category_id,
    author,
    description,
    cover,
    page_count,
    status,
    is_active,
    created_at
) VALUES (
    '[卿卿啊] 诛仙荡魔新传8-情窦篇叁',
    'jp',
    'doujinshi', 
    @category_id,
    '卿卿啊',
    '诛仙荡魔新传系列同人志第8部，情窦篇第三册',
    'https://tu.991314.xyz/images/2025/07/22/1753117736/001.jpg',
    217,
    'completed',
    TRUE,
    NOW()
);

-- 获取刚插入的漫画ID
SET @manga_id = LAST_INSERT_ID();

-- ================================
-- 3. 插入章节数据（单行本只有一个章节）
-- ================================  
INSERT INTO manga_chapters (
    manga_id,
    title,
    chapter_number,
    page_count,
    is_free,
    status,
    release_date,
    created_at
) VALUES (
    @manga_id,
    '情窦篇叁',
    1.0,
    217,
    TRUE,
    'published',
    '2025-07-22',
    NOW()
);

-- 获取章节ID
SET @chapter_id = LAST_INSERT_ID();

-- ================================
-- 4. 批量插入页面数据（217页）
-- ================================

-- 使用存储过程批量插入页面数据
DELIMITER $$

CREATE TEMPORARY PROCEDURE InsertMangaPages()
BEGIN
    DECLARE i INT DEFAULT 1;
    DECLARE page_url VARCHAR(1000);
    DECLARE page_num_str VARCHAR(10);
    
    WHILE i <= 217 DO
        -- 生成三位数的页码字符串（如：001, 002, ..., 217）
        SET page_num_str = LPAD(i, 3, '0');
        SET page_url = CONCAT('https://tu.991314.xyz/images/2025/07/22/1753117736/', page_num_str, '.jpg');
        
        -- 插入页面数据
        INSERT INTO manga_pages (
            chapter_id,
            page_number,
            image_url,
            created_at
        ) VALUES (
            @chapter_id,
            i,
            page_url,
            NOW()
        );
        
        SET i = i + 1;
    END WHILE;
END$$

DELIMITER ;

-- 执行存储过程
CALL InsertMangaPages();

-- 删除临时存储过程
DROP PROCEDURE InsertMangaPages;

-- ================================
-- 5. 更新统计数据
-- ================================

-- 更新漫画的章节数量
UPDATE mangas SET chapter_count = 1 WHERE id = @manga_id;

-- 更新分类下的漫画数量
UPDATE categories SET num = num + 1 WHERE id = @category_id;

-- ================================
-- 6. 验证数据插入结果
-- ================================

-- 查看插入的漫画信息
SELECT 
    m.id,
    m.title,
    m.region_code,
    m.manga_type,
    c.name as category_name,
    m.author,
    m.page_count,
    m.chapter_count,
    m.created_at
FROM mangas m
LEFT JOIN categories c ON m.category_id = c.id
WHERE m.title = '[卿卿啊] 诛仙荡魔新传8-情窦篇叁';

-- 查看章节信息
SELECT 
    mc.id,
    mc.title,
    mc.chapter_number,
    mc.page_count,
    mc.status
FROM manga_chapters mc
JOIN mangas m ON mc.manga_id = m.id
WHERE m.title = '[卿卿啊] 诛仙荡魔新传8-情窦篇叁';

-- 查看页面数量统计
SELECT 
    COUNT(*) as total_pages,
    MIN(page_number) as first_page,
    MAX(page_number) as last_page
FROM manga_pages mp
JOIN manga_chapters mc ON mp.chapter_id = mc.id
JOIN mangas m ON mc.manga_id = m.id
WHERE m.title = '[卿卿啊] 诛仙荡魔新传8-情窦篇叁';

-- 查看前5页和后5页的URL示例
(SELECT mp.page_number, mp.image_url, 'first_5' as type
FROM manga_pages mp
JOIN manga_chapters mc ON mp.chapter_id = mc.id
JOIN mangas m ON mc.manga_id = m.id
WHERE m.title = '[卿卿啊] 诛仙荡魔新传8-情窦篇叁'
ORDER BY mp.page_number ASC
LIMIT 5)

UNION ALL

(SELECT mp.page_number, mp.image_url, 'last_5' as type
FROM manga_pages mp
JOIN manga_chapters mc ON mp.chapter_id = mc.id
JOIN mangas m ON mc.manga_id = m.id
WHERE m.title = '[卿卿啊] 诛仙荡魔新传8-情窦篇叁'
ORDER BY mp.page_number DESC
LIMIT 5)
ORDER BY type, page_number;
```

## 🔧 简化版插入脚本（如果不想使用存储过程）

```sql
-- 手动插入版本：只插入前10页作为测试
-- 插入漫画
INSERT INTO mangas (title, region_code, manga_type, author, cover, status) 
VALUES ('[卿卿啊] 诛仙荡魔新传8-情窦篇叁', 'jp', 'doujinshi', '卿卿啊', 
        'https://tu.991314.xyz/images/2025/07/22/1753117736/001.jpg', 'completed');

SET @manga_id = LAST_INSERT_ID();

-- 插入章节
INSERT INTO manga_chapters (manga_id, title, chapter_number, page_count) 
VALUES (@manga_id, '情窦篇叁', 1.0, 217);

SET @chapter_id = LAST_INSERT_ID();

-- 手动插入前10页作为测试
INSERT INTO manga_pages (chapter_id, page_number, image_url) VALUES
(1, 1, 'https://tu.991314.xyz/images/2025/07/22/1753117736/001.jpg'),
(1, 2, 'https://tu.991314.xyz/images/2025/07/22/1753117736/002.jpg'),
(1, 3, 'https://tu.991314.xyz/images/2025/07/22/1753117736/003.jpg'),
(1, 4, 'https://tu.991314.xyz/images/2025/07/22/1753117736/004.jpg'),
(1, 5, 'https://tu.991314.xyz/images/2025/07/22/1753117736/005.jpg'),
(1, 6, 'https://tu.991314.xyz/images/2025/07/22/1753117736/006.jpg'),
(1, 7, 'https://tu.991314.xyz/images/2025/07/22/1753117736/007.jpg'),
(1, 8, 'https://tu.991314.xyz/images/2025/07/22/1753117736/008.jpg'),
(1, 9, 'https://tu.991314.xyz/images/2025/07/22/1753117736/009.jpg'),
(1, 10, 'https://tu.991314.xyz/images/2025/07/22/1753117736/010.jpg');

-- 验证插入结果
SELECT * FROM mangas WHERE title LIKE '%诛仙荡魔新传8%';
SELECT * FROM manga_chapters WHERE manga_id = @manga_id;
SELECT COUNT(*) as page_count FROM manga_pages WHERE chapter_id = @chapter_id;
```

## 📊 测试查询

```sql
-- 1. 查询漫画详情
SELECT m.*, c.name as category_name 
FROM mangas m 
LEFT JOIN categories c ON m.category_id = c.id 
WHERE m.title = '[卿卿啊] 诛仙荡魔新传8-情窦篇叁';

-- 2. 查询章节列表
SELECT * FROM manga_chapters WHERE manga_id = @manga_id;

-- 3. 查询页面列表（前20页）
SELECT page_number, image_url 
FROM manga_pages 
WHERE chapter_id = @chapter_id 
ORDER BY page_number 
LIMIT 20;

-- 4. 统计信息
SELECT 
    COUNT(DISTINCT m.id) as manga_count,
    COUNT(DISTINCT mc.id) as chapter_count,
    COUNT(mp.id) as page_count
FROM mangas m
LEFT JOIN manga_chapters mc ON m.id = mc.manga_id
LEFT JOIN manga_pages mp ON mc.id = mp.chapter_id
WHERE m.manga_type = 'doujinshi';
```

## ⚠️ 注意事项

1. **分类ID确认**：执行前请确认同人志分类的实际ID
2. **存储过程权限**：确保数据库用户有创建临时存储过程的权限
3. **批量插入性能**：217页数据插入可能需要几秒钟时间
4. **URL有效性**：建议先验证几个图片URL是否可访问
5. **字符编码**：确保数据库支持utf8mb4编码以正确存储中文标题

## 🎯 执行建议

1. **先执行简化版**：插入前10页测试功能
2. **验证数据正确性**：检查漫画、章节、页面数据
3. **再执行完整版**：确认无误后插入全部217页
4. **性能监控**：观察插入过程的性能表现

这个SQL脚本将成功在数据库中创建一个完整的同人志测试数据！



 📋 使用方法：

  # 搜索漫画
  python simple_manga_manager.py --search "借妻"

  # 查看详情
  python simple_manga_manager.py --details 2

  # 删除漫画（需要确认）
  python simple_manga_manager.py --delete 2

  # 直接删除（跳过确认）
  python simple_manga_manager.py --delete 2 --no-confirm

  # 交互式菜单
  python simple_manga_manager.py