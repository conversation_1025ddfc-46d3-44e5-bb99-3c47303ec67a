# 🚀 动漫网站生产环境部署检查清单

## 📋 部署前检查 (必须完成)

### 🔐 安全配置
- [ ] **环境变量**: 已创建 `.env.production` 并填入强密码
- [ ] **JWT密钥**: 使用32字符以上随机字符串
- [ ] **数据库密码**: 使用强密码，不是默认值
- [ ] **CORS设置**: 仅允许实际域名访问
- [ ] **禁用调试**: `NEXT_PUBLIC_DISABLE_DEVTOOLS=true`

### 🧹 文件清理
- [ ] **调试文件**: 已删除所有 `debug_*` 文件
- [ ] **日志文件**: 已删除开发期间的 `*.log` 文件
- [ ] **临时文件**: 已删除 `temp/` 目录和临时配置
- [ ] **压缩包**: 已删除 `*.zip` 备份文件
- [ ] **测试数据**: 已清理测试用的JSON文件

### 📦 构建检查
- [ ] **前端构建**: `npm run build` 成功完成
- [ ] **后端依赖**: 所有Python依赖已正确安装
- [ ] **数据库迁移**: Alembic迁移脚本就绪
- [ ] **Docker镜像**: 构建成功，无错误

### 🌐 网络配置
- [ ] **域名解析**: DNS配置指向服务器IP
- [ ] **SSL证书**: HTTPS证书有效且未过期
- [ ] **反向代理**: Nginx配置正确
- [ ] **防火墙**: 仅开放必要端口(80,443,22)

## 🚀 部署步骤

### 1. 环境准备
```bash
# 确保Docker已安装并运行
docker --version
docker-compose --version

# 确保环境变量文件存在
ls -la .env.production
```

### 2. 文件清理
```bash
# 运行清理脚本
chmod +x cleanup-for-production.sh
./cleanup-for-production.sh

# 或Windows
cleanup-for-production.bat
```

### 3. 配置检查
```bash
# 检查环境变量是否包含默认值
grep -E "your-super-secret|rootpassword|password" .env.production
# 应该没有输出，如有输出说明还有默认值需要修改
```

### 4. 部署执行
```bash
# 方式1: 使用一键脚本 (推荐)
chmod +x deploy-production-clean.sh
./deploy-production-clean.sh

# 方式2: 手动Docker部署
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d
```

## 🏥 部署后检查

### 服务状态
- [ ] **前端服务**: http://localhost:3000 可访问
- [ ] **后端API**: http://localhost:8000/api/v1/ 返回正常
- [ ] **数据库**: 连接正常，表结构完整
- [ ] **容器状态**: 所有Docker容器运行正常

### 功能测试
- [ ] **用户注册**: 新用户可正常注册
- [ ] **用户登录**: 登录功能正常
- [ ] **动漫浏览**: 动漫列表加载正常
- [ ] **视频播放**: 播放器功能正常
- [ ] **搜索功能**: 搜索结果正确
- [ ] **响应式**: 移动端界面正常

### 性能检查
- [ ] **首页加载**: 3秒内完成加载
- [ ] **API响应**: 平均响应时间<500ms
- [ ] **数据库**: 查询性能正常
- [ ] **内存使用**: 系统内存使用<80%

## 🔒 安全加固 (生产环境必须)

### 系统安全
```bash
# 1. 更新系统
sudo apt update && sudo apt upgrade -y

# 2. 配置防火墙
sudo ufw enable
sudo ufw allow 22/tcp   # SSH
sudo ufw allow 80/tcp   # HTTP
sudo ufw allow 443/tcp  # HTTPS

# 3. 禁用不必要服务
sudo systemctl disable --now snapd
sudo systemctl disable --now bluetooth
```

### 应用安全
```bash
# 1. 设置文件权限
chmod 600 .env.production
chown root:root .env.production

# 2. 配置SSL证书 (Let's Encrypt)
sudo certbot --nginx -d yourdomain.com

# 3. 配置安全头 (Nginx)
# 添加到nginx配置:
# add_header X-Frame-Options "SAMEORIGIN" always;
# add_header X-Content-Type-Options "nosniff" always;
# add_header Referrer-Policy "no-referrer-when-downgrade" always;
```

### 数据库安全
```bash
# 1. 限制数据库网络访问
# 在docker-compose.prod.yml中已禁用3306端口暴露

# 2. 定期备份
# 设置cron任务每日备份数据库
```

## 📊 监控配置

### 日志监控
```bash
# 配置日志轮转
sudo tee /etc/logrotate.d/anime-website << 'EOF'
/var/log/anime-website/*.log {
    daily
    missingok
    rotate 30
    compress
    notifempty
    create 644 www-data www-data
}
EOF
```

### 性能监控
- [ ] **系统监控**: 配置CPU、内存、磁盘监控
- [ ] **应用监控**: 配置API响应时间监控
- [ ] **数据库监控**: 配置数据库性能监控
- [ ] **错误监控**: 配置错误日志收集

### 备份策略
```bash
# 数据库备份脚本
cat > /opt/backup-anime-db.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker exec anime-mysql mysqldump -u root -p$MYSQL_ROOT_PASSWORD anime_db | gzip > /backup/anime_db_$DATE.sql.gz
find /backup -name "anime_db_*.sql.gz" -mtime +30 -delete
EOF

# 添加到crontab
echo "0 2 * * * /opt/backup-anime-db.sh" | sudo crontab -
```

## 🚨 故障恢复

### 紧急重启
```bash
# 重启所有服务
docker-compose -f docker-compose.prod.yml restart

# 重启单个服务
docker-compose -f docker-compose.prod.yml restart frontend
docker-compose -f docker-compose.prod.yml restart backend
```

### 数据库恢复
```bash
# 从备份恢复数据库
zcat /backup/anime_db_20231225_020000.sql.gz | docker exec -i anime-mysql mysql -u root -p anime_db
```

### 回滚部署
```bash
# 回滚到上一个版本
docker-compose -f docker-compose.prod.yml down
git checkout previous-stable-version
docker-compose -f docker-compose.prod.yml up -d
```

## ✅ 部署成功标准

### 技术指标
- [ ] **可用性**: 99.9%以上
- [ ] **响应时间**: 首页3秒内加载
- [ ] **API响应**: 平均500ms以内
- [ ] **错误率**: 小于0.1%

### 用户体验
- [ ] **功能完整**: 所有核心功能正常
- [ ] **界面响应**: 移动端和PC端都正常
- [ ] **搜索准确**: 搜索结果相关度高
- [ ] **播放流畅**: 视频播放无卡顿

### 安全标准
- [ ] **HTTPS**: 全站HTTPS访问
- [ ] **无漏洞**: 无明显安全漏洞
- [ ] **权限控制**: 用户权限控制正常
- [ ] **数据保护**: 用户数据加密存储

## 📞 技术支持

### 常见问题
1. **服务无法启动**: 检查端口占用和环境变量
2. **数据库连接失败**: 检查数据库服务和连接字符串
3. **前端白屏**: 检查API连接和环境变量
4. **性能问题**: 检查资源使用和缓存配置

### 联系支持
- 查看日志: `docker-compose logs -f`
- 检查服务: `docker-compose ps`
- 系统状态: `htop`, `df -h`, `free -m`

---

**部署完成后请保存此检查清单，用于后续维护和故障排除！**