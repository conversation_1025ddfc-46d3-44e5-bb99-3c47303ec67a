from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.deps import get_current_active_user, get_current_user_optional
from app.core.async_tasks import queue_view_count_update
from app.middleware.cache import get_cache_decorator, invalidate_cache_pattern
from app.crud.manga import MangaCRUD, MangaChapterCRUD, MangaPageCRUD, MangaReadingProgressCRUD
from app.schemas.manga import (
    Manga, MangaBasic, MangaCreate, MangaUpdate, MangaListResponse,
    MangaChapter, MangaChapterCreate, MangaChapterUpdate, MangaChapterListResponse,
    MangaPage, MangaPageCreate, MangaPageListResponse,
    MangaReadingProgress, MangaReadingProgressCreate, MangaReadingProgressWithDetails
)
from app.models import User

router = APIRouter()


# ==================== 漫画管理相关接口 ====================

@router.get("/", response_model=MangaListResponse, summary="获取漫画列表")
@get_cache_decorator("manga_basic_info")
async def get_mangas(
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(20, ge=1, le=50, description="返回数量"),
    category_id: Optional[int] = Query(None, description="分类ID"),
    region_code: Optional[str] = Query(None, description="地区代码"),
    manga_type: Optional[str] = Query(None, description="漫画类型"),
    status: Optional[str] = Query(None, description="状态"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="排序方向"),
    db: Session = Depends(get_db),
):
    mangas, total = MangaCRUD.get_mangas(
        db=db,
        skip=skip,
        limit=limit,
        category_id=category_id,
        region_code=region_code,
        manga_type=manga_type,
        status=status,
        search=search,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    total_pages = (total + limit - 1) // limit
    
    return MangaListResponse(
        mangas=mangas,
        total=total,
        page=(skip // limit) + 1,
        limit=limit,
        total_pages=total_pages
    )


@router.get("/popular", response_model=List[MangaBasic], summary="获取热门漫画")
@get_cache_decorator("featured")
async def get_popular_mangas(
    limit: int = Query(10, ge=1, le=50, description="返回数量"),
    db: Session = Depends(get_db),
):
    return MangaCRUD.get_popular_mangas(db=db, limit=limit)


@router.get("/recent", response_model=List[MangaBasic], summary="获取最近更新漫画")
@get_cache_decorator("featured")
async def get_recent_mangas(
    limit: int = Query(10, ge=1, le=50, description="返回数量"),
    db: Session = Depends(get_db),
):
    return MangaCRUD.get_recently_updated_mangas(db=db, limit=limit)


@router.get("/search/by-tags", response_model=MangaListResponse, summary="根据标签搜索漫画")
@get_cache_decorator("search_results")
async def search_mangas_by_tags(
    tags: str = Query(..., description="标签名称，多个用逗号分隔"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(20, ge=1, le=50, description="返回数量"),
    db: Session = Depends(get_db),
):
    tag_names = [tag.strip() for tag in tags.split(",") if tag.strip()]
    if not tag_names:
        raise HTTPException(status_code=400, detail="至少需要提供一个标签")
    
    mangas, total = MangaCRUD.search_mangas_by_tags(
        db=db, tag_names=tag_names, skip=skip, limit=limit
    )
    
    total_pages = (total + limit - 1) // limit
    
    return MangaListResponse(
        mangas=mangas,
        total=total,
        page=(skip // limit) + 1,
        limit=limit,
        total_pages=total_pages
    )


@router.get("/{manga_id}/related", response_model=List[MangaBasic], summary="获取相关推荐漫画")
@get_cache_decorator("featured")
async def get_related_mangas(
    manga_id: int,
    limit: int = Query(10, ge=1, le=20, description="推荐数量"),
    db: Session = Depends(get_db),
):
    related_mangas = MangaCRUD.get_related_mangas(db=db, manga_id=manga_id, limit=limit)
    return related_mangas


@router.get("/{manga_id}", response_model=Manga, summary="获取漫画详情")
@get_cache_decorator("manga_basic_info")
async def get_manga(
    manga_id: int,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
):
    manga = MangaCRUD.get_manga(db=db, manga_id=manga_id)
    if not manga:
        raise HTTPException(status_code=404, detail="漫画不存在")
    
    # 异步更新浏览次数（不阻塞响应）
    queue_view_count_update(manga_id)
    
    return manga


@router.post("/", response_model=Manga, summary="创建漫画")
def create_manga(
    manga: MangaCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="权限不足")
    
    return MangaCRUD.create_manga(db=db, manga_data=manga)


@router.put("/{manga_id}", response_model=Manga, summary="更新漫画信息")
def update_manga(
    manga_id: int,
    manga_update: MangaUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="权限不足")
    
    updated_manga = MangaCRUD.update_manga(db=db, manga_id=manga_id, manga_update=manga_update)
    if not updated_manga:
        raise HTTPException(status_code=404, detail="漫画不存在")
    
    return updated_manga


@router.delete("/{manga_id}", summary="删除漫画")
def delete_manga(
    manga_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="权限不足")
    
    if not MangaCRUD.delete_manga(db=db, manga_id=manga_id):
        raise HTTPException(status_code=404, detail="漫画不存在")
    
    return {"message": "漫画删除成功"}


# ==================== 漫画章节相关接口 ====================

@router.get("/{manga_id}/chapters", response_model=MangaChapterListResponse, summary="获取漫画章节列表")
async def get_manga_chapters(
    manga_id: int,
    db: Session = Depends(get_db),
):
    # 验证漫画是否存在
    manga = MangaCRUD.get_manga_basic(db=db, manga_id=manga_id)
    if not manga:
        raise HTTPException(status_code=404, detail="漫画不存在")
    
    chapters = MangaChapterCRUD.get_chapters_by_manga(db=db, manga_id=manga_id)
    
    return MangaChapterListResponse(
        chapters=chapters,
        total=len(chapters),
        manga_id=manga_id
    )


@router.get("/chapters/{chapter_id}", response_model=MangaChapter, summary="获取章节详情")
async def get_chapter(
    chapter_id: int,
    db: Session = Depends(get_db),
):
    chapter = MangaChapterCRUD.get_chapter(db=db, chapter_id=chapter_id)
    if not chapter:
        raise HTTPException(status_code=404, detail="章节不存在")
    
    return chapter


@router.get("/{manga_id}/chapters/{chapter_number}", response_model=MangaChapter, summary="通过章节号获取章节详情")
async def get_chapter_by_number(
    manga_id: int,
    chapter_number: float,
    db: Session = Depends(get_db),
):
    # 验证漫画是否存在
    manga = MangaCRUD.get_manga_basic(db=db, manga_id=manga_id)
    if not manga:
        raise HTTPException(status_code=404, detail="漫画不存在")
    
    chapter = MangaChapterCRUD.get_chapter_by_number(
        db=db, 
        manga_id=manga_id, 
        chapter_number=chapter_number
    )
    if not chapter:
        raise HTTPException(status_code=404, detail="章节不存在")
    
    return chapter


@router.post("/{manga_id}/chapters", response_model=MangaChapter, summary="创建章节")
def create_chapter(
    manga_id: int,
    chapter: MangaChapterCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="权限不足")
    
    # 验证漫画是否存在
    manga = MangaCRUD.get_manga_basic(db=db, manga_id=manga_id)
    if not manga:
        raise HTTPException(status_code=404, detail="漫画不存在")
    
    if chapter.manga_id != manga_id:
        raise HTTPException(status_code=400, detail="章节归属漫画不匹配")
    
    return MangaChapterCRUD.create_chapter(db=db, chapter_data=chapter)


@router.put("/chapters/{chapter_id}", response_model=MangaChapter, summary="更新章节信息")
def update_chapter(
    chapter_id: int,
    chapter_update: MangaChapterUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="权限不足")
    
    updated_chapter = MangaChapterCRUD.update_chapter(
        db=db, chapter_id=chapter_id, chapter_update=chapter_update
    )
    if not updated_chapter:
        raise HTTPException(status_code=404, detail="章节不存在")
    
    return updated_chapter


@router.delete("/chapters/{chapter_id}", summary="删除章节")
def delete_chapter(
    chapter_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="权限不足")
    
    if not MangaChapterCRUD.delete_chapter(db=db, chapter_id=chapter_id):
        raise HTTPException(status_code=404, detail="章节不存在")
    
    return {"message": "章节删除成功"}


# ==================== 漫画页面相关接口 ====================

@router.get("/chapters/{chapter_id}/pages", response_model=MangaPageListResponse, summary="获取章节页面列表")
async def get_chapter_pages(
    chapter_id: int,
    db: Session = Depends(get_db),
):
    # 验证章节是否存在
    chapter = MangaChapterCRUD.get_chapter(db=db, chapter_id=chapter_id)
    if not chapter:
        raise HTTPException(status_code=404, detail="章节不存在")
    
    # 获取所有页面，不限制数量
    pages = MangaPageCRUD.get_pages_by_chapter(db=db, chapter_id=chapter_id)
    
    return MangaPageListResponse(
        pages=pages,
        total=len(pages),
        chapter_id=chapter_id
    )


@router.post("/chapters/{chapter_id}/pages", response_model=List[MangaPage], summary="批量创建页面")
def create_pages(
    chapter_id: int,
    pages: List[MangaPageCreate],
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="权限不足")
    
    # 验证章节是否存在
    chapter = MangaChapterCRUD.get_chapter(db=db, chapter_id=chapter_id)
    if not chapter:
        raise HTTPException(status_code=404, detail="章节不存在")
    
    # 验证所有页面都属于当前章节
    for page in pages:
        if page.chapter_id != chapter_id:
            raise HTTPException(status_code=400, detail="页面归属章节不匹配")
    
    return MangaPageCRUD.bulk_create_pages(db=db, pages_data=pages)


# ==================== 阅读进度相关接口 ====================

@router.get("/{manga_id}/reading-progress", response_model=MangaReadingProgress, summary="获取阅读进度")
def get_reading_progress(
    manga_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    progress = MangaReadingProgressCRUD.get_user_reading_progress(
        db=db, user_id=current_user.id, manga_id=manga_id
    )
    if not progress:
        raise HTTPException(status_code=404, detail="阅读进度不存在")
    
    return progress


@router.post("/{manga_id}/reading-progress", response_model=MangaReadingProgress, summary="更新阅读进度")
def update_reading_progress(
    manga_id: int,
    progress: MangaReadingProgressCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    if progress.manga_id != manga_id:
        raise HTTPException(status_code=400, detail="漫画ID不匹配")
    
    # 验证漫画和章节是否存在
    manga = MangaCRUD.get_manga_basic(db=db, manga_id=manga_id)
    if not manga:
        raise HTTPException(status_code=404, detail="漫画不存在")
    
    chapter = MangaChapterCRUD.get_chapter(db=db, chapter_id=progress.chapter_id)
    if not chapter or chapter.manga_id != manga_id:
        raise HTTPException(status_code=404, detail="章节不存在或不属于此漫画")
    
    return MangaReadingProgressCRUD.update_reading_progress(
        db=db, user_id=current_user.id, progress_data=progress
    )


@router.delete("/{manga_id}/reading-progress", summary="删除阅读进度")
def delete_reading_progress(
    manga_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    if not MangaReadingProgressCRUD.delete_reading_progress(
        db=db, user_id=current_user.id, manga_id=manga_id
    ):
        raise HTTPException(status_code=404, detail="阅读进度不存在")
    
    return {"message": "阅读进度删除成功"}


@router.get("/reading-history", response_model=List[MangaReadingProgressWithDetails], summary="获取用户阅读历史")
def get_reading_history(
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(20, ge=1, le=50, description="返回数量"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    progress_list, _ = MangaReadingProgressCRUD.get_user_reading_history(
        db=db, user_id=current_user.id, skip=skip, limit=limit
    )
    
    return progress_list