/**
 * 生产环境工具函数
 */

// 禁用生产环境中的console.log
export const logger = {
  log: (...args: any[]) => {
    if (process.env.NODE_ENV !== 'production' || process.env.NEXT_PUBLIC_DISABLE_CONSOLE !== 'true') {
      console.log(...args);
    }
  },
  error: (...args: any[]) => {
    // error 日志在生产环境中也保留
    console.error(...args);
  },
  warn: (...args: any[]) => {
    if (process.env.NODE_ENV !== 'production' || process.env.NEXT_PUBLIC_DISABLE_CONSOLE !== 'true') {
      console.warn(...args);
    }
  },
};

// 检查是否为生产环境
export const isProduction = process.env.NODE_ENV === 'production';

// 检查是否应该禁用开发工具
export const shouldDisableDevTools = 
  process.env.NEXT_PUBLIC_DISABLE_DEVTOOLS === 'true' || isProduction;

// 获取API基础URL
export const getApiBaseUrl = () => {
  return process.env.NEXT_PUBLIC_API_BASE_URL ||
    (isProduction ? '/api' : 'http://localhost:8000');
};