// 本地化占位符图片工具
export class PlaceholderImages {
  // 生成基于哈希的渐变色占位图
  static generateGradientPlaceholder(text: string, width: number, height: number): string {
    const hash = this.hashString(text);
    const color1 = this.intToRGB(hash);
    const color2 = this.intToRGB(hash * 2);
    
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    
    if (ctx) {
      // 创建线性渐变
      const gradient = ctx.createLinearGradient(0, 0, width, height);
      gradient.addColorStop(0, color1);
      gradient.addColorStop(1, color2);
      
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, height);
      
      // 添加文字
      if (text) {
        ctx.fillStyle = '#ffffff';
        ctx.font = `${Math.min(width, height) / 8}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(text.slice(0, 2), width / 2, height / 2);
      }
    }
    
    return canvas.toDataURL('image/jpeg', 0.8);
  }
  
  // 字符串哈希函数
  private static hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }
  
  // 整数转RGB颜色
  private static intToRGB(i: number): string {
    const c = (i & 0x00FFFFFF).toString(16).toUpperCase();
    return '#' + '00000'.substring(0, 6 - c.length) + c;
  }
  
  // 获取动漫封面占位符
  static getAnimeCoverPlaceholder(title: string): string {
    return this.generateGradientPlaceholder(title, 400, 600);
  }
  
  // 获取横幅占位符  
  static getBannerPlaceholder(title: string): string {
    return this.generateGradientPlaceholder(title, 1200, 600);
  }
  
  // 获取缩略图占位符
  static getThumbnailPlaceholder(title: string): string {
    return this.generateGradientPlaceholder(title, 128, 96);
  }
  
  // 获取小头像占位符
  static getAvatarPlaceholder(title: string): string {
    return this.generateGradientPlaceholder(title, 80, 80);
  }
}