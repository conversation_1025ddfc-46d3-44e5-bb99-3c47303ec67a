'use client';

import Image from 'next/image';
import { useState } from 'react';

interface SafeImageProps {
  src: string;
  alt: string;
  fill?: boolean;
  priority?: boolean;
  className?: string;
  width?: number;
  height?: number;
}

export default function SafeImage({ 
  src, 
  alt, 
  fill, 
  priority, 
  className,
  width,
  height 
}: SafeImageProps) {
  const [imgSrc, setImgSrc] = useState(src);
  const [isError, setIsError] = useState(false);

  // 检查URL是否包含中文字符
  const hasChinese = /[\u4e00-\u9fff]/.test(src);

  // 如果包含中文字符，使用普通的img标签避免Next.js优化
  if (hasChinese) {
    return (
      // eslint-disable-next-line @next/next/no-img-element
      <img
        src={imgSrc}
        alt={alt}
        className={className}
        onError={() => {
          if (!isError) {
            setIsError(true);
            setImgSrc('/placeholder-manga.jpg');
          }
        }}
        style={fill ? {
          position: 'absolute',
          height: '100%',
          width: '100%',
          inset: '0px',
          objectFit: 'cover',
          color: 'transparent'
        } : {
          width: width || 'auto',
          height: height || 'auto'
        }}
      />
    );
  }

  // 对于其他URL，使用Next.js Image组件
  return (
    <Image
      src={imgSrc}
      alt={alt}
      fill={fill}
      priority={priority}
      className={className}
      width={width}
      height={height}
      onError={() => {
        if (!isError) {
          setIsError(true);
          setImgSrc('/placeholder-manga.jpg');
        }
      }}
    />
  );
}