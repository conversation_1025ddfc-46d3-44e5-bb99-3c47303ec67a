import { STORAGE_KEYS } from '../constants';

/**
 * 检查用户是否已登录
 */
export function isAuthenticated(): boolean {
  return !!getAccessToken();
}

/**
 * 获取访问令牌
 */
export function getAccessToken(): string | null {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
}

/**
 * 设置访问令牌
 */
export function setAccessToken(token: string): void {
  if (typeof window === 'undefined') return;
  localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, token);
}

/**
 * 清除访问令牌
 */
export function clearAccessToken(): void {
  if (typeof window === 'undefined') return;
  localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
}

/**
 * 获取认证头
 */
export function getAuthHeaders(): Record<string, string> {
  const token = getAccessToken();
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
}

/**
 * 检查令牌是否过期
 */
export function isTokenExpired(token?: string): boolean {
  const actualToken = token || getAccessToken();
  if (!actualToken) return true;
  
  try {
    const payload = JSON.parse(atob(actualToken.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch {
    return true;
  }
}

/**
 * 从令牌中获取用户信息
 */
export function getUserFromToken(token?: string): any | null {
  const actualToken = token || getAccessToken();
  if (!actualToken) return null;
  
  try {
    const payload = JSON.parse(atob(actualToken.split('.')[1]));
    return {
      id: payload.sub,
      username: payload.username,
      email: payload.email,
      isAdmin: payload.is_admin,
      exp: payload.exp,
      iat: payload.iat,
    };
  } catch {
    return null;
  }
}

/**
 * 检查用户是否为管理员
 */
export function isAdmin(): boolean {
  const user = getUserFromToken();
  return user?.isAdmin === true;
}

/**
 * 自动刷新令牌（如果需要）
 */
export function shouldRefreshToken(): boolean {
  const token = getAccessToken();
  if (!token) return false;
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = payload.exp - currentTime;
    
    // 如果令牌在5分钟内过期，则需要刷新
    return timeUntilExpiry < 300;
  } catch {
    return false;
  }
}

/**
 * 登出用户
 */
export function logout(): void {
  clearAccessToken();
  // 清除其他用户相关的本地存储
  if (typeof window !== 'undefined') {
    localStorage.removeItem(STORAGE_KEYS.USER_PREFERENCES);
    // 可以添加其他需要清除的数据
  }
}
