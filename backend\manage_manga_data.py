#!/usr/bin/env python3
"""
漫画数据管理工具
支持查看、搜索、删除漫画及相关数据
"""

import sys
import os
from sqlalchemy import create_engine, text, func
from sqlalchemy.orm import sessionmaker
from datetime import datetime
from tabulate import tabulate

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.core.database import Base
from app.models import (
    Manga, MangaChapter, MangaPage, MangaTag, 
    MangaReadingProgress, Favorite, Comment, ContentType
)


class MangaManager:
    def __init__(self):
        self.engine = create_engine(
            settings.DATABASE_URL,
            pool_pre_ping=True,
            echo=False
        )
        Session = sessionmaker(bind=self.engine)
        self.session = Session()
    
    def list_mangas(self, limit=20, offset=0, search=None):
        """列出漫画列表"""
        query = self.session.query(Manga)
        
        if search:
            query = query.filter(Manga.title.like(f'%{search}%'))
        
        total = query.count()
        mangas = query.offset(offset).limit(limit).all()
        
        # 准备表格数据
        table_data = []
        for manga in mangas:
            # 获取章节数
            chapter_count = self.session.query(MangaChapter).filter(
                MangaChapter.manga_id == manga.id
            ).count()
            
            table_data.append([
                manga.id,
                manga.title[:40] + '...' if len(manga.title) > 40 else manga.title,
                manga.author or 'N/A',
                manga.status,
                chapter_count,
                manga.created_at.strftime('%Y-%m-%d')
            ])
        
        # 显示表格
        headers = ['ID', '标题', '作者', '状态', '章节数', '创建时间']
        print(f"\n漫画列表 (共 {total} 部):")
        print(tabulate(table_data, headers=headers, tablefmt='grid'))
        
        return mangas, total
    
    def show_manga_details(self, manga_id):
        """显示漫画详细信息"""
        manga = self.session.query(Manga).filter(Manga.id == manga_id).first()
        
        if not manga:
            print(f"\n❌ 未找到ID为 {manga_id} 的漫画")
            return None
        
        # 统计相关数据
        stats = {
            'chapters': self.session.query(MangaChapter).filter(
                MangaChapter.manga_id == manga_id
            ).count(),
            'pages': self.session.execute(
                text("""
                    SELECT COUNT(*) FROM manga_pages mp
                    JOIN manga_chapters mc ON mp.chapter_id = mc.id
                    WHERE mc.manga_id = :manga_id
                """),
                {"manga_id": manga_id}
            ).scalar(),
            'favorites': self.session.query(Favorite).filter(
                Favorite.content_type == ContentType.MANGA,
                Favorite.manga_id == manga_id
            ).count(),
            'comments': self.session.query(Comment).filter(
                Comment.content_type == ContentType.MANGA,
                Comment.manga_id == manga_id
            ).count(),
            'reading_progress': self.session.query(MangaReadingProgress).filter(
                MangaReadingProgress.manga_id == manga_id
            ).count()
        }
        
        # 显示详细信息
        print(f"\n📖 漫画详情:")
        print(f"  ID: {manga.id}")
        print(f"  标题: {manga.title}")
        print(f"  作者: {manga.author or 'N/A'}")
        print(f"  画师: {manga.artist or 'N/A'}")
        print(f"  状态: {manga.status}")
        print(f"  类型: {manga.type}")
        print(f"  创建时间: {manga.created_at}")
        print(f"  更新时间: {manga.updated_at}")
        
        print(f"\n📊 相关数据统计:")
        print(f"  章节数量: {stats['chapters']}")
        print(f"  页面总数: {stats['pages']}")
        print(f"  收藏人数: {stats['favorites']}")
        print(f"  评论数量: {stats['comments']}")
        print(f"  阅读记录: {stats['reading_progress']}")
        
        # 显示最新章节
        latest_chapters = self.session.query(MangaChapter).filter(
            MangaChapter.manga_id == manga_id
        ).order_by(MangaChapter.chapter_number.desc()).limit(5).all()
        
        if latest_chapters:
            print(f"\n📚 最新章节:")
            for ch in latest_chapters:
                print(f"  第{ch.chapter_number}话: {ch.title or '无标题'}")
        
        return manga
    
    def delete_manga(self, manga_id, confirm=True):
        """删除漫画及所有相关数据"""
        manga = self.session.query(Manga).filter(Manga.id == manga_id).first()
        
        if not manga:
            print(f"\n❌ 未找到ID为 {manga_id} 的漫画")
            return False
        
        # 显示将要删除的内容
        self.show_manga_details(manga_id)
        
        if confirm:
            response = input(f"\n⚠️  确定要删除 '{manga.title}' 及其所有相关数据吗？(yes/no): ")
            if response.lower() != 'yes':
                print("已取消删除操作")
                return False
        
        try:
            # 删除所有相关数据
            # 1. 删除页面
            self.session.execute(
                text("""
                    DELETE mp FROM manga_pages mp
                    JOIN manga_chapters mc ON mp.chapter_id = mc.id
                    WHERE mc.manga_id = :manga_id
                """),
                {"manga_id": manga_id}
            )
            
            # 2. 删除章节
            self.session.query(MangaChapter).filter(
                MangaChapter.manga_id == manga_id
            ).delete()
            
            # 3. 删除标签
            self.session.query(MangaTag).filter(
                MangaTag.manga_id == manga_id
            ).delete()
            
            # 4. 删除用户相关数据
            self.session.query(MangaReadingProgress).filter(
                MangaReadingProgress.manga_id == manga_id
            ).delete()
            
            self.session.query(Favorite).filter(
                Favorite.content_type == ContentType.MANGA,
                Favorite.manga_id == manga_id
            ).delete()
            
            self.session.query(Comment).filter(
                Comment.content_type == ContentType.MANGA,
                Comment.manga_id == manga_id
            ).delete()
            
            # 5. 删除漫画本身
            self.session.query(Manga).filter(Manga.id == manga_id).delete()
            
            self.session.commit()
            print(f"\n✅ 成功删除漫画 '{manga.title}' 及其所有相关数据")
            return True
            
        except Exception as e:
            self.session.rollback()
            print(f"\n❌ 删除失败: {str(e)}")
            return False
    
    def batch_delete(self, manga_ids):
        """批量删除漫画"""
        success_count = 0
        failed_count = 0
        
        for manga_id in manga_ids:
            print(f"\n{'='*50}")
            if self.delete_manga(manga_id, confirm=True):
                success_count += 1
            else:
                failed_count += 1
        
        print(f"\n批量删除完成:")
        print(f"  成功: {success_count}")
        print(f"  失败: {failed_count}")
    
    def interactive_mode(self):
        """交互式管理模式"""
        while True:
            print("\n" + "="*50)
            print("漫画数据管理工具")
            print("="*50)
            print("1. 列出所有漫画")
            print("2. 搜索漫画")
            print("3. 查看漫画详情")
            print("4. 删除单个漫画")
            print("5. 批量删除漫画")
            print("0. 退出")
            
            choice = input("\n请选择操作 (0-5): ")
            
            if choice == '0':
                print("再见！")
                break
            
            elif choice == '1':
                # 列出漫画
                page = 0
                limit = 20
                while True:
                    mangas, total = self.list_mangas(limit=limit, offset=page*limit)
                    
                    if (page + 1) * limit < total:
                        next_page = input("\n按Enter查看下一页，输入q返回主菜单: ")
                        if next_page.lower() == 'q':
                            break
                        page += 1
                    else:
                        input("\n已到最后一页，按Enter返回主菜单")
                        break
            
            elif choice == '2':
                # 搜索漫画
                keyword = input("请输入搜索关键字: ")
                mangas, total = self.list_mangas(search=keyword)
                if total == 0:
                    print(f"未找到包含 '{keyword}' 的漫画")
                input("\n按Enter返回主菜单")
            
            elif choice == '3':
                # 查看详情
                manga_id = input("请输入漫画ID: ")
                if manga_id.isdigit():
                    self.show_manga_details(int(manga_id))
                    input("\n按Enter返回主菜单")
                else:
                    print("请输入有效的数字ID")
            
            elif choice == '4':
                # 删除单个漫画
                manga_id = input("请输入要删除的漫画ID: ")
                if manga_id.isdigit():
                    self.delete_manga(int(manga_id))
                else:
                    print("请输入有效的数字ID")
            
            elif choice == '5':
                # 批量删除
                ids_input = input("请输入要删除的漫画ID列表（用逗号分隔）: ")
                manga_ids = []
                for id_str in ids_input.split(','):
                    id_str = id_str.strip()
                    if id_str.isdigit():
                        manga_ids.append(int(id_str))
                
                if manga_ids:
                    print(f"\n准备删除 {len(manga_ids)} 部漫画")
                    confirm = input("确定要继续吗？(yes/no): ")
                    if confirm.lower() == 'yes':
                        self.batch_delete(manga_ids)
                else:
                    print("未输入有效的ID")
    
    def close(self):
        """关闭数据库连接"""
        self.session.close()


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='漫画数据管理工具')
    parser.add_argument('--list', action='store_true', help='列出所有漫画')
    parser.add_argument('--search', type=str, help='搜索漫画')
    parser.add_argument('--details', type=int, help='显示指定ID的漫画详情')
    parser.add_argument('--delete', type=int, help='删除指定ID的漫画')
    parser.add_argument('--batch-delete', type=str, help='批量删除（ID用逗号分隔）')
    parser.add_argument('--no-confirm', action='store_true', help='跳过确认提示')
    
    args = parser.parse_args()
    
    manager = MangaManager()
    
    try:
        # 如果没有提供参数，进入交互模式
        if len(sys.argv) == 1:
            manager.interactive_mode()
        else:
            # 命令行模式
            if args.list:
                manager.list_mangas()
            
            elif args.search:
                mangas, total = manager.list_mangas(search=args.search)
                if total == 0:
                    print(f"未找到包含 '{args.search}' 的漫画")
            
            elif args.details:
                manager.show_manga_details(args.details)
            
            elif args.delete:
                manager.delete_manga(args.delete, confirm=not args.no_confirm)
            
            elif args.batch_delete:
                manga_ids = []
                for id_str in args.batch_delete.split(','):
                    id_str = id_str.strip()
                    if id_str.isdigit():
                        manga_ids.append(int(id_str))
                
                if manga_ids:
                    if args.no_confirm:
                        for manga_id in manga_ids:
                            manager.delete_manga(manga_id, confirm=False)
                    else:
                        manager.batch_delete(manga_ids)
                else:
                    print("未提供有效的ID")
    
    finally:
        manager.close()


if __name__ == "__main__":
    main()