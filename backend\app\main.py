from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from app.core.config import settings
from app.core.redis_client import redis_client
from app.api import api_router
import os

app = FastAPI(
    title="Anime Platform API",
    description="里番动漫播放平台API",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins_list,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=[
        "Accept",
        "Accept-Language", 
        "Content-Language",
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "X-CSRF-Token",
        "Cache-Control"
    ],
    expose_headers=["*"],
    max_age=3600,
)

# 自定义异常处理器，确保CORS头始终存在
@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    origin = request.headers.get("origin")
    headers = {}
    
    if origin in settings.cors_origins_list:
        headers = {
            "Access-Control-Allow-Origin": origin,
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Allow-Headers": "Accept, Accept-Language, Authorization, Cache-Control, Content-Language, Content-Type, X-CSRF-Token, X-Requested-With",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
            "Access-Control-Expose-Headers": "*",
        }
    
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail},
        headers=headers,
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    origin = request.headers.get("origin")
    headers = {}
    
    if origin in settings.cors_origins_list:
        headers = {
            "Access-Control-Allow-Origin": origin,
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Allow-Headers": "Accept, Accept-Language, Authorization, Cache-Control, Content-Language, Content-Type, X-CSRF-Token, X-Requested-With",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
            "Access-Control-Expose-Headers": "*",
        }
    
    return JSONResponse(
        status_code=422,
        content={"detail": exc.errors()},
        headers=headers,
    )

# 静态文件服务 - 用于访问本地图片和视频
static_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "claw")
if os.path.exists(static_dir):
    app.mount("/static", StaticFiles(directory=static_dir), name="static")

# 包含API路由
app.include_router(api_router, prefix="/api/v1")

# 应用启动和关闭事件
@app.on_event("startup")
async def startup_event():
    """应用启动时初始化Redis连接并设置默认配置"""
    # 初始化默认Redis配置（如果不存在）
    from app.core.database import SessionLocal
    from app.core.redis_config import RedisConfigManager
    
    db = SessionLocal()
    try:
        # 初始化默认配置
        RedisConfigManager.initialize_default_config(db)
    finally:
        db.close()
    
    # 初始化Redis连接（从数据库读取配置）
    await redis_client.init_redis()

@app.on_event("shutdown") 
async def shutdown_event():
    """应用关闭时关闭Redis连接"""
    await redis_client.close_redis()

@app.get("/")
async def root():
    return {"message": "Anime Platform API"}