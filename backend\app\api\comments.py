from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.deps import get_current_active_user
from app.crud import CommentCRUD
from app.schemas.anime import Comment, CommentCreate
from app.schemas.user import UserPublic
from app.models import User
import json

router = APIRouter()


@router.get("/anime/{anime_id}", response_model=List[Comment], summary="获取动漫评论列表（仅顶级评论）")
def list_comments(
    anime_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db),
):
    comments = CommentCRUD.list_by_anime(db, anime_id=anime_id, skip=skip, limit=limit)
    # 处理 attachments 字段
    for c in comments:
        if c.attachments:
            try:
                c.attachments = json.loads(c.attachments)  # type: ignore
            except Exception:
                c.attachments = []  # type: ignore
    return comments


@router.get("/anime/{anime_id}/threaded", response_model=List[Comment], summary="获取动漫评论列表（包含嵌套回复）")
def list_comments_with_replies(
    anime_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db),
):
    # 获取顶级评论
    top_level_comments = CommentCRUD.list_by_anime(db, anime_id=anime_id, skip=skip, limit=limit)
    
    # 为每个顶级评论递归获取回复
    for comment in top_level_comments:
        comment.replies = CommentCRUD._get_replies_recursive(db, comment.id)
        # 处理 attachments 字段
        _process_comment_attachments(comment)
    
    return top_level_comments


@router.get("/manga/{manga_id}", response_model=List[Comment], summary="获取漫画评论列表（仅顶级评论）")
def list_manga_comments(
    manga_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db),
):
    comments = CommentCRUD.list_by_manga(db, manga_id=manga_id, skip=skip, limit=limit)
    # 处理 attachments 字段
    for c in comments:
        if c.attachments:
            try:
                c.attachments = json.loads(c.attachments)  # type: ignore
            except Exception:
                c.attachments = []  # type: ignore
    return comments


@router.get("/manga/{manga_id}/threaded", response_model=List[Comment], summary="获取漫画评论列表（包含嵌套回复）")
def list_manga_comments_with_replies(
    manga_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db),
):
    # 获取顶级评论
    top_level_comments = CommentCRUD.list_by_manga(db, manga_id=manga_id, skip=skip, limit=limit)
    
    # 为每个顶级评论递归获取回复
    for comment in top_level_comments:
        comment.replies = CommentCRUD._get_replies_recursive(db, comment.id)
        # 处理 attachments 字段
        _process_comment_attachments(comment)
    
    return top_level_comments


@router.post("/manga", response_model=Comment, summary="创建漫画评论")
def create_manga_comment(
    comment: CommentCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    # 验证漫画存在
    from app.crud.manga import MangaCRUD
    manga = MangaCRUD.get_manga_basic(db, manga_id=comment.manga_id)
    if not manga:
        raise HTTPException(status_code=404, detail="漫画不存在")
    
    # 创建评论
    db_comment = CommentCRUD.create_manga_comment(db, comment=comment, user_id=current_user.id)
    # 处理 attachments 字段
    if db_comment.attachments:
        try:
            db_comment.attachments = json.loads(db_comment.attachments)  # type: ignore
        except Exception:
            db_comment.attachments = []  # type: ignore
    
    return db_comment


@router.get("/{comment_id}", response_model=Comment, summary="获取评论详情")
def get_comment(
    comment_id: int,
    db: Session = Depends(get_db),
):
    comment = CommentCRUD.get_by_id(db, comment_id)
    if not comment:
        raise HTTPException(status_code=404, detail="评论不存在")
    
    # 处理 attachments 字段
    _process_comment_attachments(comment)
    return comment


@router.get("/{comment_id}/replies", response_model=List[Comment], summary="获取评论回复列表")
def list_comment_replies(
    comment_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db),
):
    # 先检查父评论是否存在
    parent_comment = CommentCRUD.get_by_id(db, comment_id)
    if not parent_comment:
        raise HTTPException(status_code=404, detail="父评论不存在")
    
    replies = CommentCRUD.list_replies(db, parent_id=comment_id, skip=skip, limit=limit)
    
    # 处理 attachments 字段
    for reply in replies:
        _process_comment_attachments(reply)
    
    return replies


@router.post("/anime/{anime_id}", response_model=Comment, summary="发表评论")
def create_comment(
    anime_id: int,
    comment: CommentCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    if comment.anime_id != anime_id:
        raise HTTPException(status_code=400, detail="anime_id 不匹配")
    
    # 验证父评论存在（如果是回复）
    if comment.parent_id:
        parent_comment = CommentCRUD.get_by_id(db, comment.parent_id)
        if not parent_comment:
            raise HTTPException(status_code=404, detail="父评论不存在")
        
        # 确保父评论属于同一部动漫
        if parent_comment.anime_id != anime_id:
            raise HTTPException(status_code=400, detail="父评论与动漫不匹配")
    
    # 验证回复目标用户存在
    if comment.reply_to_user_id:
        target_user = db.query(User).filter(User.id == comment.reply_to_user_id).first()
        if not target_user:
            raise HTTPException(status_code=404, detail="回复目标用户不存在")
    
    # 验证引用评论存在
    if comment.quoted_comment_id:
        quoted_comment = CommentCRUD.get_by_id(db, comment.quoted_comment_id)
        if not quoted_comment:
            raise HTTPException(status_code=404, detail="引用评论不存在")
    
    created = CommentCRUD.create(
        db,
        user_id=current_user.id,
        anime_id=anime_id,
        content=comment.content,
        attachments=comment.attachments,
        parent_id=comment.parent_id,
        reply_to_user_id=comment.reply_to_user_id,
        quoted_comment_id=comment.quoted_comment_id,
        quoted_content=comment.quoted_content,
    )
    
    # 处理 attachments 字段
    _process_comment_attachments(created)
    return created


def _process_comment_attachments(comment):
    """处理评论的附件字段，将JSON字符串转为数组"""
    if comment.attachments:
        try:
            comment.attachments = json.loads(comment.attachments)  # type: ignore
        except Exception:
            comment.attachments = []  # type: ignore
    
    # 递归处理回复的附件
    if hasattr(comment, 'replies') and comment.replies:
        for reply in comment.replies:
            _process_comment_attachments(reply)


@router.delete("/{comment_id}", summary="删除自己的评论")
def delete_comment(
    comment_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    ok = CommentCRUD.soft_delete(db, comment_id=comment_id, user_id=current_user.id)
    if not ok:
        raise HTTPException(status_code=404, detail="评论不存在或无权限")
    return {"message": "删除成功"}


@router.put("/{comment_id}", response_model=Comment, summary="编辑自己的评论")
def update_comment(
    comment_id: int,
    content: str = Query(..., description="新的评论内容"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """用户编辑自己的评论"""
    if not content or len(content.strip()) == 0:
        raise HTTPException(status_code=400, detail="评论内容不能为空")
    
    if len(content) > 2000:
        raise HTTPException(status_code=400, detail="评论内容不能超过2000个字符")
    
    updated_comment = CommentCRUD.update_comment(
        db, comment_id=comment_id, user_id=current_user.id, content=content.strip()
    )
    
    if not updated_comment:
        raise HTTPException(status_code=404, detail="评论不存在或无权限编辑")
    
    # 处理 attachments 字段
    _process_comment_attachments(updated_comment)
    return updated_comment


@router.post("/{comment_id}/like", summary="点赞/反对评论")
def toggle_comment_like(
    comment_id: int,
    is_like: bool = Query(..., description="True表示点赞，False表示反对"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """切换用户对评论的点赞状态"""
    from app.crud import CommentLikeCRUD
    
    success = CommentLikeCRUD.toggle_like(
        db, comment_id=comment_id, user_id=current_user.id, is_like=is_like
    )
    
    if not success:
        raise HTTPException(status_code=404, detail="评论不存在")
    
    # 返回更新后的统计信息
    stats = CommentLikeCRUD.get_comment_like_stats(db, comment_id)
    user_status = CommentLikeCRUD.get_user_like_status(db, comment_id, current_user.id)
    
    return {
        "message": "操作成功",
        "like_count": stats["like_count"],
        "dislike_count": stats["dislike_count"],
        "user_like_status": user_status
    }


@router.get("/{comment_id}/like-status", summary="获取评论点赞状态")
def get_comment_like_status(
    comment_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """获取用户对特定评论的点赞状态和统计信息"""
    from app.crud import CommentLikeCRUD
    
    # 检查评论是否存在
    comment = CommentCRUD.get_by_id(db, comment_id)
    if not comment:
        raise HTTPException(status_code=404, detail="评论不存在")
    
    stats = CommentLikeCRUD.get_comment_like_stats(db, comment_id)
    user_status = CommentLikeCRUD.get_user_like_status(db, comment_id, current_user.id)
    
    return {
        "like_count": stats["like_count"],
        "dislike_count": stats["dislike_count"],
        "user_like_status": user_status
    }


