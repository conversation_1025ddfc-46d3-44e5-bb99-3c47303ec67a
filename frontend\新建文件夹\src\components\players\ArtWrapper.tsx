'use client';

import { useEffect, useRef, useState } from 'react';
import Artplayer from 'artplayer';
import { PlayerConfig, apiClient } from '@/lib/api';

interface Props {
  option: Omit<Artplayer['option'], 'container'>;
  style?: React.CSSProperties;
  className?: string;
  getInstance?: (art: Artplayer) => void;
}

interface VASTAd {
  mediaFiles: string[];
  duration: number;
  skipAfter?: number;
}

export default function ArtWrapper({ option, style, className, getInstance }: Props) {
  const container = useRef<HTMLDivElement | null>(null);
  const artRef = useRef<Artplayer | null>(null);
  const [playerConfig, setPlayerConfig] = useState<PlayerConfig | null>(null);

  // 获取播放器配置
  useEffect(() => {
    const fetchPlayerConfig = async () => {
      try {
        const config = await apiClient.getPlayerConfig();
        setPlayerConfig(config);
      } catch (error) {
        console.error('Failed to fetch player config:', error);
        // 使用默认配置
        setPlayerConfig({
          enable_ads: false,
          preroll_ad_url: '',
          midroll_ad_url: '',
          postroll_ad_url: '',
          enable_vast: false,
          vast_url: '',
          enable_rightclick: false,
          show_stats: false,
          show_version: false,
          skip_ad_time: 5,
          ad_volume: 0.7,
          autoplay: false,
          theme_color: '#7c3aed'
        });
      }
    };
    
    fetchPlayerConfig();
  }, []);

  // VAST广告解析函数
  const parseVAST = async (vastUrl: string): Promise<VASTAd | null> => {
    try {
      const response = await fetch(vastUrl);
      const vastText = await response.text();
      const parser = new DOMParser();
      const vastDoc = parser.parseFromString(vastText, 'text/xml');
      
      const mediaFiles = Array.from(vastDoc.querySelectorAll('MediaFile'))
        .map(file => file.textContent?.trim())
        .filter(Boolean) as string[];
      
      const duration = parseInt(vastDoc.querySelector('Duration')?.textContent || '0');
      const skipAfter = parseInt(vastDoc.querySelector('SkipOffset')?.textContent || '5');
      
      return {
        mediaFiles,
        duration,
        skipAfter
      };
    } catch (error) {
      console.error('Failed to parse VAST:', error);
      return null;
    }
  };

  // 播放广告的函数
  const playAd = (art: any, adUrl: string, isVast: boolean = false) => {
    return new Promise<void>((resolve) => {
      if (isVast) {
        // VAST广告处理
        parseVAST(adUrl).then((vastAd) => {
          if (vastAd && vastAd.mediaFiles.length > 0) {
            showAdVideo(art, vastAd.mediaFiles[0], vastAd.skipAfter || playerConfig?.skip_ad_time || 5, resolve);
          } else {
            resolve();
          }
        });
      } else {
        // 直接视频广告
        showAdVideo(art, adUrl, playerConfig?.skip_ad_time || 5, resolve);
      }
    });
  };

  // 显示广告视频的函数
  const showAdVideo = (art: any, adUrl: string, skipTime: number, onComplete: () => void) => {
    const adVideo = document.createElement('video');
    adVideo.src = adUrl;
    adVideo.volume = playerConfig?.ad_volume || 0.7;
    adVideo.style.cssText = `
      position: absolute; 
      top: 0; 
      left: 0; 
      width: 100%; 
      height: 100%; 
      z-index: 1000;
      background: #000;
      object-fit: contain;
    `;
    
    // 添加广告标识
    const adLabel = document.createElement('div');
    adLabel.textContent = '广告';
    adLabel.style.cssText = `
      position: absolute; 
      top: 20px; 
      left: 20px; 
      z-index: 1001; 
      background: rgba(255,255,0,0.8); 
      color: black; 
      padding: 4px 8px; 
      border-radius: 4px; 
      font-size: 12px;
      font-weight: bold;
    `;
    
    // 添加跳过按钮
    const skipButton = document.createElement('button');
    skipButton.style.cssText = `
      position: absolute; 
      top: 20px; 
      right: 20px; 
      z-index: 1001; 
      background: rgba(0,0,0,0.8); 
      color: white; 
      border: none; 
      padding: 8px 16px; 
      border-radius: 4px; 
      cursor: pointer; 
      font-size: 14px;
      display: none;
    `;
    
    const container = art.template.$container;
    container.appendChild(adVideo);
    container.appendChild(adLabel);
    container.appendChild(skipButton);
    
    let skipTimer = skipTime;
    let countdownInterval: NodeJS.Timeout;
    
    const updateSkipButton = () => {
      if (skipTimer <= 0) {
        skipButton.style.display = 'block';
        skipButton.textContent = '跳过广告';
        if (countdownInterval) clearInterval(countdownInterval);
      } else {
        skipButton.textContent = `跳过广告 (${skipTimer})`;
        skipTimer--;
      }
    };
    
    const endAd = () => {
      if (countdownInterval) clearInterval(countdownInterval);
      container.removeChild(adVideo);
      container.removeChild(adLabel);
      container.removeChild(skipButton);
      onComplete();
    };
    
    adVideo.addEventListener('ended', endAd);
    skipButton.addEventListener('click', endAd);
    
    adVideo.play().then(() => {
      updateSkipButton();
      countdownInterval = setInterval(updateSkipButton, 1000);
    }).catch(() => {
      endAd();
    });
  };

  useEffect(() => {
    if (!container.current || !playerConfig) return;
    
    // Clean up previous instance if exists
    if (artRef.current) {
      artRef.current.destroy(false);
      artRef.current = null;
    }
    
    try {
      // 构建播放器配置
      const artPlayerOption: any = {
        container: container.current,
        ...option,
        autoplay: playerConfig.autoplay || option.autoplay || false,
        theme: playerConfig.theme_color || option.theme || '#7c3aed',
        // 配置右键菜单
        contextmenu: playerConfig.enable_rightclick ? undefined : [],
        // 添加播放器配置
        setting: playerConfig.show_stats,
        info: playerConfig.show_version,
      };
      
      // 如果启用了广告，暂停自动播放，让广告先播放
      if (playerConfig.enable_ads && (
        playerConfig.preroll_ad_url || 
        (playerConfig.enable_vast && playerConfig.vast_url)
      )) {
        artPlayerOption.autoplay = false;
      }
      
      const art = new Artplayer(artPlayerOption);
      artRef.current = art;
      
      // 配置右键菜单
      if (!playerConfig.enable_rightclick) {
        art.on('ready', () => {
          // 阻止右键菜单显示
          art.template.$container.addEventListener('contextmenu', (e: Event) => {
            e.preventDefault();
            e.stopPropagation();
            return false;
          });
        });
      }
      
      // 广告播放逻辑
      if (playerConfig.enable_ads) {
        art.on('ready', async () => {
          // 前贴片广告
          if (playerConfig.preroll_ad_url) {
            art.pause();
            await playAd(art, playerConfig.preroll_ad_url);
            art.play();
          } else if (playerConfig.enable_vast && playerConfig.vast_url) {
            art.pause();
            await playAd(art, playerConfig.vast_url, true);
            art.play();
          }
          
          // 中插广告（在视频播放到一半时）
          if (playerConfig.midroll_ad_url) {
            art.on('video:timeupdate', () => {
              const duration = art.duration;
              const currentTime = art.currentTime;
              if (duration > 0 && Math.abs(currentTime - duration / 2) < 1) {
                art.pause();
                playAd(art, playerConfig.midroll_ad_url).then(() => {
                  art.play();
                });
              }
            });
          }
          
          // 后贴片广告
          if (playerConfig.postroll_ad_url) {
            art.on('video:ended', async () => {
              await playAd(art, playerConfig.postroll_ad_url);
            });
          }
        });
      }
      
      // Add error handling for video loading
      art.on('error', (error) => {
        console.error('ArtPlayer error:', error);
      });
      
      art.on('ready', () => {
        console.log('ArtPlayer ready');
        
        // Call getInstance callback if provided
        if (getInstance) {
          getInstance(art);
        }
      });
      
    } catch (error) {
      console.error('Failed to initialize ArtPlayer:', error);
    }
    
    return () => {
      if (artRef.current) {
        artRef.current.destroy(false);
        artRef.current = null;
      }
    };
  }, [option?.url, option?.poster, playerConfig]);

  return (
    <div 
      ref={container} 
      style={{ 
        width: '100%', 
        height: '100%',
        minHeight: '300px',
        ...style 
      }} 
      className={className}
    />
  );
}


