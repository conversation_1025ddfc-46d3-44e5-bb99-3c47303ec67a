#!/usr/bin/env python3
"""
检查数据库中的同人志数据
"""

import sys
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.models import Manga

def check_doujinshi_data():
    """检查数据库中的同人志数据"""
    engine = create_engine(
        settings.DATABASE_URL,
        pool_pre_ping=True,
        echo=False
    )
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # 检查所有manga_type的分布
        print("=== 检查manga_type字段分布 ===")
        result = session.execute(text("""
            SELECT manga_type, COUNT(*) as count 
            FROM mangas 
            GROUP BY manga_type 
            ORDER BY count DESC
        """))
        
        for row in result:
            print(f"manga_type: {row.manga_type}, 数量: {row.count}")
        
        # 检查是否有doujinshi类型的数据
        print("\n=== 检查doujinshi类型数据 ===")
        doujinshi_count = session.query(Manga).filter(Manga.manga_type == 'doujinshi').count()
        print(f"doujinshi类型漫画数量: {doujinshi_count}")
        
        if doujinshi_count > 0:
            print("\n前10个doujinshi类型的漫画:")
            doujinshi_mangas = session.query(Manga).filter(Manga.manga_type == 'doujinshi').limit(10).all()
            for manga in doujinshi_mangas:
                print(f"ID: {manga.id}, 标题: {manga.title}, 类型: {manga.manga_type}")
        else:
            print("没有找到doujinshi类型的漫画")
            
        # 检查NULL或空值的manga_type
        print("\n=== 检查NULL或空值的manga_type ===")
        null_count = session.execute(text("""
            SELECT COUNT(*) as count 
            FROM mangas 
            WHERE manga_type IS NULL OR manga_type = ''
        """)).scalar()
        print(f"manga_type为NULL或空值的数量: {null_count}")
        
    except Exception as e:
        print(f"查询出错: {e}")
    finally:
        session.close()

if __name__ == "__main__":
    check_doujinshi_data()
