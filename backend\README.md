#!/usr/bin/env python3
"""
重新构建里番动漫播放平台后端
"""

完成的后端系统已构建，包含以下功能：

## ✅ 核心功能
- **用户系统**: 注册、登录、JWT认证
- **动漫管理**: 动漫列表、搜索、详情、播放
- **收藏功能**: 用户可收藏/取消收藏动漫

## ✅ 技术特性  
- FastAPI框架 + MySQL数据库
- JWT身份认证
- CORS跨域支持  
- RESTful API设计
- 分页查询支持

## 🗃️ 数据库配置
- 主机: 192.168.1.7
- 数据库: hentai  
- 用户: root / 123456

## 🚀 启动方式
```bash
cd backend
python start.py
```

## 📡 API端点

### 认证相关
- POST `/api/v1/auth/register` - 用户注册
- POST `/api/v1/auth/login` - 用户登录
- GET `/api/v1/auth/me` - 获取当前用户信息

### 动漫相关  
- GET `/api/v1/animes/` - 获取动漫列表
- GET `/api/v1/animes/search?title=关键词` - 搜索动漫
- GET `/api/v1/animes/{id}` - 获取动漫详情
- POST `/api/v1/animes/` - 创建动漫（管理员）
- PUT `/api/v1/animes/{id}` - 更新动漫（管理员）

### 收藏相关
- GET `/api/v1/favorites/` - 获取用户收藏
- POST `/api/v1/favorites/` - 添加收藏  
- DELETE `/api/v1/favorites/{anime_id}` - 取消收藏
- GET `/api/v1/favorites/{anime_id}/check` - 检查收藏状态

## 🧪 API文档
启动后访问: http://localhost:8000/docs

## ⚠️ 注意事项
如遇到数据库表结构问题，请运行：
```bash
python recreate_db.py
python create_sample_data.py
```

系统已经完全可用，支持用户注册登录和动漫收藏播放功能！