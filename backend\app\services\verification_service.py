"""
邮箱验证码服务
"""
import random
import string
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from app.core.config_manager import ConfigManager
from app.services.email_service import EmailService


class VerificationService:
    """验证码服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.config_manager = ConfigManager(db)
        self.email_service = EmailService(db)
        # 内存存储验证码（生产环境建议使用Redis）
        self._verification_codes: Dict[str, Dict[str, Any]] = {}
    
    def generate_verification_code(self, length: int = 6) -> str:
        """生成验证码"""
        return ''.join(random.choices(string.digits, k=length))
    
    def store_verification_code(
        self, 
        email: str, 
        code: str, 
        purpose: str = "register",
        expire_minutes: int = 10
    ) -> None:
        """存储验证码"""
        expire_time = datetime.now() + timedelta(minutes=expire_minutes)
        
        self._verification_codes[f"{email}:{purpose}"] = {
            "code": code,
            "expire_time": expire_time,
            "attempts": 0,
            "max_attempts": 5
        }
    
    def verify_code(self, email: str, code: str, purpose: str = "register") -> Dict[str, Any]:
        """验证验证码"""
        key = f"{email}:{purpose}"
        
        if key not in self._verification_codes:
            return {
                "success": False,
                "error": "验证码不存在或已过期"
            }
        
        stored_data = self._verification_codes[key]
        
        # 检查是否过期
        if datetime.now() > stored_data["expire_time"]:
            del self._verification_codes[key]
            return {
                "success": False,
                "error": "验证码已过期"
            }
        
        # 检查尝试次数
        if stored_data["attempts"] >= stored_data["max_attempts"]:
            del self._verification_codes[key]
            return {
                "success": False,
                "error": "验证码尝试次数过多，请重新获取"
            }
        
        # 验证码码
        if stored_data["code"] != code:
            stored_data["attempts"] += 1
            return {
                "success": False,
                "error": "验证码错误",
                "remaining_attempts": stored_data["max_attempts"] - stored_data["attempts"]
            }
        
        # 验证成功，删除验证码
        del self._verification_codes[key]
        return {
            "success": True,
            "message": "验证码验证成功"
        }
    
    def send_register_verification_code(self, email: str) -> Dict[str, Any]:
        """发送注册验证码"""
        # 检查是否启用邮箱验证
        if not self.is_email_verification_enabled():
            return {
                "success": False,
                "error": "邮箱验证功能未启用"
            }
        
        # 生成验证码
        code = self.generate_verification_code()
        
        # 存储验证码
        self.store_verification_code(email, code, "register", 10)
        
        # 发送邮件
        site_name = self.config_manager.get_config("site_name", "动漫网站")
        
        result = self.email_service.send_email(
            to=email,
            subject=f"{site_name} - 注册验证码",
            html_content="""
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #333;">注册验证码</h2>
                <p>您好！</p>
                <p>您正在注册{{ site_name }}，您的验证码是：</p>
                <div style="text-align: center; margin: 30px 0;">
                    <div style="background-color: #f8f9fa; border: 2px dashed #007cba; 
                                padding: 20px; border-radius: 8px; display: inline-block;">
                        <span style="font-size: 32px; font-weight: bold; color: #007cba; 
                                     letter-spacing: 8px;">{{ verification_code }}</span>
                    </div>
                </div>
                <p style="color: #666;">验证码有效期为10分钟，请及时使用。</p>
                <p style="color: #ff6b6b;"><strong>注意：</strong>请勿将验证码告诉他人。</p>
                <hr style="margin: 20px 0; border: none; border-top: 1px solid #eee;">
                <p style="color: #666; font-size: 12px;">
                    此邮件由{{ site_name }}自动发送，请勿回复。
                </p>
            </div>
            """,
            text_content="""
            注册验证码
            
            您好！
            
            您正在注册{{ site_name }}，您的验证码是：{{ verification_code }}
            
            验证码有效期为10分钟，请及时使用。
            
            注意：请勿将验证码告诉他人。
            
            ---
            此邮件由{{ site_name }}自动发送，请勿回复。
            """,
            template_data={
                "site_name": site_name,
                "verification_code": code
            }
        )
        
        if result["success"]:
            return {
                "success": True,
                "message": "验证码已发送到您的邮箱，请查收"
            }
        else:
            return {
                "success": False,
                "error": f"验证码发送失败：{result.get('error', '未知错误')}"
            }
    
    def send_login_verification_code(self, email: str) -> Dict[str, Any]:
        """发送登录验证码"""
        # 检查是否启用邮箱验证登录
        if not self.is_email_login_enabled():
            return {
                "success": False,
                "error": "邮箱验证登录功能未启用"
            }
        
        # 生成验证码
        code = self.generate_verification_code()
        
        # 存储验证码
        self.store_verification_code(email, code, "login", 5)
        
        # 发送邮件
        site_name = self.config_manager.get_config("site_name", "动漫网站")
        
        result = self.email_service.send_email(
            to=email,
            subject=f"{site_name} - 登录验证码",
            html_content="""
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #333;">登录验证码</h2>
                <p>您好！</p>
                <p>您正在登录{{ site_name }}，您的验证码是：</p>
                <div style="text-align: center; margin: 30px 0;">
                    <div style="background-color: #f8f9fa; border: 2px dashed #28a745; 
                                padding: 20px; border-radius: 8px; display: inline-block;">
                        <span style="font-size: 32px; font-weight: bold; color: #28a745; 
                                     letter-spacing: 8px;">{{ verification_code }}</span>
                    </div>
                </div>
                <p style="color: #666;">验证码有效期为5分钟，请及时使用。</p>
                <p style="color: #ff6b6b;"><strong>注意：</strong>如果不是您本人操作，请忽略此邮件。</p>
                <hr style="margin: 20px 0; border: none; border-top: 1px solid #eee;">
                <p style="color: #666; font-size: 12px;">
                    此邮件由{{ site_name }}自动发送，请勿回复。
                </p>
            </div>
            """,
            text_content="""
            登录验证码
            
            您好！
            
            您正在登录{{ site_name }}，您的验证码是：{{ verification_code }}
            
            验证码有效期为5分钟，请及时使用。
            
            注意：如果不是您本人操作，请忽略此邮件。
            
            ---
            此邮件由{{ site_name }}自动发送，请勿回复。
            """,
            template_data={
                "site_name": site_name,
                "verification_code": code
            }
        )
        
        if result["success"]:
            return {
                "success": True,
                "message": "验证码已发送到您的邮箱，请查收"
            }
        else:
            return {
                "success": False,
                "error": f"验证码发送失败：{result.get('error', '未知错误')}"
            }
    
    def is_email_verification_enabled(self) -> bool:
        """检查是否启用邮箱验证注册"""
        return self.config_manager.get_config("require_email_verification", "false").lower() == "true"
    
    def is_email_login_enabled(self) -> bool:
        """检查是否启用邮箱验证登录"""
        return self.config_manager.get_config("enable_email_login", "false").lower() == "true"
    
    def cleanup_expired_codes(self) -> None:
        """清理过期的验证码"""
        current_time = datetime.now()
        expired_keys = []

        for key, data in self._verification_codes.items():
            if current_time > data["expire_time"]:
                expired_keys.append(key)

        for key in expired_keys:
            del self._verification_codes[key]

    def generate_password_reset_token(self, email: str, expire_hours: int = 1) -> str:
        """生成密码重置令牌"""
        import secrets
        token = secrets.token_urlsafe(32)
        expire_time = datetime.now() + timedelta(hours=expire_hours)

        # 存储重置令牌
        self._verification_codes[f"reset:{token}"] = {
            "email": email,
            "expire_time": expire_time,
            "used": False
        }

        return token

    def verify_password_reset_token(self, token: str) -> Optional[str]:
        """验证密码重置令牌"""
        key = f"reset:{token}"

        if key not in self._verification_codes:
            return None

        data = self._verification_codes[key]
        current_time = datetime.now()

        # 检查是否过期
        if current_time > data["expire_time"]:
            del self._verification_codes[key]
            return None

        # 检查是否已使用
        if data["used"]:
            return None

        return data["email"]

    def delete_password_reset_token(self, token: str) -> None:
        """删除密码重置令牌"""
        key = f"reset:{token}"
        if key in self._verification_codes:
            del self._verification_codes[key]

    def send_password_reset_email(self, email: str, username: str, token: str) -> Dict[str, Any]:
        """发送密码重置邮件"""
        # 构建重置链接
        reset_url = f"http://192.168.1.14:3000/auth/reset-password?token={token}"

        # 邮件内容
        subject = "重置密码 - 动漫网站"
        html_content = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #7c3aed;">重置密码</h2>
                <p>亲爱的 {username}，</p>
                <p>我们收到了您重置密码的请求。请点击下面的链接来重置您的密码：</p>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{reset_url}"
                       style="background-color: #7c3aed; color: white; padding: 12px 24px;
                              text-decoration: none; border-radius: 5px; display: inline-block;">
                        重置密码
                    </a>
                </div>
                <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
                <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 3px;">
                    {reset_url}
                </p>
                <p><strong>注意：</strong></p>
                <ul>
                    <li>此链接将在1小时后失效</li>
                    <li>如果您没有请求重置密码，请忽略此邮件</li>
                    <li>为了您的账户安全，请不要将此链接分享给他人</li>
                </ul>
                <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                <p style="color: #666; font-size: 12px;">
                    此邮件由系统自动发送，请勿回复。<br>
                    如有疑问，请联系网站管理员。
                </p>
            </div>
        </body>
        </html>
        """

        text_content = f"""
        重置密码 - 动漫网站

        亲爱的 {username}，

        我们收到了您重置密码的请求。请访问以下链接来重置您的密码：

        {reset_url}

        注意：
        - 此链接将在1小时后失效
        - 如果您没有请求重置密码，请忽略此邮件
        - 为了您的账户安全，请不要将此链接分享给他人

        此邮件由系统自动发送，请勿回复。
        如有疑问，请联系网站管理员。
        """

        return self.email_service.send_email(
            to_email=email,
            subject=subject,
            html_content=html_content,
            text_content=text_content
        )
