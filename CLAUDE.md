# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个全栈动漫和漫画网站，包含用户系统、内容管理、评论系统和阅读功能。使用 Next.js + FastAPI + MySQL 架构，具备移动端响应式设计和夜间模式。

### 核心功能
- **动漫播放**: 视频播放，评论互动，收藏功能
- **漫画阅读**: 章节导航，书签系统，阅读进度同步
- **用户系统**: JWT认证，个人收藏，阅读历史
- **管理后台**: 内容管理，系统配置，特色推荐

## 开发环境设置

### 后端启动
```bash
cd backend
pip install -r requirements.txt
python start.py  # 开发环境: http://localhost:8000
```

### 前端启动
```bash
cd frontend
npm install
npm run dev  # 开发环境: http://localhost:3000
npm run build  # 生产构建
npm run start  # 生产启动
```

### 数据库管理
```bash
cd backend
python recreate_db.py        # 重建数据库结构
python create_sample_data.py # 创建示例数据
alembic upgrade head         # 应用数据库迁移
```

### 生产部署
```bash
# Windows
.\deploy-production.bat

# Linux/Mac  
./deploy-production.sh

# Docker部署
docker-compose -f docker-compose.prod.yml up -d
```

## 技术架构

### 后端技术栈 (/backend)
- **FastAPI**: 主要框架，自动生成 API 文档 (http://localhost:8000/docs)
- **SQLAlchemy**: ORM，数据库模型定义在 `app/models/__init__.py`
- **Alembic**: 数据库迁移工具，版本控制在 `alembic/versions/`
- **JWT**: 用户认证，安全配置在 `app/core/security.py`
- **MySQL**: 主数据库 (默认: ***********:3306/hentai)

### 前端技术栈 (/frontend)
- **Next.js 15**: React 框架，App Router 架构
- **TailwindCSS**: UI样式框架，配置在 `tailwind.config.ts`
- **Radix UI**: 无障碍组件库，组件在 `src/components/ui/`
- **TypeScript**: 类型定义在 `src/lib/api.ts`
- **Context API**: 状态管理 (认证/主题)，在 `src/contexts/`

### 数据库设计
主要模型关系：
- **User** ↔ **Favorite** ↔ **Anime/Manga**: 用户收藏系统
- **User** ↔ **Comment** ↔ **Anime/Manga**: 评论系统
- **Manga** → **MangaChapter** → **MangaPage**: 漫画章节结构
- **User** ↔ **MangaReadingProgress**: 阅读进度追踪

## API 接口规范

### 认证相关
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录 
- `GET /api/v1/auth/me` - 获取当前用户信息

### 动漫相关
- `GET /api/v1/animes/` - 获取动漫列表 (支持分页/搜索/排序)
- `GET /api/v1/animes/{id}` - 获取动漫详情
- `POST /api/v1/animes/{id}/view` - 增加播放次数
- `GET /api/v1/animes/featured` - 获取特色推荐

### 漫画相关
- `GET /api/v1/manga/` - 获取漫画列表
- `GET /api/v1/manga/{id}` - 获取漫画详情
- `GET /api/v1/manga/{id}/chapters` - 获取章节列表
- `GET /api/v1/manga/chapters/{chapter_id}/pages` - 获取页面列表
- `POST /api/v1/manga/{id}/reading-progress` - 更新阅读进度

### 收藏和书签
- `POST /api/v1/favorites/` - 添加收藏
- `GET /api/v1/favorites/` - 获取收藏列表
- `POST /api/v1/manga/bookmarks` - 添加书签
- `GET /api/v1/manga/bookmarks` - 获取书签列表

## 项目文件结构

### 关键配置文件
- `frontend/next.config.ts` - Next.js配置，包含生产优化
- `backend/app/core/config.py` - 后端配置，数据库连接
- `docker-compose.prod.yml` - 生产环境容器编排
- `frontend/.env.production` - 生产环境变量

### 核心业务逻辑
- `frontend/src/lib/api.ts` - API客户端，类型定义 (1062行核心文件)
- `backend/app/api/` - API路由实现，按功能模块划分
- `frontend/src/contexts/` - 全局状态管理 (认证/主题)
- `backend/app/crud/` - 数据库操作层

### UI组件体系
- `frontend/src/components/ui/` - 基础UI组件 (基于Radix UI)
- `frontend/src/components/anime/` - 动漫特定组件
- `frontend/src/app/` - 页面组件 (App Router结构)

## 环境配置

### 开发环境变量
```env
# backend/.env
DATABASE_URL=mysql://root:123456@***********:3306/hentai
JWT_SECRET=your-jwt-secret-key

# frontend/.env.development  
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_DISABLE_DEVTOOLS=false
```

### 生产环境变量
```env
# frontend/.env.production
NODE_ENV=production
NEXT_PUBLIC_API_BASE_URL=http://your-backend-domain.com
NEXT_PUBLIC_DISABLE_DEVTOOLS=true
NEXT_PUBLIC_DISABLE_CONSOLE=true
```

## 开发规范

### 数据库操作
- 使用 Alembic 进行 schema 变更：`alembic revision --autogenerate -m "description"`
- 模型定义遵循 SQLAlchemy 规范，在 `backend/app/models/__init__.py`
- 外键关系使用 `relationship()` 定义，注意 `back_populates` 配对

### API开发
- 新增 API 路由按功能模块添加到 `backend/app/api/`
- 使用 Pydantic schemas 进行输入验证，定义在 `backend/app/schemas/`
- 认证保护的端点使用 `deps.get_current_user` 依赖

### 前端开发
- 组件使用 TypeScript，接口定义在 `frontend/src/lib/api.ts`
- 状态管理优先使用 React Context，避免不必要的全局状态
- 移动端优先设计，使用 TailwindCSS 响应式类名

### 漫画阅读器特性
- 懒加载图片，预加载相邻页面
- 阅读进度实时同步 (防抖5秒更新)
- 游客进度仅存浏览器，登录用户同步服务器
- 支持书签功能，快速导航到收藏页面

## 漫画阅读器现代化改进

### 已完成的改进
- ✅ CSS动画效果已添加到 `frontend/src/app/globals.css`
- ✅ 构建错误修复 - JSX语法和TypeScript类型问题已解决

### 当前待实现功能 (优先级排序)

#### 高优先级
1. **重新设计进度条** - 将底部进度条改为顶部极细线条，减少阅读干扰
2. **实现UI自动显隐机制** - 点击显示UI，3秒后自动隐藏，提供沉浸式阅读体验
3. **移除全屏时的固定快捷键提示** - 减少视觉干扰，优化阅读体验

#### 中优先级
4. **添加沉浸模式切换功能** - 提供完全沉浸式的阅读模式
5. **工具栏毛玻璃效果和悬浮设计** - 使用现代化的玻璃形态效果
6. **优化移动端交互体验** - 改进触摸手势和响应性

### 开发注意事项
- **渐进式改进**: 每次只修改一小部分，避免引入复杂的语法错误
- **构建检查**: 每次修改后运行 `npm run build` 确保无构建错误
- **类型安全**: 确保TypeScript类型定义正确，特别是useRef初始值
- **错误恢复**: 如遇复杂语法错误，使用 `git restore <file>` 恢复后重新开始

### 关键文件
- `frontend/src/app/manga/[id]/chapter/[chapterId]/page.tsx` - 漫画阅读器主组件
- `frontend/src/app/globals.css` - 现代化样式和动画效果

## 待实现功能

### 书签系统
- 添加书签按钮，保存当前阅读位置
- 书签管理页面，快速跳转
- 书签备注功能，个人标记

### 性能优化
- 图片懒加载与预加载策略
- 减少加载时间，优化用户体验

## 常见问题

### 数据库连接失败
检查 `backend/app/core/config.py` 中的数据库配置，确保 MySQL 服务运行正常。

### 前端API调用失败
验证 `frontend/.env.development` 中的 `NEXT_PUBLIC_API_BASE_URL` 配置正确。

### 生产部署问题
确保环境变量配置正确，特别是 API URL 和数据库连接字符串。

### 爬虫数据导入
使用 `backend/claw/` 目录下的爬虫工具，参考 `QUICKSTART.md` 和 `CONFIGURATION.md`。