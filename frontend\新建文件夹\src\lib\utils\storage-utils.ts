/**
 * 安全的本地存储操作
 */

/**
 * 设置本地存储项
 */
export function setStorageItem(key: string, value: any): boolean {
  try {
    if (typeof window === 'undefined') return false;
    
    const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
    localStorage.setItem(key, serializedValue);
    return true;
  } catch (error) {
    console.error('Failed to set storage item:', error);
    return false;
  }
}

/**
 * 获取本地存储项
 */
export function getStorageItem<T = string>(key: string, defaultValue?: T): T | null {
  try {
    if (typeof window === 'undefined') return defaultValue || null;
    
    const item = localStorage.getItem(key);
    if (item === null) return defaultValue || null;
    
    // 尝试解析JSON，如果失败则返回原始字符串
    try {
      return JSON.parse(item);
    } catch {
      return item as unknown as T;
    }
  } catch (error) {
    console.error('Failed to get storage item:', error);
    return defaultValue || null;
  }
}

/**
 * 移除本地存储项
 */
export function removeStorageItem(key: string): boolean {
  try {
    if (typeof window === 'undefined') return false;
    
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error('Failed to remove storage item:', error);
    return false;
  }
}

/**
 * 清空本地存储
 */
export function clearStorage(): boolean {
  try {
    if (typeof window === 'undefined') return false;
    
    localStorage.clear();
    return true;
  } catch (error) {
    console.error('Failed to clear storage:', error);
    return false;
  }
}

/**
 * 检查本地存储是否可用
 */
export function isStorageAvailable(): boolean {
  try {
    if (typeof window === 'undefined') return false;
    
    const testKey = '__storage_test__';
    localStorage.setItem(testKey, 'test');
    localStorage.removeItem(testKey);
    return true;
  } catch {
    return false;
  }
}

/**
 * 获取本地存储使用情况
 */
export function getStorageUsage(): { used: number; total: number; percentage: number } {
  try {
    if (typeof window === 'undefined') {
      return { used: 0, total: 0, percentage: 0 };
    }
    
    let used = 0;
    for (const key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        used += localStorage[key].length + key.length;
      }
    }
    
    // 大多数浏览器的localStorage限制为5MB
    const total = 5 * 1024 * 1024;
    const percentage = (used / total) * 100;
    
    return { used, total, percentage };
  } catch {
    return { used: 0, total: 0, percentage: 0 };
  }
}

/**
 * 带过期时间的存储
 */
export function setStorageItemWithExpiry(key: string, value: any, expiryInMinutes: number): boolean {
  try {
    const now = new Date();
    const item = {
      value,
      expiry: now.getTime() + expiryInMinutes * 60 * 1000,
    };
    
    return setStorageItem(key, item);
  } catch (error) {
    console.error('Failed to set storage item with expiry:', error);
    return false;
  }
}

/**
 * 获取带过期时间的存储项
 */
export function getStorageItemWithExpiry<T = any>(key: string): T | null {
  try {
    const item = getStorageItem(key);
    if (!item) return null;
    
    const now = new Date();
    if (now.getTime() > item.expiry) {
      removeStorageItem(key);
      return null;
    }
    
    return item.value;
  } catch (error) {
    console.error('Failed to get storage item with expiry:', error);
    return null;
  }
}

/**
 * 会话存储操作
 */
export const sessionStorage = {
  setItem: (key: string, value: any): boolean => {
    try {
      if (typeof window === 'undefined') return false;
      
      const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
      window.sessionStorage.setItem(key, serializedValue);
      return true;
    } catch (error) {
      console.error('Failed to set session storage item:', error);
      return false;
    }
  },
  
  getItem: <T = string>(key: string, defaultValue?: T): T | null => {
    try {
      if (typeof window === 'undefined') return defaultValue || null;
      
      const item = window.sessionStorage.getItem(key);
      if (item === null) return defaultValue || null;
      
      try {
        return JSON.parse(item);
      } catch {
        return item as unknown as T;
      }
    } catch (error) {
      console.error('Failed to get session storage item:', error);
      return defaultValue || null;
    }
  },
  
  removeItem: (key: string): boolean => {
    try {
      if (typeof window === 'undefined') return false;
      
      window.sessionStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('Failed to remove session storage item:', error);
      return false;
    }
  },
  
  clear: (): boolean => {
    try {
      if (typeof window === 'undefined') return false;
      
      window.sessionStorage.clear();
      return true;
    } catch (error) {
      console.error('Failed to clear session storage:', error);
      return false;
    }
  },
};
