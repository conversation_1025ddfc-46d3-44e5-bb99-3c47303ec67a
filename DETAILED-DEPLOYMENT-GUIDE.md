# 📖 详细前后端部署教程

## 🎯 部署概述

这是一份完整的生产环境部署指南，涵盖了从文件清理到服务上线的全过程。

---

## 📋 部署前检查清单

### ✅ 环境要求
- [ ] 服务器已准备就绪（Linux/Windows）
- [ ] Node.js 18.0+ 已安装
- [ ] Python 3.8+ 已安装  
- [ ] 数据库服务可访问（MariaDB/MySQL）
- [ ] 域名解析已配置（如使用域名）

### ✅ 文件准备
- [ ] 项目代码已下载到服务器
- [ ] 运行清理脚本清理不必要文件
- [ ] 环境变量文件已配置

---

## 🧹 第一步：清理项目文件

### Linux/Mac 系统
```bash
# 进入项目目录
cd /path/to/anime-website

# 给脚本执行权限
chmod +x cleanup-for-production.sh

# 运行清理脚本
./cleanup-for-production.sh
```

### Windows 系统
```cmd
# 进入项目目录
cd C:\path\to\anime-website

# 运行清理脚本
cleanup-for-production.bat
```

### 清理结果
脚本将删除以下文件：
- 🗑️ 测试文件：`*.test.js`, `*.spec.ts`, `jest.config.js` 等
- 🗑️ 开发工具：`.vscode/`, `.idea/`, `.DS_Store` 等
- 🗑️ Docker 文件：`docker-compose.yml`, `Dockerfile` 等
- 🗑️ 缓存文件：`__pycache__/`, `node_modules/.cache/` 等
- 🗑️ 开发脚本：`create_sample_data.py`, `debug_*.py` 等

---

## ⚙️ 第二步：环境配置

### 前端环境配置

#### 1. 创建生产环境变量文件
```bash
# frontend/.env.production
NODE_ENV=production
NEXT_PUBLIC_API_BASE_URL=http://YOUR_SERVER_IP:8000
NEXT_PUBLIC_DISABLE_DEVTOOLS=true
NEXT_PUBLIC_DISABLE_CONSOLE=true
```

#### 2. 修复跨域配置
编辑 `frontend/next.config.ts`：
```typescript
const nextConfig: NextConfig = {
  // 🔧 修复跨域问题 - 添加您的服务器IP
  allowedDevOrigins: [
    '*************:3000',  // 替换为您的服务器IP
    'localhost:3000',
    'your-domain.com:3000' // 如果有域名，替换为您的域名
  ],
  
  // 生产环境优化
  poweredByHeader: false,
  compress: true,
  
  // 构建配置
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: false,
  },
  
  // 图片优化
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
      {
        protocol: 'http',
        hostname: '*************', // 替换为您的服务器IP
      },
    ],
  },
  
  // 输出配置
  output: 'standalone',
};
```

### 后端环境配置

#### 1. 创建生产环境变量文件
```bash
# backend/.env.production
ENVIRONMENT=production

# 数据库配置（使用您的云数据库）
DATABASE_URL=mysql+pymysql://sql23721_hentai:507877550%40lihao@************:3306/sql23721_hentai?charset=utf8mb4

# JWT 配置
SECRET_KEY=your-super-secure-production-secret-key-change-me-now
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS 配置 - 添加您的服务器IP和域名
CORS_ORIGINS=http://*************:3000,https://your-domain.com,http://localhost:3000

# 数据库连接池配置
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_RECYCLE=3600
DB_POOL_PRE_PING=true

# 日志配置
DEBUG=false
LOG_LEVEL=INFO
```

---

## 🚀 第三步：后端部署

### 1. 系统环境准备

#### Ubuntu/Debian 系统
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装 Python 和必要工具
sudo apt install python3 python3-pip python3-venv -y

# 验证安装
python3 --version
pip3 --version
```

#### CentOS/RHEL 系统
```bash
# 更新系统
sudo yum update -y

# 安装 Python 和必要工具
sudo yum install python3 python3-pip -y

# 验证安装
python3 --version
pip3 --version
```

### 2. 后端应用部署

```bash
# 1. 进入后端目录
cd backend

# 2. 创建虚拟环境
python3 -m venv venv

# 3. 激活虚拟环境
# Linux/Mac:
source venv/bin/activate
# Windows:
# venv\Scripts\activate

# 4. 升级 pip
pip install --upgrade pip

# 5. 安装依赖
pip install -r requirements.txt

# 6. 复制环境配置
cp .env.example .env.production
# 编辑 .env.production 填入实际配置

# 7. 运行数据库迁移
export $(grep -v '^#' .env.production | xargs)  # 加载环境变量
alembic upgrade head

# 8. 测试后端服务
python start.py
```

### 3. 配置系统服务（推荐）

创建 systemd 服务文件：
```bash
sudo nano /etc/systemd/system/anime-backend.service
```

```ini
[Unit]
Description=Anime Platform Backend
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/anime-website/backend
Environment=PATH=/path/to/anime-website/backend/venv/bin
EnvironmentFile=/path/to/anime-website/backend/.env.production
ExecStart=/path/to/anime-website/backend/venv/bin/python start.py
Restart=always
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=anime-backend

[Install]
WantedBy=multi-user.target
```

启用并启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable anime-backend
sudo systemctl start anime-backend
sudo systemctl status anime-backend
```

---

## 🌐 第四步：前端部署

### 1. Node.js 环境准备

#### 方法一：使用 NodeSource 仓库（推荐）
```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# CentOS/RHEL
curl -fsSL https://rpm.nodesource.com/setup_20.x | sudo bash -
sudo yum install -y nodejs
```

#### 方法二：使用 NVM
```bash
# 安装 NVM
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc

# 安装和使用 Node.js 20
nvm install 20
nvm use 20
nvm alias default 20
```

### 2. 前端应用部署

```bash
# 1. 进入前端目录
cd frontend

# 2. 安装依赖（生产环境）
npm ci --only=production

# 3. 复制环境配置
cp .env.example .env.production
# 编辑 .env.production 填入实际配置

# 4. 构建生产版本
npm run build

# 5. 测试前端服务
npm run start
```

### 3. 配置前端系统服务

创建 systemd 服务文件：
```bash
sudo nano /etc/systemd/system/anime-frontend.service
```

```ini
[Unit]
Description=Anime Platform Frontend
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/anime-website/frontend
ExecStart=/usr/bin/npm run start
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3000
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=anime-frontend

[Install]
WantedBy=multi-user.target
```

启用并启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable anime-frontend
sudo systemctl start anime-frontend
sudo systemctl status anime-frontend
```

---

## 🔧 第五步：Nginx 反向代理配置（推荐）

### 1. 安装 Nginx
```bash
# Ubuntu/Debian
sudo apt install nginx -y

# CentOS/RHEL
sudo yum install nginx -y
```

### 2. 配置 Nginx
创建站点配置：
```bash
sudo nano /etc/nginx/sites-available/anime-platform
```

```nginx
server {
    listen 80;
    server_name your-domain.com *************;  # 替换为您的域名和IP
    
    # 设置客户端最大上传大小
    client_max_body_size 100M;
    
    # 前端应用
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 后端 API
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS 头部（如果后端未设置）
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS, PUT, DELETE';
        add_header Access-Control-Allow-Headers 'Origin, Content-Type, Accept, Authorization';
        
        # 预检请求处理
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
    
    # 静态文件
    location /static/ {
        proxy_pass http://localhost:8000;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # API 文档
    location /docs {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 安全头部
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
}
```

启用站点：
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/anime-platform /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### 3. 配置防火墙
```bash
# Ubuntu/Debian (ufw)
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 22

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

---

## 🔒 第六步：SSL 证书配置（HTTPS）

### 使用 Let's Encrypt（免费）

```bash
# 1. 安装 Certbot
# Ubuntu/Debian
sudo apt install certbot python3-certbot-nginx -y

# CentOS/RHEL
sudo yum install certbot python3-certbot-nginx -y

# 2. 获取证书
sudo certbot --nginx -d your-domain.com

# 3. 测试自动更新
sudo certbot renew --dry-run
```

### 手动上传证书（如果有购买的证书）
```bash
# 1. 将证书文件上传到服务器
# your-domain.com.crt (证书文件)
# your-domain.com.key (私钥文件)

# 2. 修改 Nginx 配置添加 SSL
sudo nano /etc/nginx/sites-available/anime-platform
```

在 Nginx 配置中添加：
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/your-domain.com.crt;
    ssl_certificate_key /path/to/your-domain.com.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # ... 其他配置保持不变
}

# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

---

## 📊 第七步：监控和日志

### 1. 服务日志查看
```bash
# 查看后端服务日志
sudo journalctl -u anime-backend -f

# 查看前端服务日志  
sudo journalctl -u anime-frontend -f

# 查看 Nginx 日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 2. 设置日志轮转
创建日志轮转配置：
```bash
sudo nano /etc/logrotate.d/anime-platform
```

```
/var/log/nginx/*log {
    daily
    missingok
    rotate 14
    compress
    notifempty
    create 0640 www-data adm
    sharedscripts
    prerotate
        if [ -d /etc/logrotate.d/httpd-prerotate ]; then \
            run-parts /etc/logrotate.d/httpd-prerotate; \
        fi \
    endscript
    postrotate
        systemctl reload nginx
    endscript
}
```

### 3. 基础监控脚本
创建健康检查脚本：
```bash
nano ~/health-check.sh
```

```bash
#!/bin/bash
# 健康检查脚本

echo "=== $(date) ==="

# 检查前端服务
if curl -s http://localhost:3000/ > /dev/null; then
    echo "✅ 前端服务正常"
else
    echo "❌ 前端服务异常"
    sudo systemctl restart anime-frontend
fi

# 检查后端服务  
if curl -s http://localhost:8000/api/v1/ > /dev/null; then
    echo "✅ 后端服务正常"
else
    echo "❌ 后端服务异常"
    sudo systemctl restart anime-backend
fi

# 检查磁盘空间
DISK_USAGE=$(df -h / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "⚠️ 磁盘使用率过高: ${DISK_USAGE}%"
fi

echo "==================="
```

设置定时任务：
```bash
chmod +x ~/health-check.sh
crontab -e
```

添加：
```
# 每5分钟检查一次服务状态
*/5 * * * * /home/<USER>/health-check.sh >> /var/log/health-check.log 2>&1
```

---

## 🔍 第八步：部署后验证

### 1. 服务状态检查
```bash
# 检查服务状态
sudo systemctl status anime-backend
sudo systemctl status anime-frontend
sudo systemctl status nginx

# 检查端口监听
sudo netstat -tlnp | grep :3000  # 前端端口
sudo netstat -tlnp | grep :8000  # 后端端口
sudo netstat -tlnp | grep :80    # Nginx HTTP
sudo netstat -tlnp | grep :443   # Nginx HTTPS
```

### 2. 功能测试
```bash
# 测试前端访问
curl -I http://your-domain.com/
curl -I http://*************/

# 测试后端 API
curl -I http://your-domain.com/api/v1/
curl -I http://*************:8000/api/v1/

# 测试 API 文档
curl -I http://your-domain.com/docs
```

### 3. 浏览器测试
在浏览器中访问：
- ✅ http://your-domain.com/ （主页）
- ✅ http://your-domain.com/api/v1/ （API）
- ✅ http://your-domain.com/docs （API文档）

测试功能：
- ✅ 用户注册/登录
- ✅ 漫画浏览
- ✅ 漫画阅读
- ✅ 用户收藏
- ✅ 阅读历史

---

## 🚨 常见问题解决

### 1. 跨域请求被阻止
**错误**: `Blocked cross-origin request from *************`

**解决方案**:
1. 检查 `frontend/next.config.ts` 中的 `allowedDevOrigins`
2. 检查后端 `.env.production` 中的 `CORS_ORIGINS`
3. 确认 Nginx 配置正确

### 2. 数据库连接失败
**错误**: `Database connection failed`

**解决方案**:
```bash
# 测试数据库连接
cd backend
source venv/bin/activate
python -c "
from app.core.database import engine
try:
    conn = engine.connect()
    print('✅ 数据库连接成功')
    conn.close()
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
"
```

### 3. 构建失败
**错误**: `Build failed` 或内存不足

**解决方案**:
```bash
# 清理缓存
cd frontend
rm -rf .next/
rm -rf node_modules/
npm install

# 增加内存限制构建
NODE_OPTIONS="--max-old-space-size=4096" npm run build
```

### 4. 服务启动失败
**错误**: 服务无法启动

**解决方案**:
```bash
# 查看详细错误日志
sudo journalctl -u anime-backend -n 50
sudo journalctl -u anime-frontend -n 50

# 检查端口占用
sudo lsof -i :3000
sudo lsof -i :8000

# 手动启动测试
cd backend && source venv/bin/activate && python start.py
cd frontend && npm run start
```

---

## 🎯 性能优化建议

### 1. 启用 Gzip 压缩
在 Nginx 配置中添加：
```nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/json application/xml+rss;
```

### 2. 配置缓存策略
```nginx
# 静态资源缓存
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 3. 数据库优化
```sql
-- 添加常用查询索引
CREATE INDEX idx_manga_view_count ON mangas(view_count DESC);
CREATE INDEX idx_manga_created_at ON mangas(created_at DESC);
CREATE INDEX idx_manga_is_active ON mangas(is_active);
```

### 4. 启用 CDN（可选）
- 配置 Cloudflare 或其他 CDN 服务
- 将静态资源托管到 CDN
- 配置 DNS 解析

---

## 📋 部署完成检查清单

### ✅ 基础环境
- [ ] 服务器环境已准备
- [ ] Node.js 和 Python 已安装
- [ ] 数据库连接正常

### ✅ 应用部署
- [ ] 项目文件已清理
- [ ] 后端服务正常运行 (端口 8000)
- [ ] 前端服务正常运行 (端口 3000)
- [ ] 数据库迁移已执行

### ✅ 网络配置
- [ ] Nginx 反向代理已配置
- [ ] 防火墙规则已设置
- [ ] 域名解析已配置（如使用域名）
- [ ] SSL 证书已配置（HTTPS）

### ✅ 功能验证
- [ ] 网站首页可正常访问
- [ ] 用户注册/登录功能正常
- [ ] 漫画浏览功能正常
- [ ] 漫画阅读功能正常
- [ ] API 文档可访问
- [ ] 跨域问题已解决

### ✅ 运维配置
- [ ] 系统服务已配置（开机自启）
- [ ] 日志轮转已设置
- [ ] 健康检查已配置
- [ ] 备份策略已制定

---

## 🆘 技术支持

如果在部署过程中遇到问题：

1. **查看日志**: 使用 `journalctl` 查看服务日志
2. **测试连接**: 使用 `curl` 测试各个服务
3. **检查配置**: 确认环境变量和配置文件
4. **重启服务**: 尝试重启相关服务

### 常用命令速查
```bash
# 重启所有服务
sudo systemctl restart anime-backend anime-frontend nginx

# 查看服务状态
sudo systemctl status anime-backend anime-frontend nginx

# 查看实时日志
sudo journalctl -f -u anime-backend -u anime-frontend

# 测试网络连通性
curl -I http://localhost:3000
curl -I http://localhost:8000/api/v1/
```

---

**恭喜！🎉 您的动漫网站已成功部署到生产环境！**

现在用户可以通过 `http://your-domain.com` 或 `http://*************` 访问您的网站了！