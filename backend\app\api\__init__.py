from fastapi import APIRouter
from app.api import comments, manga, user
from app.api import auth, animes, favorites, admin
from app.api import announcements, email, cache_management, redis_config

api_router = APIRouter()

api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(animes.router, prefix="/animes", tags=["动漫"])
api_router.include_router(manga.router, prefix="/manga", tags=["漫画"])
api_router.include_router(favorites.router, prefix="/favorites", tags=["收藏"])
api_router.include_router(user.router, prefix="/user", tags=["用户"])
api_router.include_router(comments.router, prefix="/comments", tags=["评论"])
api_router.include_router(admin.router, prefix="/admin", tags=["管理员"])
api_router.include_router(announcements.router, prefix="/admin/announcements", tags=["公告"])
api_router.include_router(email.router, prefix="/admin/email", tags=["邮件"])
api_router.include_router(cache_management.router, prefix="/admin/cache", tags=["缓存管理"])
api_router.include_router(redis_config.router, prefix="/admin/redis", tags=["Redis配置"])