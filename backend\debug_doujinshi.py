#!/usr/bin/env python3
"""
调试同人志数据问题
"""

import sys
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.models import Manga

def debug_doujinshi():
    """调试同人志数据"""
    engine = create_engine(
        settings.DATABASE_URL,
        pool_pre_ping=True,
        echo=False
    )
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # 查找所有doujinshi类型的漫画
        print("=== 查找所有doujinshi类型的漫画 ===")
        doujinshi_mangas = session.query(Manga).filter(Manga.manga_type == 'doujinshi').all()
        
        for manga in doujinshi_mangas:
            print(f"ID: {manga.id}")
            print(f"标题: {manga.title}")
            print(f"类型: {manga.manga_type}")
            print(f"地区代码: {manga.region_code}")
            print(f"状态: {manga.status}")
            print(f"创建时间: {manga.created_at}")
            print(f"更新时间: {manga.updated_at}")
            print("-" * 50)
        
        # 测试不同的筛选条件
        print("\n=== 测试不同筛选条件 ===")
        
        # 1. 只筛选manga_type
        count1 = session.query(Manga).filter(Manga.manga_type == 'doujinshi').count()
        print(f"只筛选manga_type='doujinshi': {count1} 个")
        
        # 2. 筛选manga_type + region_code='kr'
        count2 = session.query(Manga).filter(
            Manga.manga_type == 'doujinshi',
            Manga.region_code == 'kr'
        ).count()
        print(f"筛选manga_type='doujinshi' + region_code='kr': {count2} 个")
        
        # 3. 筛选manga_type + region_code='jp'
        count3 = session.query(Manga).filter(
            Manga.manga_type == 'doujinshi',
            Manga.region_code == 'jp'
        ).count()
        print(f"筛选manga_type='doujinshi' + region_code='jp': {count3} 个")
        
        # 4. 筛选manga_type + region_code=None
        count4 = session.query(Manga).filter(
            Manga.manga_type == 'doujinshi',
            Manga.region_code.is_(None)
        ).count()
        print(f"筛选manga_type='doujinshi' + region_code=None: {count4} 个")
        
        # 查看所有region_code的分布
        print("\n=== region_code分布 ===")
        result = session.execute(text("""
            SELECT region_code, COUNT(*) as count 
            FROM mangas 
            WHERE manga_type = 'doujinshi'
            GROUP BY region_code 
            ORDER BY count DESC
        """))
        
        for row in result:
            print(f"region_code: {row.region_code}, 数量: {row.count}")
            
    except Exception as e:
        print(f"查询出错: {e}")
    finally:
        session.close()

if __name__ == "__main__":
    debug_doujinshi()
