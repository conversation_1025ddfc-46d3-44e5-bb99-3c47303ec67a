from sqlalchemy import text
from app.models import Base
from app.core.database import engine

def recreate_database():
    """重新创建数据库表"""
    print("正在删除现有表...")

    # 手动删除表以避免外键约束问题（SQLAlchemy 2.x 使用 exec_driver_sql）
    try:
        with engine.begin() as conn:
            conn.exec_driver_sql("SET FOREIGN_KEY_CHECKS = 0")
            conn.exec_driver_sql("DROP TABLE IF EXISTS comments")
            # stills 已移除
            conn.exec_driver_sql("DROP TABLE IF EXISTS anime_tags")
            conn.exec_driver_sql("DROP TABLE IF EXISTS tags")
            conn.exec_driver_sql("DROP TABLE IF EXISTS favorites")
            conn.exec_driver_sql("DROP TABLE IF EXISTS animes")
            conn.exec_driver_sql("DROP TABLE IF EXISTS categories")
            conn.exec_driver_sql("DROP TABLE IF EXISTS users")
            conn.exec_driver_sql("SET FOREIGN_KEY_CHECKS = 1")
        print("现有表删除完成")
    except Exception as e:
        print(f"删除表时出错: {e}")
    
    print("正在创建新表...")
    Base.metadata.create_all(bind=engine)
    print("数据库表创建完成!")

if __name__ == "__main__":
    recreate_database()