'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { 
  MangaChapter,
  MangaPage,
  Manga,
  apiClient
} from '@/lib/api';
import { localStorageUtils, debounce, ImagePreloader } from '@/lib/readerUtils';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { ReaderSettingsPanel } from '@/components/manga/ReaderSettingsPanel';
import { ChapterListSidebar } from '@/components/manga/ChapterListSidebar';
import {
  ArrowLeft,
  ChevronLeft,
  ChevronRight,
  BookOpen,
  Settings
} from 'lucide-react';

type ReadingMode = 'flip' | 'scroll';

export default function EnhancedMangaChapterPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const { resolvedTheme, setTheme } = useTheme();
  
  const mangaId = params.id as string;
  const chapterNumber = params.chapterId as string;
  
  // 基础数据状态
  const [manga, setManga] = useState<Manga | null>(null);
  const [chapter, setChapter] = useState<MangaChapter | null>(null);
  const [pages, setPages] = useState<MangaPage[]>([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [imagesLoaded, setImagesLoaded] = useState<Set<number>>(new Set());

  // UI 控制状态 - 从localStorage读取用户偏好
  const [readingMode, setReadingMode] = useState<ReadingMode>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('manga-reading-mode');
      return (saved as ReadingMode) || 'scroll';
    }
    return 'scroll';
  });
  // 移除缩放状态
  const [showUI, setShowUI] = useState(false); // 默认隐藏头部
  const [showSettings, setShowSettings] = useState(false);
  const [showChapterList, setShowChapterList] = useState(false);
  const [readerTheme, setReaderTheme] = useState<'default' | 'dark' | 'parchment' | 'paper' | 'sepia' | 'green' | 'blue'>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('manga-reader-theme');
      return (saved as 'default' | 'dark' | 'parchment' | 'paper' | 'sepia' | 'green' | 'blue') || 'default';
    }
    return 'default';
  });
  const [showFocusMask, setShowFocusMask] = useState(false);

  // 移除连续阅读状态
  // 移除已加载章节状态和加载状态

  // 工具和引用
  const imagePreloader = useRef(new ImagePreloader());
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const pageRefs = useRef<(HTMLDivElement | null)[]>([]);
  // 移除章节观察器引用
  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(null);
  
  // 触摸手势支持
  const touchMoveRef = useRef<{ y: number; time: number } | null>(null);

  // 防抖的进度保存函数
  const debouncedSaveProgress = useCallback(
    debounce((mangaId: number, chapterDbId: number, pageNumber: number) => {
      if (user) {
        // 登录用户保存到服务器
        apiClient.updateMangaReadingProgress(mangaId, {
          chapter_id: chapterDbId,
          page_number: pageNumber + 1,
          total_pages: pages.length,
          progress_percentage: ((pageNumber + 1) / pages.length) * 100,
        }).catch(console.error);
      } else {
        // 游客保存到本地存储
        localStorageUtils.saveGuestProgress(mangaId, chapterDbId, pageNumber);
      }
    }, 5000),
    [user, pages.length]
  );

  // 移除加载下一章节函数

  // 移除加载上一章节函数

  // 移除章节观察器

  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 防止在输入框中触发
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault();
          // 左键：上一话
          handlePrevChapter();
          break;

        case 'ArrowRight':
          e.preventDefault();
          // 右键：下一话
          handleNextChapter();
          break;

        case 'a':
        case 'A':
          e.preventDefault();
          if (readingMode === 'flip') {
            handlePrevPage();
          } else {
            // 滚动模式下向上滚动
            scrollContainerRef.current?.scrollBy({ top: -window.innerHeight * 0.8, behavior: 'smooth' });
          }
          break;

        case 'd':
        case 'D':
        case ' ': // 空格键
          e.preventDefault();
          if (readingMode === 'flip') {
            handleNextPage();
          } else {
            // 滚动模式下向下滚动
            scrollContainerRef.current?.scrollBy({ top: window.innerHeight * 0.8, behavior: 'smooth' });
          }
          break;

        case 'ArrowUp':
        case 'w':
        case 'W':
          e.preventDefault();
          if (readingMode === 'scroll') {
            scrollContainerRef.current?.scrollBy({ top: -200, behavior: 'smooth' });
          }
          break;

        case 'ArrowDown':
        case 's':
        case 'S':
          e.preventDefault();
          if (readingMode === 'scroll') {
            scrollContainerRef.current?.scrollBy({ top: 200, behavior: 'smooth' });
          }
          break;

        case 'Home':
          e.preventDefault();
          if (readingMode === 'flip') {
            setCurrentPage(0);
          } else {
            scrollContainerRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
          }
          break;

        case 'End':
          e.preventDefault();
          if (readingMode === 'flip') {
            setCurrentPage(pages.length - 1);
          } else {
            scrollContainerRef.current?.scrollTo({
              top: scrollContainerRef.current.scrollHeight,
              behavior: 'smooth'
            });
          }
          break;

        // 移除缩放快捷键


        // 移除缩放快捷键
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [readingMode, pages.length, showSettings]);





  // 增强的触摸手势支持
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (e.touches.length === 1) {
      const touch = e.touches[0];
      const currentTime = Date.now();
      
      touchStartRef.current = {
        x: touch.clientX,
        y: touch.clientY,
        time: currentTime
      };
      
      touchMoveRef.current = {
        y: touch.clientY,
        time: currentTime
      };
      
      // 完全依赖原生滚动，不需要额外处理
    }
  }, []);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!touchStartRef.current || !touchMoveRef.current || e.touches.length !== 1) return;
    
    const touch = e.touches[0];
    const currentTime = Date.now();
    
    // 完全简化：只更新触摸参考点，不做任何计算
    // 让浏览器完全处理原生滚动
    touchMoveRef.current = {
      y: touch.clientY,
      time: currentTime
    };
  }, []);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    if (!touchStartRef.current || e.changedTouches.length !== 1) return;

    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - touchStartRef.current.x;
    const deltaY = touch.clientY - touchStartRef.current.y;
    const deltaTime = Date.now() - touchStartRef.current.time;

    // 简化滑动手势检查，减少延迟
    const minDistance = 10; // 进一步降低阈值
    const maxTime = 1000; // 增加时间窗口

    if (deltaTime > maxTime) {
      touchStartRef.current = null;
      touchMoveRef.current = null;
      return;
    }

    const absX = Math.abs(deltaX);
    const absY = Math.abs(deltaY);

    if (readingMode === 'flip') {
      // 翻页模式：水平滑动
      if (absX > minDistance && absX > absY * 1.5) {
        e.preventDefault();
        if (deltaX > 0) {
          handlePrevPage();
        } else {
          handleNextPage();
        }
      }
    } else {
      // 滚动模式：完全依赖原生滚动，不添加任何干预
      // 只处理明确的水平滑动翻页手势
      if (absX > minDistance * 2 && absX > absY * 2) {
        e.preventDefault();
        if (deltaX > 0) {
          handlePrevPage();
        } else {
          handleNextPage();
        }
      }
      // 对于垂直滑动，完全不干预，让浏览器处理
    }

    touchStartRef.current = null;
    touchMoveRef.current = null;
  }, [readingMode]);

  // 数据获取
  const fetchChapterData = async () => {
    try {
      setLoading(true);
      
      const mangaData = await apiClient.getManga(parseInt(mangaId));
      setManga(mangaData);
      
      const chapterData = await apiClient.getMangaChapterByNumber(
        parseInt(mangaId), 
        parseFloat(chapterNumber)
      );
      setChapter(chapterData);
      
      const pagesData = await apiClient.getChapterPages(chapterData.id);
      setPages(pagesData.pages);
      
      // 加载阅读进度
      let startPage = 0;
      if (user) {
        try {
          const progress = await apiClient.getMangaReadingProgress(parseInt(mangaId));
          if (progress.chapter_id === chapterData.id) {
            startPage = progress.page_number - 1;
          }
        } catch (error) {
          console.log('No server progress found');
        }
      } else {
        const guestProgress = localStorageUtils.getGuestProgress(parseInt(mangaId));
        if (guestProgress && guestProgress.chapterId === chapterData.id) {
          startPage = guestProgress.pageNumber;
        }
      }
      
      setCurrentPage(Math.max(0, Math.min(startPage, pagesData.pages.length - 1)));
      
    } catch (error) {
      console.error('Failed to fetch chapter data:', error);
      setError('获取章节信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 图片预加载
  useEffect(() => {
    if (pages.length === 0) return;

    const preloadRange = 3; // 预加载前后3页
    const startIdx = Math.max(0, currentPage - preloadRange);
    const endIdx = Math.min(pages.length - 1, currentPage + preloadRange);
    
    const urlsToPreload = pages.slice(startIdx, endIdx + 1).map(page => page.image_url);
    
    // 当前页高优先级，其他页低优先级
    const currentUrl = pages[currentPage]?.image_url;
    if (currentUrl) {
      imagePreloader.current.preloadImages([currentUrl], 'high');
    }
    
    const otherUrls = urlsToPreload.filter(url => url !== currentUrl);
    if (otherUrls.length > 0) {
      imagePreloader.current.preloadImages(otherUrls, 'low');
    }
  }, [currentPage, pages]);

  // 进度保存
  useEffect(() => {
    if (pages.length > 0 && chapter) {
      debouncedSaveProgress(parseInt(mangaId), chapter.id, currentPage);
    }
  }, [currentPage, mangaId, chapter, debouncedSaveProgress, pages.length]);

  // 保存阅读模式偏好
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('manga-reading-mode', readingMode);
    }
  }, [readingMode]);

  // 保存阅读主题偏好
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('manga-reader-theme', readerTheme);
    }
  }, [readerTheme]);

  // 移除保存连续阅读偏好

  // 滚动模式下的页面跟踪
  useEffect(() => {
    if (readingMode !== 'scroll' || pages.length === 0) return;

    const observer = new IntersectionObserver(
      (entries) => {
        // 找到最大可见面积的页面作为当前页
        let maxVisiblePage = -1;
        let maxIntersectionRatio = 0;

        entries.forEach((entry) => {
          if (entry.isIntersecting && entry.intersectionRatio > maxIntersectionRatio) {
            const pageIndex = parseInt(entry.target.getAttribute('data-page-index') || '0');
            maxIntersectionRatio = entry.intersectionRatio;
            maxVisiblePage = pageIndex;
          }
        });

        if (maxVisiblePage >= 0) {
          setCurrentPage(maxVisiblePage);
        }
      },
      {
        threshold: [0.1, 0.25, 0.5, 0.75, 1.0], // 多个阈值，更精确检测
        root: scrollContainerRef.current,
        rootMargin: '-10% 0px -10% 0px' // 减少边缘误判
      }
    );

    pageRefs.current.forEach((ref) => {
      if (ref) {
        observer.observe(ref);
      }
    });

    return () => {
      observer.disconnect();
    };
  }, [readingMode, pages.length]);

  // 键盘控制
  const handleKeyPress = useCallback((e: KeyboardEvent) => {
    if (showSettings) return; // 设置面板打开时禁用键盘控制

    switch (e.key) {
      case 'ArrowLeft':
        e.preventDefault();
        handlePrevChapter();
        break;
      case 'ArrowRight':
        e.preventDefault();
        handleNextChapter();
        break;
      case 'a':
      case 'A':
        e.preventDefault();
        handlePrevPage();
        break;
      case 'd':
      case 'D':
        e.preventDefault();
        handleNextPage();
        break;
      case 'ArrowUp':
        e.preventDefault();
        if (readingMode === 'scroll') {
          scrollContainerRef.current?.scrollBy({ top: -200, behavior: 'smooth' });
        }
        break;
      case 'ArrowDown':
        e.preventDefault();
        if (readingMode === 'scroll') {
          scrollContainerRef.current?.scrollBy({ top: 200, behavior: 'smooth' });
        }
        break;
      case ' ':
        e.preventDefault();
        setShowUI(!showUI);
        break;
    }
  }, [readingMode, showSettings]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);

  // UI显隐控制 - 点击切换
  const handleContentClick = useCallback((e: React.MouseEvent) => {
    // 如果点击的是按钮或其他交互元素，不处理
    if ((e.target as HTMLElement).closest('button') || 
        (e.target as HTMLElement).closest('a') ||
        (e.target as HTMLElement).closest('.no-toggle')) {
      return;
    }
    
    // 切换UI显示状态
    setShowUI(!showUI);
  }, [showUI]);

  // 保持头部默认隐藏状态

  // 页面导航
  const handlePrevPage = () => {
    if (readingMode === 'scroll') {
      scrollContainerRef.current?.scrollBy({ top: -window.innerHeight * 0.8, behavior: 'smooth' });
    } else {
      if (currentPage > 0) {
        setCurrentPage(currentPage - 1);
      } else {
        handlePrevChapter();
      }
    }
  };

  const handleNextPage = () => {
    if (readingMode === 'scroll') {
      scrollContainerRef.current?.scrollBy({ top: window.innerHeight * 0.8, behavior: 'smooth' });
    } else {
      if (currentPage < pages.length - 1) {
        setCurrentPage(currentPage + 1);
      } else {
        handleNextChapter();
      }
    }
  };

  const handlePrevChapter = () => {
    if (manga && manga.chapters && chapter) {
      const currentIndex = manga.chapters.findIndex(ch => ch.chapter_number === chapter.chapter_number);
      if (currentIndex > 0) {
        const prevChapter = manga.chapters[currentIndex - 1];
        router.push(`/manga/${mangaId}/chapter/${prevChapter.chapter_number}`);
      }
    }
  };

  const handleNextChapter = () => {
    if (manga && manga.chapters && chapter) {
      const currentIndex = manga.chapters.findIndex(ch => ch.chapter_number === chapter.chapter_number);
      if (currentIndex < manga.chapters.length - 1) {
        const nextChapter = manga.chapters[currentIndex + 1];
        router.push(`/manga/${mangaId}/chapter/${nextChapter.chapter_number}`);
      }
    }
  };

  // 移除全屏控制函数


  // 移除缩放控制函数
  // 移除适应宽度和高度函数

  // 移除双击缩放功能

  // 阅读器主题处理
  useEffect(() => {
    // 根据阅读器主题设置页面样式
    const themeClasses = ['reader-default', 'reader-dark', 'reader-parchment', 'reader-paper', 'reader-sepia', 'reader-green', 'reader-blue'];
    document.body.classList.remove(...themeClasses);
    document.body.classList.add(`reader-${readerTheme}`);

    // 如果是暗色主题，确保系统也是暗色
    if (readerTheme === 'dark' && resolvedTheme !== 'dark') {
      setTheme('dark');
    } else if (readerTheme !== 'dark' && resolvedTheme === 'dark') {
      setTheme('light');
    }

    return () => {
      document.body.classList.remove(...themeClasses);
    };
  }, [readerTheme, resolvedTheme, setTheme]);



  // 移除书签功能

  useEffect(() => {
    if (mangaId && chapterNumber) {
      setImagesLoaded(new Set()); // 重置图片加载状态
      fetchChapterData();
    }
  }, [mangaId, chapterNumber]);



  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">加载图片中...</p>
        </div>
      </div>
    );
  }

  if (error || !chapter || !manga) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">章节不存在</h2>
          <p className="mb-4 text-muted-foreground">{error || '未找到该章节'}</p>
          <Button onClick={() => router.push(`/manga/${mangaId}`)}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回漫画详情
          </Button>
        </div>
      </div>
    );
  }

  // 边界检查：确保页面数据有效
  if (!pages || pages.length === 0) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">加载图片中...</p>
        </div>
      </div>
    );
  }

  // 确保currentPage在有效范围内

  const currentChapterIndex = manga.chapters?.findIndex(ch => ch.chapter_number === chapter?.chapter_number) ?? -1;
  const hasNextChapter = currentChapterIndex < (manga.chapters?.length ?? 0) - 1;
  const hasPrevChapter = currentChapterIndex > 0;

  // 根据主题获取背景样式
  const getThemeClasses = () => {
    switch (readerTheme) {
      case 'dark':
        return 'bg-gray-900 text-gray-100';
      case 'sepia':
        return 'bg-amber-50 text-amber-900';
      case 'green':
        return 'bg-green-50 text-green-900';
      case 'blue':
        return 'bg-blue-50 text-blue-900';
      case 'parchment':
        return 'bg-gradient-to-br from-amber-50 via-orange-50 to-amber-100 text-amber-900';
      case 'paper':
        return 'bg-stone-50 text-stone-800';
      default:
        return 'bg-background text-foreground';
    }
  };

  return (
    <div
      className={`manga-reader-container min-h-screen transition-all duration-500 immersive-reading overflow-hidden fixed inset-0 ${getThemeClasses()}`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      onContextMenu={(e) => e.preventDefault()} // 禁用右键菜单
    >
      {/* 顶部极细进度条 - 只在UI显示时渲染，避免闪现 */}
      {showUI && (
        <div className="fixed top-0 left-0 right-0 z-50 h-[2px] opacity-100">
          <div 
            className="h-full bg-primary transition-all duration-200"
            style={{ width: `${((currentPage + 1) / pages.length) * 100}%` }}
          />
        </div>
      )}
      
      {/* 顶部栏 */}
      <div className={`fixed top-0 left-0 right-0 z-40 bg-background/90 backdrop-blur-md border-b manga-float-shadow rounded-b-2xl transition-all duration-300 ${
        showUI ? 'translate-y-0' : '-translate-y-full'
      }`}>
        <div className="flex items-center justify-between p-3 md:p-4 min-h-[56px]">
          {/* 左侧区域 - 所有导航按钮 */}
          <div className="flex items-center gap-1 flex-shrink-0">
            {/* 退出按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push(`/manga/${mangaId}`)}
              className="flex items-center p-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="hidden md:inline ml-1">退出</span>
            </Button>

            {/* 前一话按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handlePrevChapter}
              disabled={!hasPrevChapter}
              className="flex items-center p-2"
              title="前一话"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            {/* 后一话按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleNextChapter}
              disabled={!hasNextChapter}
              className="flex items-center p-2"
              title="后一话"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          {/* 中间区域 - 章节标题，与按钮对齐 */}
          <div className="flex-1 min-w-0 px-2 flex items-center justify-center">
            <button
              onClick={() => setShowChapterList(true)}
              className="text-sm font-medium hover:text-primary transition-colors cursor-pointer"
              title={`${chapter?.title || `第 ${chapter?.chapter_number} 话`} - 点击打开章节目录`}
            >
              {/* 移动端显示短标题，桌面端显示完整标题 */}
              <div className="truncate sm:hidden">
                {chapter?.title
                  ? (chapter.title.length > 8 ? `${chapter.title.substring(0, 8)}...` : chapter.title)
                  : `第 ${chapter?.chapter_number} 话`
                }
              </div>
              <div className="truncate hidden sm:block">
                {chapter?.title || `第 ${chapter?.chapter_number} 话`}
              </div>
            </button>
          </div>

          {/* 右侧区域 - 功能按钮 */}
          <div className="flex items-center gap-1 flex-shrink-0">
            {/* 章节目录按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowChapterList(true)}
              className="flex items-center p-2"
              title="章节目录"
            >
              <BookOpen className="h-4 w-4" />
            </Button>

            {/* 设置按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(true)}
              className="flex items-center p-2"
              title="设置"
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* 阅读区域 */}
      <div className="h-screen overflow-hidden">
        {readingMode === 'flip' ? (
          // 翻页模式
          <div className="relative h-full flex items-center justify-center p-4" onClick={handleContentClick}>
            {pages.length > 0 && (
              <div
                className="relative max-w-full max-h-full flex items-center justify-center"
              >
                <img
                  key={currentPage}
                  src={pages[currentPage]?.image_url}
                  alt={`第 ${currentPage + 1} 页`}
                  className="manga-flip-image object-contain select-none manga-page-transition manga-page-shadow"
                  draggable={false}
                  loading="eager"
                  style={{
                    maxWidth: '90%',
                    maxHeight: '90%',
                    width: 'auto',
                    height: 'auto',
                    borderRadius: '0',
                    boxShadow: '0 4px 16px rgba(0,0,0,0.15)'
                  }}
                />
                
                {/* 左右导航区域 */}
                <div 
                  className="absolute left-0 top-0 w-1/3 h-full cursor-pointer flex items-center justify-start pl-4 opacity-0 hover:opacity-100 transition-opacity no-toggle"
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePrevPage();
                  }}
                >
                  {(currentPage > 0 || hasPrevChapter) && (
                    <div className="bg-black/40 backdrop-blur-sm p-3 rounded-2xl shadow-xl">
                      <ChevronLeft className="h-6 w-6 text-white" />
                    </div>
                  )}
                </div>
                
                <div 
                  className="absolute right-0 top-0 w-1/3 h-full cursor-pointer flex items-center justify-end pr-4 opacity-0 hover:opacity-100 transition-opacity no-toggle"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleNextPage();
                  }}
                >
                  {(currentPage < pages.length - 1 || hasNextChapter) && (
                    <div className="bg-black/40 backdrop-blur-sm p-3 rounded-2xl shadow-xl">
                      <ChevronRight className="h-6 w-6 text-white" />
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          // 滚动模式
          <div
            ref={scrollContainerRef}
            className="h-full overflow-y-auto manga-reader-scroll mobile-scroll-optimized"
            onClick={handleContentClick}
            style={{
              paddingTop: showUI ? '64px' : '0',
              transition: 'padding 0.3s ease',
              // 完全原生的滚动体验
              WebkitOverflowScrolling: 'touch', // iOS 原生滚动优化
              touchAction: 'pan-y', // 只允许垂直滑动手势
              scrollBehavior: 'auto', // 禁用浏览器自带的平滑滚动
              // 消除间隙
              lineHeight: 0,
              fontSize: 0,
              // 性能优化
              transform: 'translateZ(0)', // 启用硬件加速
              backfaceVisibility: 'hidden', // 避免不必要的重绘
              willChange: 'scroll-position', // 提示浏览器优化滚动
              // 禁用文本选择和长按菜单
              WebkitUserSelect: 'none',
              userSelect: 'none',
              WebkitTouchCallout: 'none', // 禁用iOS长按菜单
              WebkitTapHighlightColor: 'transparent', // 禁用点击高亮
              // 移动端长按优化
              msTouchSelect: 'none',
              msUserSelect: 'none',
              KhtmlUserSelect: 'none',
              MozUserSelect: 'none'
            }}
          >
            <div className="manga-container flex flex-col items-center pb-32" style={{
              gap: 0,
              lineHeight: 0,
              fontSize: 0,
              padding: '0 8px'
            }}>
              {/* 当前章节的页面 */}
              {pages.map((page, index) => (
                <div
                  key={`current-${index}`}
                  ref={(el) => { pageRefs.current[index] = el; }}
                  className="manga-page-wrapper w-full flex justify-center"
                  data-page={index}
                  data-page-index={index}
                  style={{
                    margin: 0,
                    padding: 0,
                    lineHeight: 0,
                    fontSize: 0,
                    marginBottom: 0,
                    marginTop: 0
                  }}
                >
                  <img
                    src={page.image_url}
                    alt={`第 ${chapter?.chapter_number} 话 第 ${index + 1} 页`}
                    className="manga-page-image h-auto block select-none"
                    draggable={false}
                    loading={index < 3 ? "eager" : "lazy"}
                    decoding="async"
                    onLoad={(e) => {
                      const img = e.target as HTMLImageElement;
                      img.style.opacity = '1';
                      // 跟踪图片加载状态
                      setImagesLoaded(prev => new Set(prev).add(index));
                    }}
                    onError={(e) => {
                      const img = e.target as HTMLImageElement;
                      img.style.backgroundColor = '#f1f3f4';
                      img.style.minHeight = '200px';
                    }}
                    style={{
                      width: 'auto',
                      height: 'auto',
                      maxWidth: '100%',
                      display: 'block',
                      margin: '0 auto',
                      padding: 0,
                      verticalAlign: 'bottom',
                      lineHeight: 0,
                      fontSize: 0,
                      borderRadius: '0',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                      backgroundColor: '#f8f9fa',
                      opacity: 0,
                      transition: 'opacity 0.3s ease-in-out',
                      // 禁用长按菜单和选择
                      WebkitTouchCallout: 'none',
                      WebkitUserSelect: 'none',
                      userSelect: 'none',
                      WebkitTapHighlightColor: 'transparent',
                      pointerEvents: 'none' // 防止图片阻挡触摸事件
                    }}
                  />
                </div>
              ))}

              {/* 章节结束导航按钮 - 只在图片完全加载后显示 */}
              {imagesLoaded.size === pages.length && pages.length > 0 && (
                <div className="w-full max-w-md mx-auto mt-8 mb-20 px-4">
                  <div className="bg-background/90 backdrop-blur-md border rounded-2xl p-6 manga-float-shadow">
                    <div className="text-center mb-4">
                      <h3 className="text-lg font-semibold">第 {chapter.chapter_number} 话完</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        {manga.title} - 共 {pages.length} 页
                      </p>
                    </div>
                    
                    <div className="flex gap-3">
                      <Button
                        variant="outline"
                        className="flex-1"
                        onClick={handlePrevChapter}
                        disabled={!hasPrevChapter}
                      >
                        <ChevronLeft className="h-4 w-4 mr-2" />
                        上一话
                      </Button>
                      
                      <Button
                        variant="default"
                        className="flex-1"
                        onClick={handleNextChapter}
                        disabled={!hasNextChapter}
                      >
                        下一话
                        <ChevronRight className="h-4 w-4 ml-2" />
                      </Button>
                    </div>
                    
                    {!hasNextChapter && (
                      <p className="text-xs text-muted-foreground text-center mt-3">
                        已经是最新话了
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>


      {/* 移除章节列表侧边栏，简化界面 */}

      {/* 移除评论侧边栏，简化界面 */}

      {/* FAB浮动按钮已移除 - 简化界面 */}
      
      {/* 侧边栏面板已移除 - 简化界面 */}
      
      {/* 聚焦遮罩 */}
      {showFocusMask && (
        <div className="reading-focus-mask" />
      )}
      
      {/* 移除Toast提示 */}
      
      {/* 长按菜单 - 移动端 */}
      <style jsx>{`
        @media (max-width: 768px) {
          .manga-page-img {
            -webkit-touch-callout: default;
          }
        }
      `}</style>

      {/* 章节目录侧边栏 */}
      <ChapterListSidebar
        isOpen={showChapterList}
        onClose={() => setShowChapterList(false)}
        chapters={manga?.chapters || []}
        currentChapterId={chapter?.id || 0}
        mangaId={parseInt(mangaId)}
      />

      {/* 设置面板 */}
      {showSettings && (
        <ReaderSettingsPanel
          isOpen={showSettings}
          onClose={() => setShowSettings(false)}
          theme={readerTheme}
          onThemeChange={setReaderTheme}
          readingMode={readingMode}
          onReadingModeChange={setReadingMode}
          showFocusMask={showFocusMask}
          onFocusMaskChange={setShowFocusMask}
        />
      )}
    </div>
  );
}