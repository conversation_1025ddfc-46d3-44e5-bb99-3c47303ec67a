# Manga Infinite Scroll Loading System

## Overview

This implementation provides a complete infinite scroll loading system for manga chapter reading, extending the existing manga reader with seamless chapter-to-chapter navigation.

## Key Features

### 1. 滚动检测机制 (Scroll Detection)
- **底部边界检测**: Monitors scroll reaching chapter bottom with IntersectionObserver
- **提前加载触发**: Triggers preloading at 200px before reaching bottom
- **滚动位置计算**: Uses `scrollTop + clientHeight >= scrollHeight - threshold` logic
- **防抖处理**: Prevents frequent loading requests with 500ms debounce

### 2. 章节连接策略 (Chapter Connection Strategy)
- **无缝衔接**: New chapter content appends seamlessly to scroll container
- **分隔标识**: Clear chapter separators with titles and page counts
- **URL状态更新**: Automatically updates browser URL when scrolling to new chapters
- **Global序列号系统**: Each page has a global sequence number for positioning

### 3. 用户体验设计 (User Experience)
- **加载指示器**: "正在加载下一话" animations between chapters
- **章节标题栏**: Sticky chapter headers with current chapter highlighting
- **加载失败处理**: Retry buttons and error messages for network issues
- **Smart预加载**: Preloads 5 pages ahead and behind current position

## Architecture

### Components

1. **`useInfiniteScroll`** Hook (`/hooks/useInfiniteScroll.ts`)
   - Manages infinite scroll state and API calls
   - Handles page loading with configurable ahead/behind ranges
   - Integrates with existing ImagePreloader system

2. **`useScrollTrigger`** Hook (`/hooks/useScrollTrigger.ts`)
   - IntersectionObserver-based scroll detection
   - Configurable thresholds for top/bottom triggers
   - Position tracking with throttled updates

3. **`InfiniteScrollComponents`** (`/components/manga/InfiniteScrollComponents.tsx`)
   - `LoadingIndicator`: Loading states and error handling
   - `ChapterSeparator`: Chapter dividers with sticky headers
   - `ProgressIndicator`: Global progress tracking
   - `ScrollTrigger`: Intersection trigger elements

4. **`InfiniteScrollReader`** (`/components/manga/InfiniteScrollReader.tsx`)
   - Main infinite scroll reader component
   - Manages page visibility and chapter transitions
   - URL updating and navigation integration

5. **`InfiniteScrollControls`** (`/components/manga/InfiniteScrollControls.tsx`)
   - Floating action button for infinite scroll settings
   - Toggle continuous reading on/off
   - Status indicators and usage hints

### API Extensions

Extended the API client with new methods:
- `getMangaChapterPages()`: Paginated chapter pages loading
- `getInfinitePages()`: Unified infinite pages API call (待后端实现)

### Integration Points

- **Existing Reader**: Integrated with current flip/scroll mode toggle
- **Settings Panel**: Connected to existing reader settings
- **Progress Tracking**: Compatible with existing reading progress system
- **Image Preloading**: Uses existing ImagePreloader for performance

## Usage

### Basic Usage
```tsx
// Enable infinite scroll in scroll mode
<InfiniteScrollReader
  mangaId={mangaId}
  initialChapterId={chapterId}
  initialPage={currentPage}
  enabled={continuousReading && readingMode === 'scroll'}
  zoom={zoom}
  onPageChange={handlePageChange}
  onChapterChange={handleChapterChange}
/>
```

### Controls Integration
```tsx
// Add floating controls
<InfiniteScrollControls
  continuousReading={continuousReading}
  onToggleContinuous={setContinuousReading}
  isLoading={isLoadingNextChapter}
  currentChapter={chapter?.chapter_number}
  totalChapters={manga?.chapters?.length}
/>
```

## Backend API Requirements

The system expects the following API endpoint (as specified in requirements):

```
GET /api/v1/manga/{manga_id}/infinite-pages
```

Query Parameters:
- `current_chapter_id`: Current chapter ID
- `current_page`: Current page number
- `pages_ahead`: Pages to load ahead (default: 10)
- `pages_behind`: Pages to load behind (default: 5)

Expected Response:
```json
{
  "pages": [
    {
      "id": 1,
      "page_number": 1,
      "image_url": "...",
      "chapter": {...},
      "global_sequence_number": 150
    }
  ],
  "chapters": [
    {
      "id": 1,
      "chapter_number": 5.5,
      "title": "Special Chapter",
      "global_sequence_number": 145
    }
  ],
  "total_pages": 500,
  "current_global_position": 150
}
```

## Performance Optimizations

1. **Smart Preloading**: Only preloads images within 5-page range
2. **Lazy Loading**: Uses native lazy loading for distant images
3. **Debounced Loading**: Prevents excessive API calls
4. **Memory Management**: Limits loaded content to prevent memory leaks
5. **Intersection Observer**: Efficient scroll position detection

## Mobile Optimizations

- **Touch Gestures**: Maintains existing touch swipe support
- **Viewport Optimization**: Responsive design for mobile screens
- **Performance**: Optimized scroll performance with `overflow-scrolling: touch`
- **Battery Friendly**: Minimizes DOM manipulation and reflows

## Accessibility

- **Keyboard Navigation**: Full keyboard support maintained
- **Screen Readers**: Proper ARIA labels for chapter transitions
- **Focus Management**: Maintains focus context during navigation
- **High Contrast**: Compatible with theme system

## File Structure

```
src/
├── hooks/
│   ├── useInfiniteScroll.ts          # Core infinite scroll logic
│   └── useScrollTrigger.ts           # Scroll detection utilities
├── components/manga/
│   ├── InfiniteScrollComponents.tsx   # UI components
│   ├── InfiniteScrollReader.tsx      # Main reader component
│   └── InfiniteScrollControls.tsx    # Control interface
├── lib/
│   └── api.ts                        # Extended API methods
└── app/manga/[id]/chapter/[chapterId]/
    └── page.tsx                      # Integrated reader page
```

## Configuration

The system is highly configurable through props:

```tsx
interface InfiniteScrollConfig {
  pagesAhead: number;           // Pages to preload ahead (default: 15)
  pagesBehind: number;          // Pages to preload behind (default: 10)
  topThreshold: number;         // Top trigger threshold in px (default: 300)
  bottomThreshold: number;      // Bottom trigger threshold in px (default: 300)
  preloadThreshold: number;     // Image preload range (default: 3)
  throttleMs: number;           // Scroll throttle delay (default: 150)
}
```

## Future Enhancements

1. **Virtual Scrolling**: For extremely long manga series
2. **Offline Caching**: Cache loaded chapters for offline reading
3. **Reading Statistics**: Track reading time and patterns
4. **Chapter Bookmarks**: Quick jump to specific chapters
5. **Reading Mode Memory**: Remember infinite scroll preference per manga