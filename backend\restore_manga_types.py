#!/usr/bin/env python3
"""
恢复漫画类型到原始状态
只保留ID=1的漫画为doujinshi，其他都改回serial
"""

import sys
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.models import Manga, MangaType

def restore_manga_types():
    """恢复漫画类型"""
    engine = create_engine(
        settings.DATABASE_URL,
        pool_pre_ping=True,
        echo=False
    )
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # 将除了ID=1之外的所有doujinshi改回serial
        mangas_to_restore = session.query(Manga).filter(
            Manga.manga_type == 'doujinshi',
            Manga.id != 1
        ).all()
        
        print(f"找到 {len(mangas_to_restore)} 个需要恢复的漫画")
        
        for manga in mangas_to_restore:
            print(f"恢复漫画: ID={manga.id}, 标题={manga.title}")
            manga.manga_type = MangaType.SERIAL
        
        session.commit()
        print(f"成功恢复了 {len(mangas_to_restore)} 个漫画为serial类型")
        
        # 验证结果
        doujinshi_count = session.query(Manga).filter(Manga.manga_type == 'doujinshi').count()
        print(f"现在只有 {doujinshi_count} 个同人志类型的漫画")
        
        # 显示剩余的同人志
        doujinshi_mangas = session.query(Manga).filter(Manga.manga_type == 'doujinshi').all()
        for manga in doujinshi_mangas:
            print(f"剩余同人志 - ID: {manga.id}, 标题: {manga.title}")
            
    except Exception as e:
        print(f"操作出错: {e}")
        session.rollback()
    finally:
        session.close()

if __name__ == "__main__":
    restore_manga_types()
