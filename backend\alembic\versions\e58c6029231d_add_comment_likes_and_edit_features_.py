"""add_comment_likes_and_edit_features_final

Revision ID: e58c6029231d
Revises: d5f0e1fc29ed
Create Date: 2025-08-22 20:58:33.427241

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e58c6029231d'
down_revision: Union[str, None] = 'd5f0e1fc29ed'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('comment_likes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('comment_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('is_like', sa.<PERSON>(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['comment_id'], ['comments.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('comment_id', 'user_id', name='uq_comment_user_like')
    )
    op.create_index(op.f('ix_comment_likes_id'), 'comment_likes', ['id'], unique=False)
    op.add_column('comments', sa.Column('like_count', sa.Integer(), nullable=False))
    op.add_column('comments', sa.Column('dislike_count', sa.Integer(), nullable=False))
    op.add_column('comments', sa.Column('is_edited', sa.Boolean(), nullable=False))
    op.add_column('comments', sa.Column('edited_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('comments', sa.Column('edit_count', sa.Integer(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('comments', 'edit_count')
    op.drop_column('comments', 'edited_at')
    op.drop_column('comments', 'is_edited')
    op.drop_column('comments', 'dislike_count')
    op.drop_column('comments', 'like_count')
    op.drop_index(op.f('ix_comment_likes_id'), table_name='comment_likes')
    op.drop_table('comment_likes')
    # ### end Alembic commands ###
