"""
快速测试Redis动态配置
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import SessionLocal
from app.core.redis_config import RedisConfigManager

def test_redis_config_basic():
    """基础测试Redis配置管理"""
    
    db = SessionLocal()
    
    try:
        print("\n=== Redis配置管理测试 ===\n")
        
        # 初始化默认配置
        RedisConfigManager.initialize_default_config(db)
        print("[OK] 默认配置已初始化")
        
        # 获取配置
        config = RedisConfigManager.get_all_config(db)
        print(f"\n当前配置:")
        print(f"  启用: {config['redis_enabled']}")
        print(f"  主机: {config['redis_host']}")
        print(f"  端口: {config['redis_port']}")
        print(f"  数据库: {config['redis_db']}")
        print(f"  最大内存: {config['redis_max_memory']} MB")
        
        # 更新配置
        print("\n更新配置...")
        success = RedisConfigManager.update_configs(db, {
            'redis_enabled': True,
            'redis_host': '***********',
            'redis_port': 6379,
            'redis_max_memory': 128
        })
        
        if success:
            print("[OK] 配置更新成功")
            
            # 重新获取验证
            config = RedisConfigManager.get_all_config(db)
            print(f"\n更新后的配置:")
            print(f"  启用: {config['redis_enabled']}")
            print(f"  主机: {config['redis_host']}")
            print(f"  端口: {config['redis_port']}")
            print(f"  最大内存: {config['redis_max_memory']} MB")
        else:
            print("[FAIL] 配置更新失败")
        
        print("\n[OK] 测试完成！Redis配置管理功能正常")
        
    except Exception as e:
        print(f"\n[ERROR] 测试失败: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    test_redis_config_basic()