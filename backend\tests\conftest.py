#!/usr/bin/env python3
"""
Test configuration and fixtures for user management tests.

Provides reusable test data, mock objects, and configuration for all test suites.
"""

import pytest
import asyncio
import tempfile
import shutil
from typing import Dict, Any, Generator, List
from unittest.mock import Mock, MagicMock
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session
from fastapi.testclient import TestClient
from testcontainers.mysql import MySqlContainer

from app.main import app
from app.core.database import get_db
from app.core.security import get_password_hash, create_access_token
from app.models import Base, User, Favorite, Comment, CommentLike, MangaReadingProgress
from app.schemas.user import PasswordChangeRequest, AccountDeleteRequest

class TestDataFactory:
    """Factory for creating test data"""

    @staticmethod
    def create_user_data(
        username: str = "testuser",
        email: str = "<EMAIL>",
        password: str = "password123",
        is_admin: bool = False,
        is_active: bool = True
    ) -> Dict[str, Any]:
        """Create user data for testing"""
        return {
            "username": username,
            "email": email,
            "password": password,
            "password_hash": get_password_hash(password),
            "is_admin": is_admin,
            "is_active": is_active
        }

    @staticmethod
    def create_password_change_data(
        old_password: str = "oldpass123",
        new_password: str = "newpass456",
        confirm_password: str = None
    ) -> Dict[str, Any]:
        """Create password change request data"""
        if confirm_password is None:
            confirm_password = new_password
        
        return {
            "old_password": old_password,
            "new_password": new_password,
            "confirm_password": confirm_password
        }

    @staticmethod
    def create_account_delete_data(username: str = "testuser") -> Dict[str, Any]:
        """Create account deletion request data"""
        return {
            "username_confirmation": username
        }

    @staticmethod
    def create_malicious_payloads() -> Dict[str, List[str]]:
        """Create various malicious payloads for security testing"""
        return {
            "sql_injection": [
                "'; DROP TABLE users; --",
                "' OR '1'='1",
                "' UNION SELECT * FROM users --",
                "'; UPDATE users SET is_admin=true WHERE id=1; --",
                "' OR 1=1; --",
                "admin'--",
                "admin' /*",
                "admin' #",
                "' or 1=1#",
                "' or 1=1--",
                "' or 1=1/*"
            ],
            "xss": [
                "<script>alert('XSS')</script>",
                "javascript:alert('XSS')",
                "<img src=x onerror=alert('XSS')>",
                "<svg onload=alert('XSS')>",
                "';alert('XSS');//",
                "<iframe src='javascript:alert(1)'></iframe>",
                "<body onload=alert('XSS')>",
                "<input onfocus=alert('XSS') autofocus>",
                "';alert(String.fromCharCode(88,83,83))//",
                "<script>eval('al'+'ert(1)')</script>"
            ],
            "command_injection": [
                "; cat /etc/passwd",
                "| whoami",
                "`whoami`",
                "$(whoami)",
                "; rm -rf /",
                "&& ls -la",
                "|| echo vulnerable",
                "; ping -c 10 google.com"
            ],
            "path_traversal": [
                "../../../../etc/passwd",
                "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
                "../../../../../../../etc/passwd",
                "....//....//....//etc/passwd",
                "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd"
            ]
        }

    @staticmethod
    def create_weak_passwords() -> List[str]:
        """Create list of weak passwords for testing"""
        return [
            "123",                    # Too short
            "password",               # No numbers
            "12345678",              # No letters
            "pass",                  # Too short, no numbers
            "p1",                    # Too short
            "a" * 129,               # Too long (129 chars)
            "",                      # Empty
            "   ",                   # Only whitespace
            "password",              # Dictionary word, no numbers
            "123456789",             # Only numbers
            "abcdefgh",              # Only letters
            "ABCDEFGH",              # Only uppercase letters
            "aaaaaaaa",              # All same character
            "12341234",              # Repeated pattern
        ]

    @staticmethod
    def create_strong_passwords() -> List[str]:
        """Create list of strong passwords for testing"""
        return [
            "StrongPass123",
            "MySecure2023!",
            "Complex$Password9",
            "Test@123Password",
            "Secure&Pass456",
            "Password123!@#",
            "MyP@ssw0rd2023",
            "Str0ng&Secure!",
            "Test#Password99",
            "SecureP@ss2023"
        ]

    @staticmethod
    def create_unicode_data() -> Dict[str, Any]:
        """Create unicode test data"""
        return {
            "usernames": [
                "用户测试",
                "тестовый_пользователь",
                "utilisateur_test",
                "usuario_prueba",
                "테스트사용자",
                "ユーザーテスト",
                "اختبار_المستخدم",
                "משתמש_בדיקה"
            ],
            "passwords": [
                "密码Test123",
                "пароль123Test",
                "motDePasse123",
                "contraseña123",
                "비밀번호Test123",
                "パスワード123",
                "كلمة_السر123",
                "סיסמה123Test"
            ]
        }

class MockObjects:
    """Collection of mock objects for testing"""

    @staticmethod
    def create_mock_user(
        user_id: int = 1,
        username: str = "testuser",
        email: str = "<EMAIL>",
        is_admin: bool = False
    ) -> Mock:
        """Create a mock User object"""
        mock_user = Mock(spec=User)
        mock_user.id = user_id
        mock_user.username = username
        mock_user.email = email
        mock_user.password_hash = get_password_hash("password123")
        mock_user.is_active = True
        mock_user.is_admin = is_admin
        mock_user.avatar_url = None
        return mock_user

    @staticmethod
    def create_mock_db() -> Mock:
        """Create a mock database session"""
        mock_db = Mock(spec=Session)
        mock_db.commit = Mock()
        mock_db.rollback = Mock()
        mock_db.refresh = Mock()
        mock_db.delete = Mock()
        mock_db.execute = Mock()
        mock_db.query = Mock()
        return mock_db

    @staticmethod
    def create_mock_request() -> Mock:
        """Create a mock FastAPI request object"""
        mock_request = Mock()
        mock_request.headers = {"user-agent": "pytest"}
        mock_request.client = Mock()
        mock_request.client.host = "127.0.0.1"
        return mock_request

# Pytest fixtures

@pytest.fixture
def test_data_factory():
    """Provide TestDataFactory instance"""
    return TestDataFactory()

@pytest.fixture
def mock_objects():
    """Provide MockObjects instance"""
    return MockObjects()

@pytest.fixture
def malicious_payloads():
    """Provide malicious payloads for security testing"""
    return TestDataFactory.create_malicious_payloads()

@pytest.fixture
def weak_passwords():
    """Provide weak passwords for testing"""
    return TestDataFactory.create_weak_passwords()

@pytest.fixture
def strong_passwords():
    """Provide strong passwords for testing"""
    return TestDataFactory.create_strong_passwords()

@pytest.fixture
def unicode_data():
    """Provide unicode test data"""
    return TestDataFactory.create_unicode_data()

@pytest.fixture
def test_client():
    """Provide FastAPI test client"""
    return TestClient(app)

@pytest.fixture
def mock_user(mock_objects):
    """Provide a mock user"""
    return mock_objects.create_mock_user()

@pytest.fixture
def mock_admin_user(mock_objects):
    """Provide a mock admin user"""
    return mock_objects.create_mock_user(user_id=999, username="admin", is_admin=True)

@pytest.fixture
def mock_db(mock_objects):
    """Provide a mock database session"""
    return mock_objects.create_mock_db()

@pytest.fixture(scope="session")
def mysql_container():
    """Provide MySQL test container for integration tests"""
    container = MySqlContainer("mysql:8.0")
    container.start()
    yield container
    container.stop()

@pytest.fixture
def db_session(mysql_container):
    """Provide real database session for integration tests"""
    # Create database engine
    connection_url = mysql_container.get_connection_url()
    engine = create_engine(connection_url)
    
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    # Create session
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = SessionLocal()
    
    yield session
    
    session.close()
    engine.dispose()

@pytest.fixture
def authenticated_headers(test_client, test_data_factory):
    """Provide authenticated headers for API requests"""
    user_data = test_data_factory.create_user_data()
    
    # Register user
    test_client.post("/api/v1/auth/register", json={
        "username": user_data["username"],
        "email": user_data["email"],
        "password": user_data["password"]
    })
    
    # Login to get token
    response = test_client.post("/api/v1/auth/login", data={
        "username": user_data["username"],
        "password": user_data["password"]
    })
    
    token = response.json()["access_token"]
    
    return {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

@pytest.fixture
def sample_users(db_session):
    """Create sample users in the database"""
    users = []
    
    user_data_list = [
        {"username": "user1", "email": "<EMAIL>", "password": "pass123"},
        {"username": "user2", "email": "<EMAIL>", "password": "pass456"},
        {"username": "admin1", "email": "<EMAIL>", "password": "admin123", "is_admin": True},
    ]
    
    for user_data in user_data_list:
        user = User(
            username=user_data["username"],
            email=user_data["email"],
            password_hash=get_password_hash(user_data["password"]),
            is_admin=user_data.get("is_admin", False),
            is_active=True
        )
        db_session.add(user)
        users.append(user)
    
    db_session.commit()
    
    # Refresh to get IDs
    for user in users:
        db_session.refresh(user)
    
    return users

@pytest.fixture
def temp_directory():
    """Provide temporary directory for file operations"""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)

@pytest.fixture(autouse=True)
def cleanup_after_test(db_session):
    """Cleanup database after each test"""
    yield
    
    # Clean up all test data
    try:
        db_session.execute("SET FOREIGN_KEY_CHECKS = 0")
        for table in reversed(Base.metadata.sorted_tables):
            db_session.execute(f"DELETE FROM {table.name}")
        db_session.execute("SET FOREIGN_KEY_CHECKS = 1")
        db_session.commit()
    except Exception:
        db_session.rollback()

# Configuration for different test environments
class TestConfig:
    """Test configuration settings"""
    
    # Database settings
    DATABASE_URL = "sqlite:///./test.db"
    
    # Security settings
    SECRET_KEY = "test-secret-key-do-not-use-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES = 30
    
    # Test settings
    TESTING = True
    DEBUG = True
    
    # Rate limiting (for testing)
    RATE_LIMIT_ENABLED = False
    
    # Email settings (for testing)
    EMAIL_TESTING = True
    SEND_EMAILS = False

# pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line("markers", "slow: mark test as slow running")
    config.addinivalue_line("markers", "integration: mark test as integration test")
    config.addinivalue_line("markers", "security: mark test as security test")
    config.addinivalue_line("markers", "e2e: mark test as end-to-end test")

def pytest_collection_modifyitems(config, items):
    """Modify test collection"""
    # Add markers based on test file names
    for item in items:
        if "integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        if "security" in item.nodeid:
            item.add_marker(pytest.mark.security)
        if "e2e" in item.nodeid:
            item.add_marker(pytest.mark.e2e)

# Export commonly used items
__all__ = [
    "TestDataFactory",
    "MockObjects", 
    "TestConfig",
    "test_data_factory",
    "mock_objects",
    "malicious_payloads",
    "weak_passwords",
    "strong_passwords",
    "unicode_data",
    "test_client",
    "mock_user",
    "mock_admin_user",
    "mock_db",
    "mysql_container",
    "db_session",
    "authenticated_headers",
    "sample_users",
    "temp_directory",
    "cleanup_after_test"
]