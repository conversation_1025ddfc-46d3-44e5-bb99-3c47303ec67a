"""
检查并修复fanart字段长度问题
"""
import pymysql

def check_and_fix_fanart_field():
    """检查并修复fanart字段长度"""
    db_config = {
        'host': '***********',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'hentai',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("检查fanart字段结构...")
        
        # 检查字段定义
        cursor.execute("DESCRIBE animes")
        columns = cursor.fetchall()
        
        for col in columns:
            field, type_, null, key, default, extra = col
            if field == 'fanart':
                print(f"当前fanart字段: {field} {type_}")
        
        # 修改fanart字段为TEXT类型以支持长JSON
        print("修改fanart字段为TEXT类型...")
        cursor.execute("ALTER TABLE animes MODIFY COLUMN fanart TEXT")
        
        # 同时修改其他可能需要的字段
        print("优化其他字段...")
        cursor.execute("ALTER TABLE animes MODIFY COLUMN description TEXT")
        cursor.execute("ALTER TABLE animes MODIFY COLUMN cover VARCHAR(1000)")
        cursor.execute("ALTER TABLE animes MODIFY COLUMN video_url VARCHAR(1000)")
        
        connection.commit()
        
        # 再次检查字段
        cursor.execute("DESCRIBE animes")
        columns = cursor.fetchall()
        
        print("\n修改后的字段结构:")
        for col in columns:
            field, type_, null, key, default, extra = col
            if field in ['fanart', 'description', 'cover', 'video_url']:
                print(f"  {field}: {type_}")
        
        connection.close()
        print("\n字段修改完成!")
        
    except Exception as e:
        print(f"修改字段失败: {e}")

if __name__ == "__main__":
    check_and_fix_fanart_field()