from typing import Optional, List
from pydantic import BaseModel, Field
from datetime import datetime, date
from decimal import Decimal
from enum import Enum

# Import UserPublic schema for forward reference resolution
from app.schemas.user import UserPublic

# 枚举类型 - 使用标准的Python Enum
class MangaTypeEnum(str, Enum):
    SERIAL = "serial"  # 连载
    TANKOUBON = "tankoubon"  # 单行本
    DOUJINSHI = "doujinshi"  # 同人志

class MangaStatusEnum(str, Enum):
    ONGOING = "ongoing"  # 连载中
    COMPLETED = "completed"  # 已完结
    HIATUS = "hiatus"  # 休载
    CANCELLED = "cancelled"  # 已取消

class ChapterStatusEnum(str, Enum):
    DRAFT = "draft"  # 草稿
    PUBLISHED = "published"  # 已发布

# ==================== 漫画页面相关 Schema ====================

class MangaPageBase(BaseModel):
    page_number: int
    image_url: str
    width: Optional[int] = None
    height: Optional[int] = None
    file_size: Optional[int] = None

class MangaPageCreate(MangaPageBase):
    chapter_id: int

class MangaPage(MangaPageBase):
    id: int
    chapter_id: int
    created_at: datetime

    class Config:
        from_attributes = True

# ==================== 漫画章节相关 Schema ====================

class MangaChapterBase(BaseModel):
    title: Optional[str] = None
    chapter_number: Decimal
    volume_number: Optional[int] = None
    is_free: bool = True
    price: Decimal = 0
    status: ChapterStatusEnum = ChapterStatusEnum.PUBLISHED
    release_date: Optional[datetime] = None

class MangaChapterCreate(MangaChapterBase):
    manga_id: int

class MangaChapterUpdate(BaseModel):
    title: Optional[str] = None
    chapter_number: Optional[Decimal] = None
    volume_number: Optional[int] = None
    is_free: Optional[bool] = None
    price: Optional[Decimal] = None
    status: Optional[ChapterStatusEnum] = None
    release_date: Optional[datetime] = None

class MangaChapterInDB(MangaChapterBase):
    id: int
    manga_id: int
    page_count: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class MangaChapter(MangaChapterInDB):
    pages: List[MangaPage] = []

# ==================== 共享Schema定义 ====================

# 分类Schema
class CategoryBase(BaseModel):
    name: str
    num: int = 0

class Category(CategoryBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# 标签Schema
class TagBase(BaseModel):
    name: str
    name_english: Optional[str] = None
    description: Optional[str] = None

class Tag(TagBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# ==================== 漫画相关 Schema ====================

class MangaBase(BaseModel):
    title: str
    title_original: Optional[str] = None
    category_id: Optional[int] = None
    region_code: Optional[str] = None
    manga_type: Optional[MangaTypeEnum] = None
    author: Optional[str] = None
    artist: Optional[str] = None
    publisher: Optional[str] = None
    description: Optional[str] = None
    cover: Optional[str] = None
    banner: Optional[str] = None
    status: MangaStatusEnum = MangaStatusEnum.ONGOING
    is_active: bool = True
    release_date: Optional[date] = None

class MangaCreate(MangaBase):
    pass

class MangaUpdate(BaseModel):
    title: Optional[str] = None
    title_original: Optional[str] = None
    category_id: Optional[int] = None
    region_code: Optional[str] = None
    manga_type: Optional[MangaTypeEnum] = None
    author: Optional[str] = None
    artist: Optional[str] = None
    publisher: Optional[str] = None
    description: Optional[str] = None
    cover: Optional[str] = None
    banner: Optional[str] = None
    status: Optional[MangaStatusEnum] = None
    is_active: Optional[bool] = None
    release_date: Optional[date] = None

class MangaInDB(MangaBase):
    id: int
    view_count: int
    favorite_count: int
    chapter_count: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# 轻量级漫画Schema，用于列表响应，不包含章节信息避免N+1查询
class MangaBasic(MangaInDB):
    tags: Optional[List[Tag]] = []
    category: Optional[Category] = None

class Manga(MangaInDB):
    chapters: List[MangaChapter] = []
    tags: Optional[List[Tag]] = []
    category: Optional[Category] = None

# ==================== 阅读进度相关 Schema ====================

class MangaReadingProgressBase(BaseModel):
    page_number: int = 1
    total_pages: int = 0
    progress_percentage: float = 0.0

class MangaReadingProgressCreate(MangaReadingProgressBase):
    manga_id: int
    chapter_id: int

class MangaReadingProgressUpdate(BaseModel):
    page_number: Optional[int] = None
    total_pages: Optional[int] = None
    progress_percentage: Optional[float] = None
    chapter_id: Optional[int] = None

class MangaReadingProgress(MangaReadingProgressBase):
    id: int
    user_id: int
    manga_id: int
    chapter_id: int
    last_read_at: datetime
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class MangaReadingProgressWithDetails(MangaReadingProgress):
    manga: Optional[Manga] = None
    chapter: Optional[MangaChapter] = None
    user: Optional[UserPublic] = None

# ==================== 搜索和列表相关 Schema ====================

class MangaSearch(BaseModel):
    title: Optional[str] = None
    author: Optional[str] = None
    artist: Optional[str] = None
    category_id: Optional[int] = None
    region_code: Optional[str] = None
    manga_type: Optional[MangaTypeEnum] = None
    status: Optional[MangaStatusEnum] = None
    tags: Optional[List[str]] = []

class MangaListResponse(BaseModel):
    mangas: List[MangaBasic]
    total: int
    page: int
    limit: int
    total_pages: int

class MangaChapterListResponse(BaseModel):
    chapters: List[MangaChapter]
    total: int
    manga_id: int

class MangaPageListResponse(BaseModel):
    pages: List[MangaPage]
    total: int
    chapter_id: int

# Forward reference resolution
MangaBasic.model_rebuild()
Manga.model_rebuild()
MangaChapter.model_rebuild()
MangaReadingProgressWithDetails.model_rebuild()
Category.model_rebuild()
Tag.model_rebuild()