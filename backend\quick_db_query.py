#!/usr/bin/env python3
"""
直接SQL查询脚本 - 用于快速检查数据库状态
"""

import pymysql
import sys
from urllib.parse import urlparse

DATABASE_URL = "mysql+pymysql://sql23721_hentai:507877550%40lihao@************:3306/sql23721_hentai?charset=utf8mb4"

def parse_database_url(url):
    if url.startswith('mysql+pymysql://'):
        url = url.replace('mysql+pymysql://', 'mysql://')
    
    parsed = urlparse(url)
    
    return {
        'host': parsed.hostname,
        'port': parsed.port or 3306,
        'user': parsed.username,
        'password': parsed.password.replace('%40', '@') if parsed.password else None,
        'database': parsed.path.lstrip('/').split('?')[0]
    }

def execute_query(query, description=""):
    config = parse_database_url(DATABASE_URL)
    
    try:
        connection = pymysql.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password=config['password'],
            database=config['database'],
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            cursor.execute(query)
            results = cursor.fetchall()
            
            if description:
                print(f"\n=== {description} ===")
            print(f"Query: {query}")
            print(f"Results ({len(results)} rows):")
            
            if results:
                for i, row in enumerate(results, 1):
                    print(f"  {i}: {row}")
            else:
                print("  No results")
            
        connection.close()
        
    except Exception as e:
        print(f"Error executing query: {e}")

def main():
    print("数据库快速查询工具")
    print("=" * 40)
    
    # 预定义的查询
    queries = [
        ("SELECT COUNT(*) FROM animes", "动漫总数"),
        ("SELECT COUNT(*) FROM users", "用户总数"),
        ("SELECT COUNT(*) FROM comments", "评论总数"),
        ("SELECT id, title FROM animes WHERE id <= 5", "前5个动漫"),
        ("SELECT id, username FROM users", "所有用户"),
        ("SELECT c.id, c.anime_id, c.user_id, LENGTH(c.content) as content_length, c.created_at FROM comments c ORDER BY c.id", "所有评论概览"),
        ("SELECT COUNT(*) FROM comments WHERE anime_id = 1", "anime_id=1的评论数"),
        ("SELECT c.id, c.content, u.username FROM comments c JOIN users u ON c.user_id = u.id WHERE c.anime_id = 1", "anime_id=1的详细评论"),
    ]
    
    for query, desc in queries:
        execute_query(query, desc)
    
    print("\n" + "=" * 40)
    print("查询完成!")

if __name__ == "__main__":
    main()