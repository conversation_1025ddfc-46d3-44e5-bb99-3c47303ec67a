#!/usr/bin/env python3
"""
多年份配置示例和测试
"""

import sys
import yaml
sys.path.append('.')

from unified_crawler import UnifiedCrawler

def test_multi_year_with_real_crawler():
    """使用实际爬虫测试多年份配置"""
    print("Testing multi-year configuration with real crawler...")
    
    # 创建多年份测试配置
    test_configs = [
        {
            'name': '单年份单月份',
            'config': {
                'crawl': {
                    'date_filter': {
                        'year': 2025,
                        'month': 1
                    }
                }
            }
        },
        {
            'name': '单年份多月份',
            'config': {
                'crawl': {
                    'date_filter': {
                        'year': 2025,
                        'month': [1, 2, 3]
                    }
                }
            }
        },
        {
            'name': '多年份单月份',
            'config': {
                'crawl': {
                    'date_filter': {
                        'year': [2024, 2025],
                        'month': 1
                    }
                }
            }
        },
        {
            'name': '多年份多月份',
            'config': {
                'crawl': {
                    'date_filter': {
                        'year': [2023, 2024, 2025],
                        'month': [1, 2, 3]
                    }
                }
            }
        }
    ]
    
    for test_case in test_configs:
        print(f"\n=== {test_case['name']} ===")
        
        # 保存临时配置
        temp_config = test_case['config'].copy()
        temp_config.update({
            'database': {
                'host': '************:3306',
                'user': 'sql23721_hentai',
                'password': '507877550@lihao',
                'database': 'sql23721_hentai'
            }
        })
        
        temp_file = f'temp_{test_case["name"]}.yml'
        with open(temp_file, 'w', encoding='utf-8') as f:
            yaml.dump(temp_config, f, default_flow_style=False, allow_unicode=True)
        
        try:
            # 测试爬虫初始化
            crawler = UnifiedCrawler(temp_file)
            
            print(f"Target years: {crawler.TARGET_YEARS}")
            print(f"Target months: {crawler.TARGET_MONTHS}")
            print(f"Total combinations: {len(crawler.TARGET_YEARS)} × {len(crawler.TARGET_MONTHS)} = {len(crawler.TARGET_YEARS) * len(crawler.TARGET_MONTHS)}")
            
            # 验证配置正确性
            expected_years = temp_config['crawl']['date_filter']['year']
            expected_months = temp_config['crawl']['date_filter']['month']
            
            if isinstance(expected_years, list):
                assert crawler.TARGET_YEARS == expected_years
            else:
                assert crawler.TARGET_YEARS == [expected_years]
                
            if isinstance(expected_months, list):
                assert crawler.TARGET_MONTHS == expected_months
            else:
                assert crawler.TARGET_MONTHS == [expected_months]
            
            print("Configuration test PASSED")
            
        except Exception as e:
            print(f"Configuration test FAILED: {e}")
        finally:
            # 清理临时文件
            import os
            if os.path.exists(temp_file):
                os.remove(temp_file)

def show_usage_examples():
    """显示使用示例"""
    print("\n" + "="*60)
    print("多年份配置使用示例")
    print("="*60)
    
    examples = [
        {
            'title': '示例1: 爬取单个年份的多个月份',
            'yaml': '''
crawl:
  date_filter:
    year: 2025
    month: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]  # 全年
'''
        },
        {
            'title': '示例2: 爬取多个年份的指定月份',
            'yaml': '''
crawl:
  date_filter:
    year: [2023, 2024, 2025]  # 3年
    month: [6, 7, 8]          # 夏季月份
'''
        },
        {
            'title': '示例3: 爬取大范围年份的全年数据',
            'yaml': '''
crawl:
  date_filter:
    year: [2020, 2021, 2022, 2023, 2024, 2025]  # 6年
    month: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]  # 全年
    # 总共: 6年 × 12月 = 72个组合
'''
        },
        {
            'title': '示例4: 爬取特定年份的特定季节',
            'yaml': '''
crawl:
  date_filter:
    year: [2024, 2025]
    month: [12, 1, 2]  # 冬季月份（跨年）
'''
        }
    ]
    
    for example in examples:
        print(f"\n{example['title']}:")
        print(example['yaml'])
    
    print("\n配置说明:")
    print("- year: 可以是单个数字或数字数组")
    print("- month: 可以是单个数字或数字数组") 
    print("- 爬虫会自动处理所有年份和月份的组合")
    print("- 建议先测试小范围配置，确认正常后再扩大范围")

if __name__ == "__main__":
    print("Multi-Year Configuration Support Test")
    print("=" * 50)
    
    # 测试多年份配置
    test_multi_year_with_real_crawler()
    
    # 显示使用示例
    show_usage_examples()
    
    print("\n🎉 Multi-year configuration is now fully supported!")
    print("\nTo use multiple years in your config.yml:")
    print("1. Edit the date_filter section") 
    print("2. Set year to an array: year: [2023, 2024, 2025]")
    print("3. Set month to an array: month: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]")
    print("4. Run the crawler normally - it will process all combinations")