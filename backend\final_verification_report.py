#!/usr/bin/env python3
"""
数据库评论验证汇总报告
综合检查anime_id=1的评论数据是否存在
"""

import pymysql
import sys
import os
from urllib.parse import urlparse
from datetime import datetime
import json

DATABASE_URL = "mysql+pymysql://sql23721_hentai:507877550%40lihao@************:3306/sql23721_hentai?charset=utf8mb4"

def parse_database_url(url):
    if url.startswith('mysql+pymysql://'):
        url = url.replace('mysql+pymysql://', 'mysql://')
    
    parsed = urlparse(url)
    
    return {
        'host': parsed.hostname,
        'port': parsed.port or 3306,
        'user': parsed.username,
        'password': parsed.password.replace('%40', '@') if parsed.password else None,
        'database': parsed.path.lstrip('/').split('?')[0]
    }

def generate_final_report():
    print("数据库评论验证最终报告")
    print("=" * 50)
    print(f"报告生成时间: {datetime.now()}")
    print()
    
    config = parse_database_url(DATABASE_URL)
    print(f"数据库信息:")
    print(f"  主机: {config['host']}")
    print(f"  端口: {config['port']}")
    print(f"  数据库: {config['database']}")
    print()
    
    try:
        connection = pymysql.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password=config['password'],
            database=config['database'],
            charset='utf8mb4',
            connect_timeout=10
        )
        
        print("[SUCCESS] 数据库连接成功")
        print()
        
        with connection.cursor() as cursor:
            # 1. 基础数据统计
            print("1. 基础数据统计")
            print("-" * 30)
            
            cursor.execute("SELECT COUNT(*) FROM animes")
            anime_count = cursor.fetchone()[0]
            print(f"动漫总数: {anime_count}")
            
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            print(f"用户总数: {user_count}")
            
            cursor.execute("SELECT COUNT(*) FROM comments")
            total_comments = cursor.fetchone()[0]
            print(f"评论总数: {total_comments}")
            print()
            
            # 2. anime_id=1 的详细信息
            print("2. anime_id=1 的详细信息")
            print("-" * 30)
            
            cursor.execute("SELECT id, title, title_english, title_japanese FROM animes WHERE id = 1")
            anime_info = cursor.fetchone()
            
            if anime_info:
                print(f"动漫存在: YES")
                print(f"  ID: {anime_info[0]}")
                print(f"  标题: {anime_info[1]}")
                print(f"  英文标题: {anime_info[2] or 'N/A'}")
                print(f"  日文标题: {anime_info[3] or 'N/A'}")
            else:
                print(f"动漫存在: NO")
                print("  [ERROR] anime_id=1 在数据库中不存在")
            print()
            
            # 3. anime_id=1 的评论情况
            print("3. anime_id=1 的评论情况")
            print("-" * 30)
            
            cursor.execute("SELECT COUNT(*) FROM comments WHERE anime_id = 1 AND (is_deleted = 0 OR is_deleted IS NULL)")
            comment_count = cursor.fetchone()[0]
            print(f"评论数量: {comment_count}")
            
            if comment_count > 0:
                print("评论存在: YES")
                
                # 获取评论详情
                cursor.execute("""
                    SELECT c.id, c.user_id, c.content, c.attachments, c.created_at, u.username 
                    FROM comments c 
                    LEFT JOIN users u ON c.user_id = u.id 
                    WHERE c.anime_id = 1 AND (c.is_deleted = 0 OR c.is_deleted IS NULL)
                    ORDER BY c.created_at DESC
                """)
                comments = cursor.fetchall()
                
                print(f"评论详情:")
                for i, comment in enumerate(comments, 1):
                    print(f"  评论 #{i}:")
                    print(f"    ID: {comment[0]}")
                    print(f"    用户: {comment[5] or 'Unknown'} (ID: {comment[1]})")
                    print(f"    创建时间: {comment[4]}")
                    print(f"    内容长度: {len(comment[2])} 字符")
                    print(f"    内容预览: {'有emoji表情' if any(ord(char) > 127 for char in comment[2]) else comment[2][:50]}")
                    print(f"    附件: {'有' if comment[3] else '无'}")
                    
            else:
                print("评论存在: NO")
                print("  [INFO] anime_id=1 没有评论数据")
            print()
            
            # 4. 数据完整性检查
            print("4. 数据完整性检查")
            print("-" * 30)
            
            # 检查孤立评论
            cursor.execute("""
                SELECT COUNT(*) FROM comments c 
                LEFT JOIN users u ON c.user_id = u.id 
                WHERE u.id IS NULL
            """)
            orphaned_users = cursor.fetchone()[0]
            
            cursor.execute("""
                SELECT COUNT(*) FROM comments c 
                LEFT JOIN animes a ON c.anime_id = a.id 
                WHERE a.id IS NULL
            """)
            orphaned_animes = cursor.fetchone()[0]
            
            print(f"孤立评论(用户不存在): {orphaned_users}")
            print(f"孤立评论(动漫不存在): {orphaned_animes}")
            print(f"引用完整性: {'正常' if (orphaned_users == 0 and orphaned_animes == 0) else '有问题'}")
            print()
            
            # 5. API端点验证
            print("5. API端点验证")
            print("-" * 30)
            print("相关API端点:")
            print("  GET /api/comments/anime/1 - 获取anime_id=1的评论")
            print("  GET /api/comments/anime/1/threaded - 获取带回复的评论")
            print("  POST /api/comments/anime/1 - 发表新评论")
            print()
            
            # 6. 结论
            print("6. 结论")
            print("-" * 30)
            
            if anime_info and comment_count > 0:
                print("状态: [SUCCESS] 完全正常")
                print("结果: anime_id=1存在且有评论数据")
                print(f"具体: 发现{comment_count}条评论，数据完整")
                print("建议: 可以正常使用评论功能")
            elif anime_info and comment_count == 0:
                print("状态: [WARNING] 部分正常") 
                print("结果: anime_id=1存在但没有评论")
                print("具体: 动漫存在，但缺少评论数据")
                print("建议: 可以通过API或管理面板添加测试评论")
            else:
                print("状态: [ERROR] 有问题")
                print("结果: anime_id=1不存在或数据有问题")
                print("建议: 检查数据迁移或初始化过程")
        
        connection.close()
        
    except Exception as e:
        print(f"[ERROR] 验证过程失败: {e}")
        return False
    
    print()
    print("=" * 50)
    print("报告结束")
    return True

if __name__ == "__main__":
    generate_final_report()