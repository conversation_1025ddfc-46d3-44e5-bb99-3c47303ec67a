"""
getchu.com 剧照图片刮削模块
支持从getchu.com获取剧照图片
"""

import requests
from lxml import html
import urllib.parse
import os
import re

def search_getchu(search_term):
    """
    在getchu.com搜索指定的标题，返回最匹配的链接
    """
    # URL编码
    encoded_url = urllib.parse.quote_plus(search_term, encoding='cp932')
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    
    # 构建搜索URL
    getchu_url = f"https://www.getchu.com/php/search.phtml?genre=all&search_keyword={encoded_url}&gc=gc"
    print(f"搜索词: '{search_term}'")
    print(f"搜索URL: {getchu_url}")
    
    try:
        response = requests.get(getchu_url, headers=headers, timeout=30)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"请求失败，状态码: {response.status_code}")
            return None
        
        tree = html.fromstring(response.content)
        
        # 搜索所有包含soft.phtml?id=的链接
        soft_links = tree.xpath('//a[contains(@href, "soft.phtml?id=")]')
        print(f"找到 {len(soft_links)} 个soft.phtml链接")
        
        if not soft_links:
            print("没有找到任何soft.phtml链接")
            return None
        
        # 收集链接信息
        link_candidates = []
        for link in soft_links:
            href = link.get('href')
            text = link.text_content().strip() if link.text_content() else ""
            if text:  # 只考虑有文本的链接
                link_candidates.append((href, text))
        
        print(f"\n有效链接候选: {len(link_candidates)} 个")
        
        # 寻找最佳匹配
        best_match = None
        best_score = -1
        
        for href, text in link_candidates:
            score = 0
            
            # 完全匹配得最高分
            if search_term == text:
                score = 1000
                print(f"✓ 完全匹配: '{text}'")
            elif search_term.lower() == text.lower():
                score = 900
                print(f"✓ 忽略大小写完全匹配: '{text}'")
            elif search_term in text:
                score = 500
                print(f"✓ 包含搜索词: '{text}'")
            else:
                # 计算关键词匹配度
                search_words = search_term.split()
                matched_words = 0
                for word in search_words:
                    if word in text:
                        matched_words += 1
                        score += 50
                
                print(f"部分匹配: '{text}' (匹配 {matched_words}/{len(search_words)} 个词)")
            
            print(f"  链接: {href}, 分数: {score}")
            
            if score > best_score:
                best_score = score
                best_match = href
        
        if best_match:
            print(f"\n✓ 选择最佳匹配: {best_match} (分数: {best_score})")
            
            # 处理链接格式
            if best_match.startswith('../'):
                best_match = best_match[3:]
            
            if not best_match.startswith('http'):
                final_url = f"https://www.getchu.com/{best_match}"
            else:
                final_url = best_match
            
            return final_url
        else:
            print("没有找到合适的匹配")
            return None
            
    except Exception as e:
        print(f"发生错误: {e}")
        return None
def get_getchu_info(result):
    """
    获取getchu.com的详细信息，特别是样本图片链接
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    
    try:
        # 检查URL是否包含gc=gc参数，如果没有则添加（绕过年龄认证）
        if '&gc=gc' not in result and '?gc=gc' not in result:
            if '?' in result:
                result = result + '&gc=gc'
            else:
                result = result + '?gc=gc'
        
        print(f"请求URL: {result}")
        response = requests.get(result, headers=headers, timeout=30)
        if response.status_code != 200:
            print(f"请求失败，状态码: {response.status_code}")
            return None
        
        tree = html.fromstring(response.content)
        print(f"成功获取页面: {result}")
        
        # 保存HTML内容用于调试
        with open('debug_getchu_detail.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("HTML内容已保存到 debug_getchu_detail.html")
        
        # 检查是否仍然是年龄认证页面
        age_verification = tree.xpath('//h1[contains(text(), "年齢認証")]')
        if age_verification:
            print("警告：仍然是年龄认证页面，无法获取图片")
            return None
        
        # 尝试多种XPath选择器来获取图片链接
        selectors = [
            '//*[@id="soft_table"]//td/a[contains(@href, ".jpg")]/@href',
            '//div[@align="center"]//a[contains(@href, ".jpg")]/@href'
            # '//a[contains(@href, "sample") and contains(@href, ".jpg")]/@href',
            # '//div[contains(@style, "margin-top")]//a[contains(@href, ".jpg")]/@href'

        ]
        
        all_image_links = []
        
        for i, selector in enumerate(selectors, 1):
            try:
                image_links = tree.xpath(selector)
                print(f"选择器 {i}: {selector}")
                print(f"  找到 {len(image_links)} 个链接")
                if image_links:
                    for link in image_links[:3]:  # 只显示前3个
                        print(f"    {link}")
                    all_image_links.extend(image_links)
            except Exception as e:
                print(f"选择器 {i} 执行失败: {e}")
        
        # 去重
        unique_links = list(set(all_image_links))
        print(f"\n总共找到 {len(unique_links)} 个唯一的图片链接")
        
        # 处理链接格式，转换为完整URL
        full_image_urls = []
        base_url = "https://www.getchu.com/"
        
        for link in unique_links:
            # 去掉开头的"./"
            if link.startswith('./'):
                link = link[2:]
            
            # 如果不是完整URL，添加基础URL
            if not link.startswith('http'):
                full_url = base_url + link
            else:
                full_url = link
            
            full_image_urls.append(full_url)
            print(f"图片链接: {full_url}")
        
        return full_image_urls
        
    except Exception as e:
        print(f"获取页面信息时发生错误: {e}")
        return None

def download_images(image_urls, title):
    """
    下载图片并保存到extrafanart文件夹，根据规则重命名
    """
    if not image_urls:
        print("没有图片需要下载")
        return
    
    # 创建extrafanart文件夹
    extrafanart_dir = "extrafanart"
    if not os.path.exists(extrafanart_dir):
        os.makedirs(extrafanart_dir)
        print(f"创建文件夹: {extrafanart_dir}")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Referer': 'https://www.getchu.com/'
    }
    
    downloaded_count = 0
    
    for url in image_urls:
        try:
            print(f"下载图片: {url}")
            response = requests.get(url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                # 从URL中提取文件名
                filename = url.split('/')[-1]
                
                # 处理重命名逻辑
                if 'package.jpg' in filename:
                    # package.jpg保存到当前目录，重命名为标题-fanart.jpg
                    new_filename = f"{title}-fanart.jpg"
                    save_path = new_filename
                else:
                    # 检查是否是sample图片
                    sample_match = re.search(r'sample(\d+)\.jpg', filename)
                    if sample_match:
                        sample_num = sample_match.group(1)
                        new_filename = f"fanart{sample_num}.jpg"
                        save_path = os.path.join(extrafanart_dir, new_filename)
                    else:
                        # 其他图片保持原名
                        save_path = os.path.join(extrafanart_dir, filename)
                
                # 保存文件
                with open(save_path, 'wb') as f:
                    f.write(response.content)
                
                print(f"✓ 保存成功: {save_path}")
                downloaded_count += 1
            else:
                print(f"✗ 下载失败: {url} (状态码: {response.status_code})")
                
        except Exception as e:
            print(f"✗ 下载出错: {url} - {e}")
    
    print(f"\n下载完成！成功下载 {downloaded_count} 张图片")

# 测试
if __name__ == "__main__":
    search_term = 'OVA いまりあ ＃5'
    result = search_getchu(search_term)
    if result:
        image_urls = get_getchu_info(result)
        if image_urls:
            download_images(image_urls, search_term)
        else:
            print("没有找到图片链接")
    else:
        print("搜索失败")

