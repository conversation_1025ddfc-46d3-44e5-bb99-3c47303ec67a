"""
Redis/DragonflyDB配置管理模块
从数据库动态读取和管理Redis/DragonflyDB配置
支持Redis和DragonflyDB（完全兼容Redis协议）
"""
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models import SystemConfig
from app.core.database import get_db
import json
import logging

logger = logging.getLogger(__name__)

class RedisConfigManager:
    """Redis/DragonflyDB配置管理器
    DragonflyDB与Redis协议完全兼容，无需特殊处理
    """
    
    # Redis配置键名
    REDIS_ENABLED = "redis_enabled"
    REDIS_HOST = "redis_host"
    REDIS_PORT = "redis_port"
    REDIS_DB = "redis_db"
    REDIS_PASSWORD = "redis_password"
    REDIS_MAX_MEMORY = "redis_max_memory"  # MB单位，0表示无限制
    REDIS_EXPIRE_TIME = "redis_expire_time"  # 默认过期时间（秒）
    REDIS_MAX_CONNECTIONS = "redis_max_connections"  # 连接池最大连接数
    
    # 默认配置
    DEFAULT_CONFIG = {
        REDIS_ENABLED: "false",
        REDIS_HOST: "localhost",
        REDIS_PORT: "6379",
        REDIS_DB: "0",
        REDIS_PASSWORD: "",
        REDIS_MAX_MEMORY: "0",  # 0表示无限制
        REDIS_EXPIRE_TIME: "3600",  # 1小时
        REDIS_MAX_CONNECTIONS: "50"
    }
    
    @classmethod
    def get_config(cls, db: Session, key: str) -> Optional[str]:
        """获取单个配置值"""
        try:
            config = db.query(SystemConfig).filter(SystemConfig.key == key).first()
            if config:
                return config.value
            # 如果没有配置，返回默认值
            return cls.DEFAULT_CONFIG.get(key)
        except Exception as e:
            logger.error(f"获取Redis配置失败 {key}: {e}")
            return cls.DEFAULT_CONFIG.get(key)
    
    @classmethod
    def get_all_config(cls, db: Session) -> Dict[str, Any]:
        """获取所有Redis配置"""
        config_dict = {}
        
        for key, default_value in cls.DEFAULT_CONFIG.items():
            value = cls.get_config(db, key)
            config_dict[key] = value if value is not None else default_value
        
        # 类型转换
        config_dict[cls.REDIS_ENABLED] = config_dict[cls.REDIS_ENABLED].lower() == "true"
        config_dict[cls.REDIS_PORT] = int(config_dict[cls.REDIS_PORT])
        config_dict[cls.REDIS_DB] = int(config_dict[cls.REDIS_DB])
        config_dict[cls.REDIS_MAX_MEMORY] = int(config_dict[cls.REDIS_MAX_MEMORY])
        config_dict[cls.REDIS_EXPIRE_TIME] = int(config_dict[cls.REDIS_EXPIRE_TIME])
        config_dict[cls.REDIS_MAX_CONNECTIONS] = int(config_dict[cls.REDIS_MAX_CONNECTIONS])
        
        return config_dict
    
    @classmethod
    def set_config(cls, db: Session, key: str, value: str, description: str = None) -> bool:
        """设置配置值"""
        try:
            config = db.query(SystemConfig).filter(SystemConfig.key == key).first()
            
            if config:
                # 更新现有配置
                config.value = value
                if description:
                    config.description = description
            else:
                # 创建新配置
                config = SystemConfig(
                    key=key,
                    value=value,
                    description=description or f"Redis配置: {key}"
                )
                db.add(config)
            
            db.commit()
            logger.info(f"Redis配置更新成功: {key} = {value}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"设置Redis配置失败 {key}: {e}")
            return False
    
    @classmethod
    def update_configs(cls, db: Session, configs: Dict[str, Any]) -> bool:
        """批量更新配置"""
        try:
            for key, value in configs.items():
                if key in cls.DEFAULT_CONFIG:
                    # 转换为字符串存储
                    str_value = str(value).lower() if isinstance(value, bool) else str(value)
                    cls.set_config(db, key, str_value)
            
            db.commit()
            logger.info("Redis配置批量更新成功")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"批量更新Redis配置失败: {e}")
            return False
    
    @classmethod
    def initialize_default_config(cls, db: Session) -> None:
        """初始化默认配置（如果不存在）"""
        for key, value in cls.DEFAULT_CONFIG.items():
            existing = db.query(SystemConfig).filter(SystemConfig.key == key).first()
            if not existing:
                description_map = {
                    cls.REDIS_ENABLED: "是否启用Redis/DragonflyDB缓存",
                    cls.REDIS_HOST: "Redis/DragonflyDB服务器地址",
                    cls.REDIS_PORT: "Redis/DragonflyDB服务器端口",
                    cls.REDIS_DB: "Redis/DragonflyDB数据库编号",
                    cls.REDIS_PASSWORD: "Redis/DragonflyDB连接密码（可选）",
                    cls.REDIS_MAX_MEMORY: "Redis/DragonflyDB最大内存限制（MB，0为无限制）",
                    cls.REDIS_EXPIRE_TIME: "默认缓存过期时间（秒）",
                    cls.REDIS_MAX_CONNECTIONS: "Redis/DragonflyDB连接池最大连接数"
                }
                
                config = SystemConfig(
                    key=key,
                    value=value,
                    description=description_map.get(key, f"Redis/DragonflyDB配置: {key}")
                )
                db.add(config)
        
        try:
            db.commit()
            logger.info("Redis默认配置初始化成功")
        except Exception as e:
            db.rollback()
            logger.error(f"初始化Redis默认配置失败: {e}")
    
    @classmethod
    def get_redis_url(cls, db: Session) -> Optional[str]:
        """构建Redis连接URL"""
        config = cls.get_all_config(db)
        
        if not config[cls.REDIS_ENABLED]:
            return None
        
        password = config[cls.REDIS_PASSWORD]
        host = config[cls.REDIS_HOST]
        port = config[cls.REDIS_PORT]
        db_num = config[cls.REDIS_DB]
        
        if password:
            return f"redis://:{password}@{host}:{port}/{db_num}"
        else:
            return f"redis://{host}:{port}/{db_num}"
    
    @classmethod
    def validate_config(cls, configs: Dict[str, Any]) -> tuple[bool, str]:
        """验证配置有效性"""
        # 验证端口号
        if 'redis_port' in configs:
            try:
                port = int(configs['redis_port'])
                if port < 1 or port > 65535:
                    return False, "端口号必须在1-65535之间"
            except:
                return False, "端口号必须是有效的数字"
        
        # 验证数据库编号
        if 'redis_db' in configs:
            try:
                db_num = int(configs['redis_db'])
                if db_num < 0 or db_num > 15:
                    return False, "数据库编号必须在0-15之间"
            except:
                return False, "数据库编号必须是有效的数字"
        
        # 验证内存限制
        if 'redis_max_memory' in configs:
            try:
                memory = int(configs['redis_max_memory'])
                if memory < 0:
                    return False, "内存限制不能为负数"
            except:
                return False, "内存限制必须是有效的数字"
        
        # 验证过期时间
        if 'redis_expire_time' in configs:
            try:
                expire = int(configs['redis_expire_time'])
                if expire < 0:
                    return False, "过期时间不能为负数"
            except:
                return False, "过期时间必须是有效的数字"
        
        # 验证最大连接数
        if 'redis_max_connections' in configs:
            try:
                connections = int(configs['redis_max_connections'])
                if connections < 1:
                    return False, "最大连接数至少为1"
            except:
                return False, "最大连接数必须是有效的数字"
        
        return True, "配置有效"