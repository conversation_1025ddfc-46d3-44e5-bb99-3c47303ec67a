from sqlalchemy.orm import Session, joinedload
from sqlalchemy import desc, or_, func, case
from typing import Optional, List
import json
import re
from app.models import Anime, Favorite, Tag
from app.schemas.anime import AnimeCreate, AnimeUpdate

class AnimeCRUD:
    @staticmethod
    def _serialize_fanart(fanart) -> Optional[str]:
        """将fanart转换为数据库存储格式"""
        if fanart is None:
            return None
        if isinstance(fanart, list):
            return json.dumps(fanart) if fanart else None
        return str(fanart)
    
    @staticmethod
    def _deserialize_fanart(fanart_str: Optional[str]):
        """从数据库读取fanart并转换为列表格式"""
        if not fanart_str:
            return None
        try:
            # 尝试解析为JSON
            parsed = json.loads(fanart_str)
            return parsed if isinstance(parsed, list) else [fanart_str]
        except (json.JSONDecodeError, TypeError):
            # 如果不是JSON，尝试按逗号分割
            if isinstance(fanart_str, str):
                if ',' in fanart_str:
                    return [url.strip() for url in fanart_str.split(',') if url.strip()]
                return [fanart_str] if fanart_str.strip() else None
            # 如果已经是列表，直接返回过滤后的结果
            elif isinstance(fanart_str, list):
                return [url for url in fanart_str if url and str(url).strip()]
            return None
    
    @staticmethod
    def get_anime_by_id(db: Session, anime_id: int) -> Optional[Anime]:
        anime = db.query(Anime).options(joinedload(Anime.tags)).filter(Anime.id == anime_id).first()
        if anime and anime.fanart:
            # 转换fanart为前端期望的格式
            anime.fanart = AnimeCRUD._deserialize_fanart(anime.fanart)
        return anime
    
    @staticmethod
    def get_animes(
        db: Session,
        skip: int = 0,
        limit: int = 20,
        category_id: Optional[int] = None,
        search: Optional[str] = None,
    ) -> dict:
        # 允许 is_active 为 NULL 的老数据也能显示
        query = db.query(Anime).options(joinedload(Anime.tags))

        if category_id is not None:
            query = query.filter(Anime.category_id == category_id)
            
        if search:
            # 简化但修复的搜索逻辑 - 主要修复中文搜索问题
            search_term = f"%{search}%"
            
            # 使用LIKE替代ILIKE解决中文字符问题，并添加去空格搜索
            search_no_space = search.replace(" ", "")
            search_no_space_term = f"%{search_no_space}%"
            
            # 构建搜索条件
            search_conditions = []
            
            # 主标题匹配 - 使用LIKE避免中文问题
            search_conditions.append(Anime.title.like(search_term))
            search_conditions.append(Anime.title.like(search_no_space_term))  # 处理空格问题
            
            # 英文标题匹配 - 保持ILIKE
            search_conditions.append(Anime.title_english.ilike(search_term))
            
            # 日文标题匹配 - 使用LIKE
            search_conditions.append(Anime.title_japanese.like(search_term))
            
            # 标签匹配 - 使用LIKE
            search_conditions.append(Tag.name.like(search_term))
            
            # 执行搜索查询
            query = query.outerjoin(Anime.tags).filter(
                or_(*search_conditions)
            ).distinct()
            
            # 按创建时间排序
            query = query.order_by(desc(Anime.created_at))

        # 获取总数 - 简化计数查询
        if search:
            # 使用相同的简化搜索条件
            search_term = f"%{search}%"
            search_no_space = search.replace(" ", "")
            search_no_space_term = f"%{search_no_space}%"
            
            count_conditions = [
                Anime.title.like(search_term),
                Anime.title.like(search_no_space_term),
                Anime.title_english.ilike(search_term),
                Anime.title_japanese.like(search_term),
                Tag.name.like(search_term),
            ]
            
            count_query = db.query(Anime).outerjoin(Anime.tags).filter(
                or_(*count_conditions)
            ).distinct()
            total = count_query.count()
        else:
            total = query.count()
        
        # 获取分页数据 - 确保所有查询都按最新时间排序
        if not search:
            # 对于非搜索查询，按创建时间降序排列（最新的在前面）
            query = query.order_by(desc(Anime.created_at))
        
        animes = query.offset(skip).limit(limit).all()
        
        # 转换所有anime的fanart字段
        for anime in animes:
            if anime.fanart:
                anime.fanart = AnimeCRUD._deserialize_fanart(anime.fanart)
        
        return {"animes": animes, "total": total}
    
    @staticmethod
    def search_animes(db: Session, title: str, skip: int = 0, limit: int = 20) -> List[Anime]:
        # 使用简化但修复的搜索逻辑
        search_term = f"%{title}%"
        search_no_space = title.replace(" ", "")
        search_no_space_term = f"%{search_no_space}%"
        
        # 构建搜索条件 - 与get_animes保持一致但简化
        search_conditions = [
            Anime.title.like(search_term),
            Anime.title.like(search_no_space_term),  # 处理空格问题
            Anime.title_english.ilike(search_term),
            Anime.title_japanese.like(search_term),
            Tag.name.like(search_term),
        ]
        
        # 执行查询
        animes = db.query(Anime).options(joinedload(Anime.tags)).outerjoin(Anime.tags).filter(
            or_(*search_conditions)
        ).distinct().order_by(desc(Anime.created_at)).offset(skip).limit(limit).all()
        
        # 转换所有anime的fanart字段
        for anime in animes:
            if anime.fanart:
                anime.fanart = AnimeCRUD._deserialize_fanart(anime.fanart)
        
        return animes
    
    @staticmethod
    def create_anime(db: Session, anime: AnimeCreate) -> Anime:
        anime_data = anime.dict()
        # 序列化fanart字段
        if anime_data.get('fanart'):
            anime_data['fanart'] = AnimeCRUD._serialize_fanart(anime_data['fanart'])
        
        db_anime = Anime(**anime_data)
        db.add(db_anime)
        db.commit()
        db.refresh(db_anime)
        
        # 返回时反序列化fanart供前端使用
        if db_anime.fanart:
            db_anime.fanart = AnimeCRUD._deserialize_fanart(db_anime.fanart)
        
        return db_anime
    
    @staticmethod
    def update_anime(db: Session, anime_id: int, anime_update: AnimeUpdate) -> Optional[Anime]:
        anime = db.query(Anime).filter(Anime.id == anime_id).first()
        if not anime:
            return None
        
        update_data = anime_update.dict(exclude_unset=True)
        # 序列化fanart字段
        if 'fanart' in update_data:
            update_data['fanart'] = AnimeCRUD._serialize_fanart(update_data['fanart'])
        
        for field, value in update_data.items():
            setattr(anime, field, value)
        
        db.commit()
        db.refresh(anime)
        
        # 返回时反序列化fanart供前端使用
        if anime.fanart:
            anime.fanart = AnimeCRUD._deserialize_fanart(anime.fanart)
        
        return anime
    
    @staticmethod
    def _extract_series_info(title: str) -> tuple[str, int]:
        """简化的动漫系列信息提取
        
        返回: (基本标题, 序号)
        例如: "魅魔再教育物語 1" -> ("魅魔再教育物語", 1)
        """
        if not title:
            return title, 0
        
        title = title.strip()
        
        # 简化的正则表达式，专注于最常见的格式
        # 模式1: "标题 数字" (有空格)
        match = re.match(r'^(.+?)\s+(\d+)$', title)
        if match:
            base_title = match.group(1).strip()
            episode_num = int(match.group(2))
            return base_title, episode_num
        
        # 模式2: "标题数字" (无空格，但至少3个字符的基本标题)
        match = re.match(r'^(.{3,})(\d+)$', title)
        if match:
            base_title = match.group(1).strip()
            episode_num = int(match.group(2))
            return base_title, episode_num
        
        # 如果没有匹配到，返回原标题
        return title, 0
    
    @staticmethod
    def get_anime_recommendations(db: Session, anime_id: int, limit: int = 12) -> List[Anime]:
        """简化的动漫推荐算法
        
        推荐策略：
        1. 优先推荐同系列动漫（相同基本标题，不同集数）
        2. 然后推荐相同标签的随机动漫
        3. 最后补充热门动漫
        """
        current_anime = AnimeCRUD.get_anime_by_id(db, anime_id)
        if not current_anime:
            return []
        
        recommendations = []
        used_ids = {anime_id}
        
        # 1. 同系列推荐
        base_title, current_episode = AnimeCRUD._extract_series_info(current_anime.title)
        
        if current_episode > 0:  # 如果成功解析出集数
            # 查找相同基本标题的其他动漫
            series_candidates = db.query(Anime).options(joinedload(Anime.tags)).filter(
                Anime.id != anime_id,
                or_(Anime.is_active == True, Anime.is_active.is_(None)),
                or_(
                    Anime.title.like(f"{base_title} %"),  # "基本标题 数字"
                    Anime.title.like(f"{base_title}%"),   # "基本标题数字"
                )
            ).all()
            
            # 解析每个候选动漫的集数并排序
            series_with_episodes = []
            for anime in series_candidates:
                _, episode_num = AnimeCRUD._extract_series_info(anime.title)
                if episode_num > 0:  # 只包含有效集数的动漫
                    series_with_episodes.append((anime, episode_num))
            
            # 按集数升序排序，优先推荐后续集数
            series_with_episodes.sort(key=lambda x: x[1])
            
            # 添加到推荐列表
            for anime, episode_num in series_with_episodes:
                if anime.id not in used_ids and len(recommendations) < limit:
                    if anime.fanart:
                        anime.fanart = AnimeCRUD._deserialize_fanart(anime.fanart)
                    recommendations.append(anime)
                    used_ids.add(anime.id)
        
        # 2. 标签推荐（如果系列推荐不够）
        if len(recommendations) < limit and current_anime.tags:
            tag_ids = [tag.id for tag in current_anime.tags]
            tag_based = db.query(Anime).options(joinedload(Anime.tags)).join(
                Anime.tags
            ).filter(
                Anime.id != anime_id,
                or_(Anime.is_active == True, Anime.is_active.is_(None)),
                Tag.id.in_(tag_ids)
            ).group_by(Anime.id).order_by(
                func.random()
            ).limit(limit - len(recommendations)).all()
            
            for anime in tag_based:
                if anime.id not in used_ids and len(recommendations) < limit:
                    if anime.fanart:
                        anime.fanart = AnimeCRUD._deserialize_fanart(anime.fanart)
                    recommendations.append(anime)
                    used_ids.add(anime.id)
        
        # 3. 热门动漫补充
        if len(recommendations) < limit:
            popular_animes = db.query(Anime).options(joinedload(Anime.tags)).filter(
                Anime.id != anime_id,
                or_(Anime.is_active == True, Anime.is_active.is_(None))
            ).order_by(Anime.view_count.desc()).limit(limit - len(recommendations)).all()
            
            for anime in popular_animes:
                if anime.id not in used_ids and len(recommendations) < limit:
                    if anime.fanart:
                        anime.fanart = AnimeCRUD._deserialize_fanart(anime.fanart)
                    recommendations.append(anime)
                    used_ids.add(anime.id)
        
        return recommendations[:limit]
    
    @staticmethod
    def increment_view_count(db: Session, anime_id: int):
        try:
            anime = db.query(Anime).filter(Anime.id == anime_id).first()
            if anime:
                anime.view_count += 1
                db.commit()
        except Exception as e:
            print(f"Error incrementing view count for anime {anime_id}: {e}")
            db.rollback()

class FavoriteCRUD:
    @staticmethod
    def get_user_favorites(db: Session, user_id: int, skip: int = 0, limit: int = 20) -> List[Favorite]:
        return db.query(Favorite).options(joinedload(Favorite.anime)).filter(
            Favorite.user_id == user_id
        ).order_by(desc(Favorite.created_at)).offset(skip).limit(limit).all()
    
    @staticmethod
    def get_favorite(db: Session, user_id: int, anime_id: int) -> Optional[Favorite]:
        return db.query(Favorite).filter(
            Favorite.user_id == user_id,
            Favorite.anime_id == anime_id
        ).first()
    
    @staticmethod
    def add_favorite(db: Session, user_id: int, anime_id: int) -> Favorite:
        db_favorite = Favorite(user_id=user_id, anime_id=anime_id)
        db.add(db_favorite)
        
        # 更新动漫收藏数
        anime = AnimeCRUD.get_anime_by_id(db, anime_id)
        if anime:
            anime.favorite_count += 1
        
        db.commit()
        db.refresh(db_favorite)
        return db_favorite
    
    @staticmethod
    def remove_favorite(db: Session, user_id: int, anime_id: int) -> bool:
        favorite = FavoriteCRUD.get_favorite(db, user_id, anime_id)
        if not favorite:
            return False
        
        db.delete(favorite)
        
        # 更新动漫收藏数 - 直接查询数据库对象，避免fanart字段序列化问题
        anime = db.query(Anime).filter(Anime.id == anime_id).first()
        if anime and anime.favorite_count > 0:
            anime.favorite_count -= 1
        
        db.commit()
        return True

    # 扩展的收藏功能，支持动漫和漫画
    @staticmethod
    def get_user_favorites_by_type(db: Session, user_id: int, content_type: str, skip: int = 0, limit: int = 20) -> List[Favorite]:
        """获取用户收藏列表（按内容类型）"""
        query = db.query(Favorite).filter(
            Favorite.user_id == user_id,
            Favorite.content_type == content_type
        )
        
        if content_type == "anime":
            query = query.options(joinedload(Favorite.anime))
        elif content_type == "manga":
            query = query.options(joinedload(Favorite.manga))
        
        return query.order_by(desc(Favorite.created_at)).offset(skip).limit(limit).all()
    
    @staticmethod
    def get_user_favorites_count(db: Session, user_id: int) -> int:
        """获取用户收藏总数"""
        return db.query(Favorite).filter(Favorite.user_id == user_id).count()
    
    @staticmethod
    def get_all_user_favorites(db: Session, user_id: int, skip: int = 0, limit: int = 20) -> List[Favorite]:
        """获取用户所有收藏列表"""
        try:
            favorites = db.query(Favorite).options(
                joinedload(Favorite.anime),
                joinedload(Favorite.manga)
            ).filter(
                Favorite.user_id == user_id
            ).order_by(desc(Favorite.created_at)).offset(skip).limit(limit).all()
            
            # 转换为可序列化的格式
            result = []
            for fav in favorites:
                fav_dict = {
                    'id': fav.id,
                    'user_id': fav.user_id,
                    'content_type': fav.content_type.value if hasattr(fav.content_type, 'value') else str(fav.content_type),
                    'anime_id': fav.anime_id,
                    'manga_id': fav.manga_id,
                    'created_at': fav.created_at.isoformat() if fav.created_at else None,
                    'anime': None,
                    'manga': None
                }
                
                # 添加关联数据
                if fav.anime:
                    fav_dict['anime'] = {
                        'id': fav.anime.id,
                        'title': fav.anime.title,
                        'cover': fav.anime.cover
                    }
                
                if fav.manga:
                    fav_dict['manga'] = {
                        'id': fav.manga.id,
                        'title': fav.manga.title,
                        'cover': fav.manga.cover
                    }
                
                result.append(fav_dict)
            
            return result
            
        except Exception as e:
            print(f'get_all_user_favorites 错误: {e}')
            import traceback
            traceback.print_exc()
            return []
    
    @staticmethod
    def get_favorite_by_content(db: Session, user_id: int, content_type: str, content_id: int) -> Optional[Favorite]:
        """检查用户是否收藏了特定内容"""
        query = db.query(Favorite).filter(
            Favorite.user_id == user_id,
            Favorite.content_type == content_type
        )
        
        if content_type == "anime":
            query = query.filter(Favorite.anime_id == content_id)
        elif content_type == "manga":
            query = query.filter(Favorite.manga_id == content_id)
        
        return query.first()
    
    @staticmethod
    def add_favorite_by_content(db: Session, user_id: int, content_type: str, content_id: int) -> Favorite:
        """添加收藏"""
        from app.models import ContentType
        
        # 创建收藏记录
        favorite_data = {
            "user_id": user_id,
            "content_type": ContentType.ANIME if content_type == "anime" else ContentType.MANGA
        }
        
        if content_type == "anime":
            favorite_data["anime_id"] = content_id
        elif content_type == "manga":
            favorite_data["manga_id"] = content_id
        
        db_favorite = Favorite(**favorite_data)
        db.add(db_favorite)
        
        # 更新收藏数
        if content_type == "anime":
            from app.crud.anime import AnimeCRUD
            anime = AnimeCRUD.get_anime_by_id(db, content_id)
            if anime:
                anime.favorite_count += 1
        elif content_type == "manga":
            from app.crud.manga import MangaCRUD
            manga = MangaCRUD.get_manga_basic(db, content_id)
            if manga:
                manga.favorite_count += 1
        
        db.commit()
        db.refresh(db_favorite)
        return db_favorite
    
    @staticmethod
    def remove_favorite_by_content(db: Session, user_id: int, content_type: str, content_id: int) -> bool:
        """取消收藏"""
        favorite = FavoriteCRUD.get_favorite_by_content(db, user_id, content_type, content_id)
        if not favorite:
            return False
        
        db.delete(favorite)
        
        # 更新收藏数 - 直接查询数据库对象，避免fanart字段序列化问题
        if content_type == "anime":
            # 直接查询数据库对象，不使用get_anime_by_id方法（该方法会修改fanart字段）
            anime = db.query(Anime).filter(Anime.id == content_id).first()
            if anime and anime.favorite_count > 0:
                anime.favorite_count -= 1
        elif content_type == "manga":
            from app.crud.manga import MangaCRUD
            manga = MangaCRUD.get_manga_basic(db, content_id)
            if manga and manga.favorite_count > 0:
                manga.favorite_count -= 1
        
        db.commit()
        return True