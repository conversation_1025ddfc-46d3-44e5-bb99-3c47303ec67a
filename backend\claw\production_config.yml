# 正式爬取配置文件
# Hanime1.me 视频抓取和处理系统配置文件

# ========== 全局配置 ==========
app:
  name: "Hanime1.me 正式爬取系统"
  version: "1.0.0"
  author: "Production Team"

# ========== 爬取配置 ==========
crawl:
  # 目标网站
  base_url: "https://hanime1.me"

  # 爬取目标日期配置 - 支持单个或多个年份/月份
  date_filter:
    # 年份配置 - 支持单个年份或年份数组
    year: 2003  # 单个年份
    # year: [2003, 2004, 2005]  # 多个年份示例
    # year: [2000, 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010]  # 大范围年份
    
    # 月份配置 - 支持单个月份或月份数组
    month: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]  # 全年12个月
    # month: [1, 2, 3]  # 前3个月示例
    # month: 6  # 单个月份示例
    
    # 使用示例:
    # 1. 爬取2003年全年: year: 2003, month: [1,2,3,4,5,6,7,8,9,10,11,12]
    # 2. 爬取2003-2005年全年: year: [2003,2004,2005], month: [1,2,3,4,5,6,7,8,9,10,11,12] 
    # 3. 爬取多年特定月份: year: [2003,2004,2005], month: [6,7,8]
    # 4. 爬取2000-2010年数据: year: [2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010], month: [1,2,3,4,5,6,7,8,9,10,11,12]

  # 搜索配置
  search:
    genre: "%E8%A3%8F%E7%95%AA"  # 里番类型的URL编码
    sort: ""                      # 排序方式
    type: ""                      # 视频类型

  # 质量优先级 (从高到低)
  quality_priority:
    - "1080"
    - "720"
    - "480"

  # 跳过关键词
  skip_keywords:
    - "中字後補"
    - "简中补字"
    - "Chinese Sub"
    - "中文字幕後補"

# ========== 下载配置 ==========
download:
  # 下载目录
  download_dir: "downloads"       # 下载根目录
  temp_dir: "temp"               # 临时文件目录

  # 目录组织
  organize_by_date: true         # 是否按日期组织目录 (年/月)

  # 下载选项
  enable_video: true             # 是否下载视频
  enable_cover: true             # 是否下载封面
  enable_fanart: true            # 是否下载高清图片（getchu剧照）

  # 下载优化
  chunk_size: 8192              # 下载块大小(字节)
  max_concurrent: 1             # 最大并发下载数（建议设为1避免被封）

  # 文件命名
  clean_filename: true          # 是否清理文件名中的非法字符

# ========== Web访问配置 ==========
web_access:
  # 域名前缀配置 - 用于生成完整的可访问URL
  domain_prefix: "https://static.hxsl.org"  # 静态文件域名前缀
  
  # URL路径配置
  base_path: ""                 # 基础路径 (可选，如: "/anime" 或 "/static")
  
  # 示例: 最终URL格式
  # https://static.denlu.top/2025/01/视频标题/fanart.jpg
  # https://static.denlu.top/2025/01/视频标题/extrafanart/fanart1.jpg

# ========== 网络配置 ==========
network:
  # 代理配置
  proxy:
    enabled: false              # 是否启用代理
    http: ""                    # HTTP代理地址
    https: ""                   # HTTPS代理地址
    socks5: ""                  # SOCKS5代理地址

  # 请求配置
  timeout: 30                   # 请求超时时间(秒)
  max_retries: 3               # 最大重试次数
  retry_delay: 2               # 重试延迟(秒)

  # 用户代理
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

# ========== 数据库配置 ==========
database:
  host: "************:3306"
  user: "sql23721_hentai"
  password: "507877550@lihao"
  database: "sql23721_hentai"
  charset: "utf8mb4"

# ========== Selenium配置 ==========
selenium:
  # Chrome选项
  headless: true                # 是否无头模式
  no_sandbox: true             # 是否禁用沙箱
  disable_dev_shm: true        # 是否禁用/dev/shm使用

  # 等待时间
  implicit_wait: 5             # 隐式等待时间(秒)
  page_load_timeout: 30        # 页面加载超时(秒)

# ========== 爬取策略 ==========
strategy:
  # 爬取模式
  mode: "full"                 # full: 完整爬取, test: 测试模式, resume: 恢复爬取
  
  # 爬取限制
  max_videos_per_month: 0      # 每月最大爬取视频数（0表示无限制）
  start_from_page: 1           # 从第几页开始爬取
  
  # 错误处理
  continue_on_error: true      # 遇到错误时是否继续
  skip_existing: true          # 是否跳过已存在的视频

# ========== Getchu配置 ==========
getchu:
  enabled: true                # 是否启用getchu剧照下载
  search_timeout: 30           # 搜索超时时间
  download_timeout: 30         # 下载超时时间
  max_images_per_video: 50     # 每个视频最大剧照数量

# ========== 日志配置 ==========
logging:
  level: "INFO"                # 日志级别: DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "production_crawl.log" # 日志文件路径
  max_size: "50MB"            # 日志文件最大大小
  backup_count: 10            # 日志文件备份数量
  console_output: true        # 是否输出到控制台

# ========== 性能配置 ==========
performance:
  # 内存优化
  gc_threshold: 100           # 垃圾回收阈值
  
  # 并发控制
  max_concurrent_downloads: 1  # 最大并发下载数
  request_delay: 1            # 请求间隔(秒)，避免被封
  
  # 资源限制
  max_memory_mb: 2048         # 最大内存使用(MB)
