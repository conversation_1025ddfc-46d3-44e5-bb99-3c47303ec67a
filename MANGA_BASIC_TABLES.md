# 🎭 漫画模块基础表设计 - 简化版

## 📋 设计原则

基于用户反馈，采用**简化优先**的设计策略：
- ✅ 复用现有表结构（User、Category、Tag、Favorite、Comment）
- ✅ 只创建必要的基础表，不预设复杂优化
- ✅ 评论只到漫画级别，不细分到章节
- ✅ 暂不分区、不创建额外索引、不设评分系统
- ✅ 为后续扩展预留空间

## 🏗️ 新增表结构

### 1. 漫画主表 (`mangas`)

```sql
CREATE TABLE mangas (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(500) NOT NULL COMMENT '漫画标题',
    title_original VARCHAR(500) COMMENT '原版标题',
    
    -- 分类信息（复用现有系统）
    category_id INTEGER COMMENT '复用categories表，用于漫画分类',
    
    -- 基本信息
    region_code VARCHAR(10) NOT NULL COMMENT '地区代码：jp/kr/cn',
    manga_type ENUM('serial', 'tankoubon', 'doujinshi') NOT NULL COMMENT '漫画类型：系列/单行本/同人志',
    
    author VARCHAR(200) COMMENT '作者',
    artist VARCHAR(200) COMMENT '画师', 
    publisher VARCHAR(200) COMMENT '出版社',
    description TEXT COMMENT '简介',
    
    -- 图片
    cover VARCHAR(1000) COMMENT '封面图片URL',
    banner VARCHAR(1000) COMMENT '横幅图片URL',
    
    -- 统计信息
    view_count BIGINT DEFAULT 0 COMMENT '浏览次数',
    favorite_count INTEGER DEFAULT 0 COMMENT '收藏次数',
    chapter_count INTEGER DEFAULT 0 COMMENT '章节总数',
    
    -- 状态
    status ENUM('ongoing', 'completed', 'hiatus', 'cancelled') DEFAULT 'ongoing' COMMENT '连载状态',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    
    -- 时间
    release_date DATE COMMENT '发布日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 外键
    FOREIGN KEY (category_id) REFERENCES categories(id)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2. 漫画章节表 (`manga_chapters`)

```sql
CREATE TABLE manga_chapters (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    manga_id BIGINT NOT NULL,
    
    title VARCHAR(300) NOT NULL COMMENT '章节标题',
    chapter_number DECIMAL(8,2) NOT NULL COMMENT '章节号，支持小数如1.5',
    volume_number INTEGER COMMENT '卷号',
    
    -- 内容信息
    page_count INTEGER DEFAULT 0 COMMENT '页面数量',
    
    -- 权限控制
    is_free BOOLEAN DEFAULT TRUE COMMENT '是否免费',
    price DECIMAL(8,2) DEFAULT 0.00 COMMENT '价格',
    
    -- 状态
    status ENUM('draft', 'published') DEFAULT 'published' COMMENT '发布状态',
    release_date DATETIME COMMENT '发布时间',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 外键和约束
    UNIQUE KEY uk_manga_chapter (manga_id, chapter_number),
    FOREIGN KEY (manga_id) REFERENCES mangas(id) ON DELETE CASCADE
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3. 漫画页面表 (`manga_pages`)

```sql
CREATE TABLE manga_pages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    chapter_id BIGINT NOT NULL,
    
    page_number INTEGER NOT NULL COMMENT '页码',
    image_url VARCHAR(1000) NOT NULL COMMENT '图片URL',
    
    -- 图片基本信息
    width INTEGER COMMENT '图片宽度',
    height INTEGER COMMENT '图片高度',
    file_size INTEGER COMMENT '文件大小（字节）',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    UNIQUE KEY uk_chapter_page (chapter_id, page_number),
    FOREIGN KEY (chapter_id) REFERENCES manga_chapters(id) ON DELETE CASCADE
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 4. 漫画标签关联表 (`manga_tags`)

```sql
CREATE TABLE manga_tags (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    manga_id BIGINT NOT NULL,
    tag_id INTEGER NOT NULL COMMENT '复用tags表',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束（复用现有tags表）
    UNIQUE KEY uk_manga_tag (manga_id, tag_id),
    FOREIGN KEY (manga_id) REFERENCES mangas(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 5. 用户阅读进度表 (`manga_reading_progress`)

```sql
CREATE TABLE manga_reading_progress (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INTEGER NOT NULL COMMENT '用户ID，匹配users表',
    manga_id BIGINT NOT NULL COMMENT '漫画ID',  
    chapter_id BIGINT NOT NULL COMMENT '当前阅读章节',
    
    -- 阅读位置
    page_number INTEGER DEFAULT 1 COMMENT '当前页码',
    read_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '阅读进度百分比',
    
    -- 阅读状态
    is_completed BOOLEAN DEFAULT FALSE COMMENT '是否读完',
    
    -- 同步信息（为防抖机制预留）
    sync_version INTEGER DEFAULT 1 COMMENT '同步版本号',
    device_type VARCHAR(20) COMMENT '设备类型：web/android/ios',
    
    -- 时间
    last_read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键和索引
    INDEX idx_user_id (user_id),
    INDEX idx_manga_id (manga_id), 
    INDEX idx_chapter_id (chapter_id),
    UNIQUE KEY uk_user_manga (user_id, manga_id),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (manga_id) REFERENCES mangas(id) ON DELETE CASCADE,
    FOREIGN KEY (chapter_id) REFERENCES manga_chapters(id) ON DELETE CASCADE
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 🔧 扩展现有表

### 1. 扩展收藏表 (`favorites`)

```sql
-- 添加支持漫画的字段
ALTER TABLE favorites 
ADD COLUMN content_type ENUM('anime', 'manga') DEFAULT 'anime' COMMENT '内容类型' AFTER anime_id,
ADD COLUMN manga_id BIGINT NULL COMMENT '漫画ID（当content_type为manga时使用）' AFTER content_type,
ADD FOREIGN KEY (manga_id) REFERENCES mangas(id) ON DELETE CASCADE;
```

### 2. 扩展评论表 (`comments`)

```sql
-- 添加支持漫画的字段（仅到漫画级别，不到章节）
ALTER TABLE comments
ADD COLUMN content_type ENUM('anime', 'manga') DEFAULT 'anime' COMMENT '内容类型' AFTER anime_id,
ADD COLUMN manga_id BIGINT NULL COMMENT '漫画ID（当content_type为manga时使用）' AFTER content_type,
ADD FOREIGN KEY (manga_id) REFERENCES mangas(id) ON DELETE CASCADE;
```

### 3. 扩展标签表 (`tags`)

```sql
-- 添加适用范围字段
ALTER TABLE tags 
ADD COLUMN applicable_to SET('anime', 'manga') DEFAULT 'anime' COMMENT '适用内容类型';
```

## 📝 初始化数据

### 预设漫画分类数据

```sql
-- 在现有categories表中添加漫画分类
INSERT INTO categories (name, num) VALUES 
('日本漫画', 0),
('韩国漫画', 0), 
('中国漫画', 0),
('同人志', 0),
('单行本', 0);
```

### 预设漫画标签数据

```sql
-- 在现有tags表中添加漫画专用标签，设置适用范围
INSERT INTO tags (name, name_english, description, applicable_to) VALUES
('热血', 'Hot Blood', '热血战斗题材', 'anime,manga'),
('恋爱', 'Romance', '恋爱题材', 'anime,manga'),
('校园', 'School Life', '校园生活', 'anime,manga'),
('奇幻', 'Fantasy', '奇幻魔法', 'anime,manga'),
('日常', 'Slice of Life', '日常生活', 'anime,manga'),
('武侠', 'Wuxia', '中国武侠', 'manga'),
('同人', 'Doujinshi', '同人创作', 'manga');
```

## 💡 使用示例

### 1. 创建日本连载漫画

```sql
-- 插入漫画信息
INSERT INTO mangas (
    title, title_original, category_id, region_code, manga_type,
    author, description, status, release_date
) VALUES (
    '火影忍者', 'NARUTO', 1, 'jp', 'serial',
    '岸本齐史', '忍者世界的成长故事', 'completed', '1999-09-21'
);

-- 添加标签
INSERT INTO manga_tags (manga_id, tag_id) VALUES 
(1, 1), -- 热血
(1, 3); -- 校园
```

### 2. 创建章节和页面

```sql
-- 创建第一章
INSERT INTO manga_chapters (manga_id, title, chapter_number, page_count, release_date) 
VALUES (1, '漩涡鸣人登场', 1.0, 20, '1999-09-21 10:00:00');

-- 添加页面
INSERT INTO manga_pages (chapter_id, page_number, image_url, width, height) VALUES
(1, 1, '/images/manga/1/1/page1.jpg', 800, 1200),
(1, 2, '/images/manga/1/1/page2.jpg', 800, 1200),
(1, 3, '/images/manga/1/1/page3.jpg', 800, 1200);
```

### 3. 用户收藏和进度

```sql
-- 用户收藏漫画
INSERT INTO favorites (user_id, content_type, manga_id) 
VALUES (1, 'manga', 1);

-- 记录阅读进度
INSERT INTO manga_reading_progress (user_id, manga_id, chapter_id, page_number, read_percentage, device_type)
VALUES (1, 1, 1, 3, 15.0, 'web');
```

### 4. 漫画评论

```sql
-- 用户对漫画进行评论
INSERT INTO comments (user_id, content_type, manga_id, content) 
VALUES (1, 'manga', 1, '这部漫画真的很精彩！');
```

## 🔍 基础查询示例

### 1. 获取漫画列表

```sql
SELECT m.*, c.name as category_name 
FROM mangas m 
LEFT JOIN categories c ON m.category_id = c.id 
WHERE m.is_active = TRUE 
ORDER BY m.created_at DESC;
```

### 2. 获取用户阅读历史

```sql
SELECT m.title, m.cover, mrp.last_read_at, mrp.read_percentage
FROM manga_reading_progress mrp
JOIN mangas m ON mrp.manga_id = m.id
WHERE mrp.user_id = ?
ORDER BY mrp.last_read_at DESC;
```

### 3. 获取漫画章节列表

```sql
SELECT * FROM manga_chapters 
WHERE manga_id = ? AND status = 'published' 
ORDER BY chapter_number;
```

### 4. 获取章节页面

```sql
SELECT * FROM manga_pages 
WHERE chapter_id = ? 
ORDER BY page_number;
```

### 5. 获取漫画标签

```sql
SELECT t.name, t.name_english 
FROM manga_tags mt
JOIN tags t ON mt.tag_id = t.id
WHERE mt.manga_id = ?;
```

## 🚀 后续扩展预留

此基础架构为后续优化预留了空间：

1. **分区扩展**：表结构支持后续添加分区策略
2. **索引优化**：可根据实际查询需求添加合适索引
3. **评分系统**：可在不影响现有结构的情况下添加评分表
4. **缓存层**：可在业务层添加Redis缓存
5. **防抖同步**：sync_version和device_type字段已预留
6. **多语言**：title_original和title_english字段支持多语言

## ✨ 设计优势

1. **简单实用**：只包含必要功能，易于理解和维护
2. **复用现有**：最大化利用已有的User、Category、Tag等表
3. **扩展友好**：为后续优化预留充足空间
4. **数据完整**：支持完整的漫画阅读流程
5. **跨平台**：支持不同设备的进度同步基础

这个简化设计专注于核心功能，为项目快速启动提供坚实基础！