from typing import Optional, List, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime, date
import json

# Import UserPublic schema for forward reference resolution
from app.schemas.user import UserPublic

# 动漫相关Schema
class AnimeBase(BaseModel):
    title: str
    title_english: Optional[str] = None
    title_japanese: Optional[str] = None
    description: Optional[str] = None
    cover: Optional[str] = None
    fanart: Optional[Union[str, List[str]]] = None
    video_url: str
    release_year: Optional[int] = None
    release_date: Optional[date] = None
    is_active: Optional[bool] = True
    category_id: Optional[int] = None
    
    @validator('fanart', pre=True)
    def parse_fanart(cls, v):
        if v is None:
            return None
        if isinstance(v, str):
            # 尝试解析JSON字符串
            try:
                parsed = json.loads(v)
                return parsed if isinstance(parsed, list) else [v]
            except (json.JSONDecodeError, TypeError):
                # 如果不是JSON，尝试按逗号分割
                if ',' in v:
                    return [url.strip() for url in v.split(',') if url.strip()]
                return [v] if v.strip() else None
        elif isinstance(v, list):
            return [url.strip() for url in v if url and url.strip()]
        return v

class AnimeCreate(AnimeBase):
    pass

class AnimeUpdate(BaseModel):
    title: Optional[str] = None
    title_english: Optional[str] = None
    title_japanese: Optional[str] = None
    description: Optional[str] = None
    cover: Optional[str] = None
    fanart: Optional[Union[str, List[str]]] = None
    video_url: Optional[str] = None
    release_year: Optional[int] = None
    release_date: Optional[date] = None
    is_active: Optional[bool] = None
    category_id: Optional[int] = None
    
    @validator('fanart', pre=True)
    def parse_fanart(cls, v):
        if v is None:
            return None
        if isinstance(v, str):
            # 尝试解析JSON字符串
            try:
                parsed = json.loads(v)
                return parsed if isinstance(parsed, list) else [v]
            except (json.JSONDecodeError, TypeError):
                # 如果不是JSON，尝试按逗号分割
                if ',' in v:
                    return [url.strip() for url in v.split(',') if url.strip()]
                return [v] if v.strip() else None
        elif isinstance(v, list):
            return [url.strip() for url in v if url and url.strip()]
        return v

class AnimeInDB(AnimeBase):
    id: int
    view_count: int
    favorite_count: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class Anime(AnimeInDB):
    tags: Optional[List["Tag"]] = []

# 收藏相关Schema
class FavoriteBase(BaseModel):
    anime_id: Optional[int] = None
    manga_id: Optional[int] = None

class FavoriteCreate(FavoriteBase):
    pass

class FavoriteInDB(FavoriteBase):
    id: int
    user_id: int
    content_type: str
    created_at: datetime
    
    class Config:
        from_attributes = True

class Favorite(FavoriteInDB):
    anime: Optional[Anime] = None
    manga: Optional[dict] = None  # 临时使用dict，避免循环导入

# 剧照Schema
class StillBase(BaseModel):
    anime_id: int
    image_url: str
    sort_order: Optional[int] = 0

class StillCreate(StillBase):
    pass

class StillInDB(StillBase):
    id: int
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

class Still(StillInDB):
    pass

# 标签Schema
class TagBase(BaseModel):
    name: str
    name_english: Optional[str] = None
    description: Optional[str] = None

class TagCreate(TagBase):
    pass

class TagUpdate(BaseModel):
    name: Optional[str] = None
    name_english: Optional[str] = None
    description: Optional[str] = None

class TagInDB(TagBase):
    id: int
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

class Tag(TagInDB):
    pass

# 评论相关Schema
class CommentBase(BaseModel):
    anime_id: Optional[int] = None
    manga_id: Optional[int] = None
    content: str
    attachments: Optional[List[str]] = None  # 表情/图片URL数组
    
    # 回复功能相关字段
    parent_id: Optional[int] = None  # 父评论ID（用于回复）
    reply_to_user_id: Optional[int] = None  # 回复的目标用户ID
    
    # 引用功能相关字段
    quoted_comment_id: Optional[int] = None  # 被引用的评论ID
    quoted_content: Optional[str] = None  # 被引用评论的内容快照

class CommentCreate(CommentBase):
    pass

class CommentUpdate(BaseModel):
    content: Optional[str] = None
    attachments: Optional[List[str]] = None
    parent_id: Optional[int] = None
    reply_to_user_id: Optional[int] = None
    quoted_comment_id: Optional[int] = None
    quoted_content: Optional[str] = None

class CommentInDB(CommentBase):
    id: int
    user_id: int
    is_deleted: bool
    # 点赞统计字段
    like_count: int = 0
    dislike_count: int = 0
    # 编辑功能相关字段
    is_edited: bool = False
    edited_at: Optional[datetime] = None
    edit_count: int = 0
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class CommentLikeBase(BaseModel):
    is_like: bool  # True for like, False for dislike

class CommentLikeCreate(CommentLikeBase):
    pass

class CommentLike(CommentLikeBase):
    id: int
    comment_id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class Comment(CommentInDB):
    # 关联的用户信息
    user: Optional[UserPublic] = None
    reply_to_user: Optional[UserPublic] = None
    
    # 子评论列表（用于树形结构显示）
    replies: Optional[List["Comment"]] = []
    
    # 引用的评论信息（简化版本，避免循环引用）
    quoted_comment: Optional[dict] = None
    
    # 当前用户的点赞状态（在API中动态添加）
    user_like_status: Optional[bool] = None  # True=点赞, False=反对, None=未投票

# Forward reference resolution
Comment.model_rebuild()