// 本地存储工具，用于游客进度
export const localStorageUtils = {
  // 保存游客阅读进度
  saveGuestProgress: (mangaId: number, chapterId: number, pageNumber: number) => {
    try {
      const key = `manga_progress_${mangaId}`;
      const progress = {
        mangaId,
        chapterId,
        pageNumber,
        lastReadAt: Date.now(),
      };
      localStorage.setItem(key, JSON.stringify(progress));
    } catch (error) {
      console.error('Failed to save guest progress:', error);
    }
  },

  // 获取游客阅读进度
  getGuestProgress: (mangaId: number) => {
    try {
      const key = `manga_progress_${mangaId}`;
      const stored = localStorage.getItem(key);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error('Failed to get guest progress:', error);
      return null;
    }
  },

  // 清除游客阅读进度
  clearGuestProgress: (mangaId: number) => {
    try {
      const key = `manga_progress_${mangaId}`;
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Failed to clear guest progress:', error);
    }
  }
};

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// 图片懒加载预加载工具
export class ImagePreloader {
  private cache = new Set<string>();
  private loadingPromises = new Map<string, Promise<void>>();
  private failedUrls = new Set<string>();
  private priorityQueue: { url: string; priority: 'high' | 'low' }[] = [];
  private isProcessing = false;
  private maxConcurrent = 3; // 最大并发加载数
  private currentLoading = 0;

  async preloadImages(urls: string[], priority: 'high' | 'low' = 'low') {
    // 添加到优先级队列
    urls.forEach(url => {
      if (!this.cache.has(url) && !this.failedUrls.has(url) && !this.loadingPromises.has(url)) {
        this.priorityQueue.push({ url, priority });
      }
    });

    // 按优先级排序
    this.priorityQueue.sort((a, b) => {
      if (a.priority === 'high' && b.priority === 'low') return -1;
      if (a.priority === 'low' && b.priority === 'high') return 1;
      return 0;
    });

    // 开始处理队列
    if (!this.isProcessing) {
      this.processQueue();
    }
  }

  private async processQueue() {
    this.isProcessing = true;

    while (this.priorityQueue.length > 0 && this.currentLoading < this.maxConcurrent) {
      const item = this.priorityQueue.shift();
      if (!item) continue;

      this.currentLoading++;
      this.loadImage(item.url, item.priority).finally(() => {
        this.currentLoading--;
        // 继续处理队列
        if (this.priorityQueue.length > 0) {
          this.processQueue();
        } else if (this.currentLoading === 0) {
          this.isProcessing = false;
        }
      });
    }

    if (this.priorityQueue.length === 0 && this.currentLoading === 0) {
      this.isProcessing = false;
    }
  }

  private loadImage(url: string, priority: 'high' | 'low' = 'low'): Promise<void> {
    if (this.cache.has(url)) {
      return Promise.resolve();
    }

    if (this.failedUrls.has(url)) {
      return Promise.reject(new Error(`Image previously failed to load: ${url}`));
    }

    if (this.loadingPromises.has(url)) {
      return this.loadingPromises.get(url)!;
    }

    const promise = new Promise<void>((resolve, reject) => {
      const img = new Image();
      
      // 设置加载优先级
      if (priority === 'high') {
        img.loading = 'eager';
        img.fetchPriority = 'high';
      } else {
        img.loading = 'lazy';
        img.fetchPriority = 'low';
      }

      // 设置超时
      const timeout = setTimeout(() => {
        this.loadingPromises.delete(url);
        this.failedUrls.add(url);
        reject(new Error(`Image load timeout: ${url}`));
      }, priority === 'high' ? 10000 : 15000); // 高优先级10秒，低优先级15秒

      img.onload = () => {
        clearTimeout(timeout);
        this.cache.add(url);
        this.loadingPromises.delete(url);
        resolve();
      };
      
      img.onerror = () => {
        clearTimeout(timeout);
        this.loadingPromises.delete(url);
        this.failedUrls.add(url);
        reject(new Error(`Failed to load image: ${url}`));
      };
      
      img.src = url;
    });

    this.loadingPromises.set(url, promise);
    return promise;
  }

  isLoaded(url: string): boolean {
    return this.cache.has(url);
  }

  isFailed(url: string): boolean {
    return this.failedUrls.has(url);
  }

  isLoading(url: string): boolean {
    return this.loadingPromises.has(url);
  }

  // 清理失败的URL，允许重试
  retryFailedUrl(url: string) {
    this.failedUrls.delete(url);
  }

  // 获取缓存统计信息
  getStats() {
    return {
      cached: this.cache.size,
      loading: this.loadingPromises.size,
      failed: this.failedUrls.size,
      queued: this.priorityQueue.length
    };
  }

  // 清理缓存
  clearCache() {
    this.cache.clear();
    this.failedUrls.clear();
    this.priorityQueue = [];
  }
}