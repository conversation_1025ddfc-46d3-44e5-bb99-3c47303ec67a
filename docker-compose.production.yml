version: '3.8'

services:
  # 数据库服务
  postgres:
    image: postgres:15-alpine
    container_name: anime_postgres
    environment:
      POSTGRES_DB: anime_website
      POSTGRES_USER: anime_user
      POSTGRES_PASSWORD: your_secure_password_here
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - anime_network

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: anime_redis
    command: redis-server --appendonly yes --requirepass your_redis_password_here
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - anime_network

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    container_name: anime_backend
    environment:
      - DATABASE_URL=***************************************************************/anime_website
      - REDIS_URL=redis://:your_redis_password_here@redis:6379/0
    env_file:
      - ./backend/.env.production
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
      - ./backend/backups:/app/backups
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - anime_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.production
    container_name: anime_frontend
    env_file:
      - ./frontend/.env.production
    ports:
      - "3000:3000"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - anime_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: anime_nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./backend/uploads:/var/www/uploads
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    networks:
      - anime_network

volumes:
  postgres_data:
  redis_data:

networks:
  anime_network:
    driver: bridge
