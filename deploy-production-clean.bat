@echo off
REM 动漫网站生产环境一键部署脚本 (Windows版)
REM 包含文件清理和安全配置

echo 🚀 开始动漫网站生产环境部署...

REM 1. 清理开发文件
echo 📁 清理开发和调试文件...

REM 删除调试文件
if exist "backend\claw\debug_*" del /q "backend\claw\debug_*" 2>nul
if exist "backend\manhua\debug_*" del /q "backend\manhua\debug_*" 2>nul

REM 删除日志文件
if exist "backend\claw\*.log" del /q "backend\claw\*.log" 2>nul

REM 删除临时文件
if exist "backend\claw\temp" rmdir /s /q "backend\claw\temp" 2>nul
if exist "backend\claw\temp_*.yml" del /q "backend\claw\temp_*.yml" 2>nul

REM 删除压缩包
if exist "backend\app.zip" del /q "backend\app.zip" 2>nul
if exist "frontend\src.zip" del /q "frontend\src.zip" 2>nul

REM 删除测试数据
if exist "backend\comments_export.json" del /q "backend\comments_export.json" 2>nul

echo ✅ 清理完成

REM 2. 检查环境变量
if not exist ".env.production" (
    echo ❌ 错误: 未找到 .env.production 文件
    echo 请创建 .env.production 文件并配置生产环境变量
    pause
    exit /b 1
)

echo 📋 检查环境变量...
findstr /C:"your-super-secret-jwt-key" /C:"rootpassword" /C:"password" .env.production >nul
if %errorlevel%==0 (
    echo ❌ 警告: 发现默认密码，请修改为安全密码
    pause
    exit /b 1
)

echo ✅ 环境变量检查通过

REM 3. 构建和启动服务
echo 🔨 构建Docker镜像...
docker-compose -f docker-compose.prod.yml build --no-cache

echo 🚀 启动生产环境服务...
docker-compose -f docker-compose.prod.yml up -d

REM 4. 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 30 /nobreak >nul

REM 5. 数据库初始化
echo 🗄️ 初始化数据库...
docker-compose -f docker-compose.prod.yml exec backend python -m alembic upgrade head

REM 6. 健康检查
echo 🏥 进行健康检查...
curl -f http://localhost:8000/api/v1 >nul 2>&1
if %errorlevel%==0 (
    echo ✅ 后端服务正常
) else (
    echo ❌ 后端服务启动失败
    pause
    exit /b 1
)

curl -f http://localhost:3000 >nul 2>&1
if %errorlevel%==0 (
    echo ✅ 前端服务正常
) else (
    echo ❌ 前端服务启动失败
    pause
    exit /b 1
)

echo.
echo 🎉 部署完成!
echo 📱 前端访问: http://localhost:3000
echo 🔌 后端API: http://localhost:8000
echo.
echo 📝 重要提醒:
echo 1. 配置反向代理和SSL证书
echo 2. 设置防火墙规则
echo 3. 配置域名解析
echo 4. 设置定期备份
echo.

REM 显示服务状态
docker-compose -f docker-compose.prod.yml ps

pause