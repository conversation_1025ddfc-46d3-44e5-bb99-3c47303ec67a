'use client';

import { useParams, useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function RedirectToChapterPage() {
  const params = useParams();
  const router = useRouter();
  
  const mangaId = params.id as string;
  const chapterId = params.chapterId as string;

  useEffect(() => {
    // 重定向到正确的章节页面路径
    if (mangaId && chapterId) {
      router.replace(`/manga/${mangaId}/chapter/${chapterId}`);
    }
  }, [mangaId, chapterId, router]);

  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
        <p className="mt-4 text-muted-foreground">正在跳转到章节阅读...</p>
      </div>
    </div>
  );
}