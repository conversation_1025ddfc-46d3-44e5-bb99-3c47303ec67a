from datetime import datetime, timedelta
from typing import Any, Union
from passlib.context import Crypt<PERSON>ontext
from jose import jwt
from werkzeug.security import check_password_hash as werkzeug_check
from app.core.config import settings

# 密码哈希上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_access_token(subject: Union[str, Any], expires_delta: timedelta = None):
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码，支持bcrypt和werkzeug两种格式"""
    try:
        # 首先尝试passlib bcrypt验证
        if pwd_context.verify(plain_password, hashed_password):
            return True
    except Exception:
        pass
    
    try:
        # 如果bcrypt失败，尝试werkzeug格式验证
        if werkzeug_check(hashed_password, plain_password):
            return True
    except Exception:
        pass
    
    return False

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)