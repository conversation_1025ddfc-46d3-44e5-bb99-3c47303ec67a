'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { AnimeCard } from '@/components/anime/AnimeCard';
import { Pagination } from '@/components/ui/Pagination';
import { Anime, apiClient } from '@/lib/api';

export default function SearchPageContent() {
  const searchParams = useSearchParams();
  const [animes, setAnimes] = useState<Anime[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalResults, setTotalResults] = useState(0);
  const [hasSearched, setHasSearched] = useState(false);

  // 从URL参数中获取搜索词并自动搜索
  useEffect(() => {
    const urlSearchTerm = searchParams.get('search');
    if (urlSearchTerm) {
      setSearchTerm(urlSearchTerm);
      setHasSearched(true);
      
      // 使用async函数来确保状态更新后执行搜索
      const performSearch = async () => {
        try {
          setLoading(true);
          const response = await apiClient.getAnimes({
            skip: 0,
            limit: 42, // 6行 x 7列 = 42个视频
            search: urlSearchTerm.trim(),
            sort_by: 'created_at',
            sort_order: 'desc'
          });
          setAnimes(response.animes);
          setTotalPages(Math.ceil(response.total / 42));
          setTotalResults(response.total);
          setCurrentPage(1);
        } catch (error) {
          console.error('Failed to fetch animes:', error);
          setAnimes([]);
          setTotalResults(0);
        } finally {
          setLoading(false);
        }
      };
      
      performSearch();
    }
  }, [searchParams]);

  // 分页相关函数
  const fetchAnimes = async (page: number = 1) => {
    if (!searchTerm.trim() || !hasSearched) return;

    try {
      setLoading(true);
      const response = await apiClient.getAnimes({
        skip: (page - 1) * 42,
        limit: 42,
        search: searchTerm.trim(),
        sort_by: 'created_at',
        sort_order: 'desc'
      });
      setAnimes(response.animes);
      setTotalPages(Math.ceil(response.total / 42));
      setTotalResults(response.total);
    } catch (error) {
      console.error('Failed to fetch animes:', error);
      setAnimes([]);
      setTotalResults(0);
    } finally {
      setLoading(false);
    }
  };

  // 处理页面变化
  useEffect(() => {
    if (currentPage > 1 && hasSearched) {
      fetchAnimes(currentPage);
    }
  }, [currentPage, hasSearched]);

  // 收藏状态变化处理
  const handleFavoriteChange = () => {
    // AnimeCard组件目前不使用这个callback，保留空实现
  };

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* Page Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">搜索结果</h1>
        {searchTerm && (
          <p className="text-muted-foreground">
            搜索 &ldquo;{searchTerm}&rdquo; 的结果
          </p>
        )}
      </div>

      {/* Results */}
      {loading ? (
        <div className="text-center py-12">
          <div className="text-lg">加载中...</div>
        </div>
      ) : animes.length > 0 ? (
        <>
          {/* Results Grid - 7 columns responsive */}
          <div className="grid grid-cols-3 md:grid-cols-7 gap-4">
            {animes.map((anime) => (
              <AnimeCard 
                key={anime.id} 
                anime={anime} 
                onFavoriteChange={handleFavoriteChange} 
              />
            ))}
          </div>

          {/* Advanced Pagination */}
          {totalPages > 1 && (
            <div className="mt-8">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
              
              {/* 页面信息 - 移动端缩小 */}
              <div className="flex justify-center items-center mt-4">
                <span className="text-xs md:text-sm text-muted-foreground">
                  第 {currentPage} 页，共 {totalPages} 页 • 共 {totalResults} 个结果
                </span>
              </div>
            </div>
          )}
        </>
      ) : hasSearched ? (
        <div className="text-center py-12">
          <div className="text-lg mb-2">未找到相关内容</div>
          <p className="text-muted-foreground">
            {searchTerm ? `没有找到包含 &ldquo;${searchTerm}&rdquo; 的动漫` : '没有找到相关动漫'}
          </p>
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-lg mb-2">请通过标签点击进入搜索</div>
          <p className="text-muted-foreground">
            点击动漫详情页面的标签来搜索相关内容
          </p>
        </div>
      )}
    </div>
  );
}