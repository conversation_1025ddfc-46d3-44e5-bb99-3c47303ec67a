#!/bin/bash

# 动漫网站生产环境一键部署脚本
# 包含文件清理和安全配置

set -e

echo "🚀 开始动漫网站生产环境部署..."

# 1. 清理开发文件
echo "📁 清理开发和调试文件..."

# 删除调试文件
find backend/claw -name "debug_*" -delete 2>/dev/null || true
find backend/manhua -name "debug_*" -delete 2>/dev/null || true

# 删除日志文件
find backend/claw -name "*.log" -delete 2>/dev/null || true

# 删除临时文件
rm -rf backend/claw/temp/ 2>/dev/null || true
rm -f backend/claw/temp_*.yml 2>/dev/null || true

# 删除压缩包
rm -f backend/app.zip 2>/dev/null || true
rm -f frontend/src.zip 2>/dev/null || true

# 删除测试数据
rm -f backend/comments_export.json 2>/dev/null || true

echo "✅ 清理完成"

# 2. 检查环境变量
if [ ! -f .env.production ]; then
    echo "❌ 错误: 未找到 .env.production 文件"
    echo "请创建 .env.production 文件并配置生产环境变量"
    exit 1
fi

echo "📋 检查环境变量..."
if grep -q "your-super-secret-jwt-key\|rootpassword\|password" .env.production; then
    echo "❌ 警告: 发现默认密码，请修改为安全密码"
    exit 1
fi

echo "✅ 环境变量检查通过"

# 3. 构建和启动服务
echo "🔨 构建Docker镜像..."
docker-compose -f docker-compose.prod.yml build --no-cache

echo "🚀 启动生产环境服务..."
docker-compose -f docker-compose.prod.yml up -d

# 4. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 5. 数据库初始化
echo "🗄️ 初始化数据库..."
docker-compose -f docker-compose.prod.yml exec backend python -m alembic upgrade head

# 6. 健康检查
echo "🏥 进行健康检查..."
if curl -f http://localhost:8000/api/v1 >/dev/null 2>&1; then
    echo "✅ 后端服务正常"
else
    echo "❌ 后端服务启动失败"
    exit 1
fi

if curl -f http://localhost:3000 >/dev/null 2>&1; then
    echo "✅ 前端服务正常"
else
    echo "❌ 前端服务启动失败"
    exit 1
fi

echo ""
echo "🎉 部署完成!"
echo "📱 前端访问: http://localhost:3000"
echo "🔌 后端API: http://localhost:8000"
echo ""
echo "📝 重要提醒:"
echo "1. 配置反向代理和SSL证书"
echo "2. 设置防火墙规则"
echo "3. 配置域名解析"
echo "4. 设置定期备份"
echo ""

# 显示服务状态
docker-compose -f docker-compose.prod.yml ps