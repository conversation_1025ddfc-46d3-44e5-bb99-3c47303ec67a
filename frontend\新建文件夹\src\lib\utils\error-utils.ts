import { ERROR_MESSAGES } from '../constants';

/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  VALIDATION = 'VALIDATION',
  SERVER = 'SERVER',
  UNKNOWN = 'UNKNOWN',
}

/**
 * 自定义错误类
 */
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly statusCode?: number;
  public readonly details?: any;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    statusCode?: number,
    details?: any
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
  }
}

/**
 * 根据HTTP状态码获取错误类型
 */
export function getErrorTypeFromStatus(status: number): ErrorType {
  switch (status) {
    case 400:
      return ErrorType.VALIDATION;
    case 401:
      return ErrorType.UNAUTHORIZED;
    case 403:
      return ErrorType.FORBIDDEN;
    case 404:
      return ErrorType.NOT_FOUND;
    case 500:
    case 502:
    case 503:
    case 504:
      return ErrorType.SERVER;
    default:
      return ErrorType.UNKNOWN;
  }
}

/**
 * 获取用户友好的错误消息
 */
export function getUserFriendlyErrorMessage(error: any): string {
  if (error instanceof AppError) {
    return error.message;
  }

  if (error?.response?.status) {
    const status = error.response.status;
    const errorType = getErrorTypeFromStatus(status);
    
    switch (errorType) {
      case ErrorType.UNAUTHORIZED:
        return ERROR_MESSAGES.UNAUTHORIZED;
      case ErrorType.FORBIDDEN:
        return ERROR_MESSAGES.FORBIDDEN;
      case ErrorType.NOT_FOUND:
        return ERROR_MESSAGES.NOT_FOUND;
      case ErrorType.VALIDATION:
        return error.response.data?.detail || ERROR_MESSAGES.VALIDATION_ERROR;
      case ErrorType.SERVER:
        return ERROR_MESSAGES.SERVER_ERROR;
      default:
        return ERROR_MESSAGES.UNKNOWN_ERROR;
    }
  }

  if (error?.message) {
    // 检查是否是网络错误
    if (error.message.includes('fetch') || error.message.includes('network')) {
      return ERROR_MESSAGES.NETWORK_ERROR;
    }
    return error.message;
  }

  return ERROR_MESSAGES.UNKNOWN_ERROR;
}

/**
 * 创建API错误
 */
export function createApiError(
  message: string,
  status?: number,
  details?: any
): AppError {
  const errorType = status ? getErrorTypeFromStatus(status) : ErrorType.UNKNOWN;
  return new AppError(message, errorType, status, details);
}

/**
 * 是否为网络错误
 */
export function isNetworkError(error: any): boolean {
  return (
    error instanceof TypeError ||
    error?.message?.includes('fetch') ||
    error?.message?.includes('network') ||
    error?.code === 'NETWORK_ERROR'
  );
}

/**
 * 是否为认证错误
 */
export function isAuthError(error: any): boolean {
  return (
    error?.response?.status === 401 ||
    error?.type === ErrorType.UNAUTHORIZED
  );
}

/**
 * 是否为权限错误
 */
export function isPermissionError(error: any): boolean {
  return (
    error?.response?.status === 403 ||
    error?.type === ErrorType.FORBIDDEN
  );
}

/**
 * 错误日志记录
 */
export function logError(error: any, context?: string): void {
  const errorInfo = {
    message: error?.message || 'Unknown error',
    stack: error?.stack,
    context,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href,
  };

  console.error('Application Error:', errorInfo);

  // 在生产环境中，这里可以发送错误到监控服务
  if (process.env.NODE_ENV === 'production') {
    // 发送到错误监控服务
    // sendErrorToMonitoring(errorInfo);
  }
}
