'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { apiClient, User } from '@/lib/api';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (username: string, password: string) => Promise<void>;
  register: (username: string, email: string, password: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [hasToken, setHasToken] = useState(false);

  useEffect(() => {
    const initAuth = async () => {
      const token = localStorage.getItem('access_token');
      setHasToken(!!token);

      if (token && apiClient.isAuthenticated()) {
        try {
          const userData = await apiClient.getCurrentUser();
          setUser(userData);
          // 存储用户信息用于临时显示
          localStorage.setItem('username', userData.username);
          localStorage.setItem('is_admin', userData.is_admin.toString());
        } catch (error) {
          console.error('Failed to fetch user data:', error);

          // 检查是否为认证错误
          const isAuthError = error instanceof Error && (
            error.message.includes('401') ||
            error.message.includes('unauthorized') ||
            error.message.includes('Could not validate credentials')
          );

          if (isAuthError) {
            console.log('Authentication error detected, clearing auth state');
            apiClient.logout();
            setUser(null);
            setHasToken(false);
            localStorage.removeItem('username');
            localStorage.removeItem('is_admin');
          } else {
            // 网络错误或服务器错误，保持登录状态但使用缓存的用户信息
            console.log('Network or server error, maintaining auth state with cached data');
            const cachedUsername = localStorage.getItem('username');
            const cachedIsAdmin = localStorage.getItem('is_admin') === 'true';

            if (cachedUsername) {
              // 使用缓存的用户信息创建临时用户对象
              const tempUser: User = {
                id: 0,
                username: cachedUsername,
                email: '',
                is_admin: cachedIsAdmin,
                is_active: true,
                avatar_url: undefined,
                created_at: '',
                updated_at: ''
              };
              setUser(tempUser);
            } else {
              // 没有缓存信息，清除认证状态
              apiClient.logout();
              setUser(null);
              setHasToken(false);
              localStorage.removeItem('username');
              localStorage.removeItem('is_admin');
            }
          }
        }
      } else {
        // 没有token，清除所有状态
        setUser(null);
        setHasToken(false);
      }

      setLoading(false);
    };

    initAuth();
  }, []);

  const login = async (username: string, password: string) => {
    setLoading(true);
    try {
      const loginResponse = await apiClient.login({ username, password });
      setUser(loginResponse.user); // Use user data from login response
      setHasToken(true);
      // 存储用户信息用于临时显示
      localStorage.setItem('username', loginResponse.user.username);
      localStorage.setItem('is_admin', loginResponse.user.is_admin.toString());
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (username: string, email: string, password: string) => {
    setLoading(true);
    try {
      await apiClient.register({ username, email, password });
      // Auto-login after registration
      await login(username, password);
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    apiClient.logout();
    setUser(null);
    setHasToken(false);
    // 清理存储的用户信息
    localStorage.removeItem('username');
    localStorage.removeItem('is_admin');
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    register,
    logout,
    isAuthenticated: !!user || hasToken,
    isAdmin: user?.is_admin ?? false,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};