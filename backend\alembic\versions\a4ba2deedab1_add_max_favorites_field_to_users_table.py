"""add max_favorites field to users table

Revision ID: a4ba2deedab1
Revises: f1a2b3c4d5e6
Create Date: 2025-09-06 15:45:16.402476

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'a4ba2deedab1'
down_revision: Union[str, None] = 'f1a2b3c4d5e6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('comments', 'content_type',
               existing_type=mysql.ENUM('anime', 'manga'),
               type_=sa.Enum('anime', 'manga', name='contenttype', native_enum=False),
               nullable=False,
               comment=None,
               existing_comment='内容类型',
               existing_server_default=sa.text("'anime'"))
    op.alter_column('comments', 'anime_id',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=True)
    op.alter_column('comments', 'manga_id',
               existing_type=mysql.BIGINT(display_width=20),
               comment=None,
               existing_comment='漫画ID（当content_type为manga时使用）',
               existing_nullable=True)
    op.drop_constraint(op.f('comments_ibfk_6'), 'comments', type_='foreignkey')
    op.create_foreign_key(None, 'comments', 'mangas', ['manga_id'], ['id'])
    op.alter_column('favorites', 'content_type',
               existing_type=mysql.ENUM('anime', 'manga'),
               type_=sa.Enum('anime', 'manga', name='contenttype', native_enum=False),
               nullable=False,
               comment=None,
               existing_comment='内容类型',
               existing_server_default=sa.text("'anime'"))
    op.alter_column('favorites', 'manga_id',
               existing_type=mysql.BIGINT(display_width=20),
               comment=None,
               existing_comment='漫画ID（当content_type为manga时使用）',
               existing_nullable=True)
    op.create_unique_constraint('uq_user_anime_favorite', 'favorites', ['user_id', 'content_type', 'anime_id'])
    op.create_unique_constraint('uq_user_manga_favorite', 'favorites', ['user_id', 'content_type', 'manga_id'])
    op.drop_constraint(op.f('favorites_ibfk_3'), 'favorites', type_='foreignkey')
    op.create_foreign_key(None, 'favorites', 'mangas', ['manga_id'], ['id'])
    op.alter_column('manga_chapters', 'title',
               existing_type=mysql.VARCHAR(length=300),
               nullable=True,
               comment=None,
               existing_comment='章节标题')
    op.alter_column('manga_chapters', 'chapter_number',
               existing_type=mysql.DECIMAL(precision=8, scale=2),
               comment=None,
               existing_comment='章节号，支持小数如1.5',
               existing_nullable=False)
    op.alter_column('manga_chapters', 'volume_number',
               existing_type=mysql.INTEGER(display_width=11),
               comment=None,
               existing_comment='卷号',
               existing_nullable=True)
    op.alter_column('manga_chapters', 'page_count',
               existing_type=mysql.INTEGER(display_width=11),
               comment=None,
               existing_comment='页面数量',
               existing_nullable=True,
               existing_server_default=sa.text('0'))
    op.alter_column('manga_chapters', 'is_free',
               existing_type=mysql.TINYINT(display_width=1),
               comment=None,
               existing_comment='是否免费',
               existing_nullable=True,
               existing_server_default=sa.text('1'))
    op.alter_column('manga_chapters', 'price',
               existing_type=mysql.DECIMAL(precision=8, scale=2),
               comment=None,
               existing_comment='价格',
               existing_nullable=True,
               existing_server_default=sa.text('0.00'))
    op.alter_column('manga_chapters', 'status',
               existing_type=mysql.ENUM('draft', 'published'),
               type_=sa.Enum('draft', 'published', name='chapterstatus', native_enum=False),
               comment=None,
               existing_comment='发布状态',
               existing_nullable=True,
               existing_server_default=sa.text("'published'"))
    op.alter_column('manga_chapters', 'release_date',
               existing_type=mysql.DATETIME(),
               comment=None,
               existing_comment='发布时间',
               existing_nullable=True)
    op.alter_column('manga_chapters', 'created_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('current_timestamp()'))
    op.alter_column('manga_chapters', 'updated_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('current_timestamp() ON UPDATE current_timestamp()'))
    op.drop_index(op.f('uk_manga_chapter'), table_name='manga_chapters')
    op.create_index(op.f('ix_manga_chapters_id'), 'manga_chapters', ['id'], unique=False)
    op.create_index(op.f('ix_manga_chapters_manga_id'), 'manga_chapters', ['manga_id'], unique=False)
    op.drop_constraint(op.f('manga_chapters_ibfk_1'), 'manga_chapters', type_='foreignkey')
    op.create_foreign_key(None, 'manga_chapters', 'mangas', ['manga_id'], ['id'])
    op.alter_column('manga_pages', 'page_number',
               existing_type=mysql.INTEGER(display_width=11),
               comment=None,
               existing_comment='页码',
               existing_nullable=False)
    op.alter_column('manga_pages', 'image_url',
               existing_type=mysql.VARCHAR(length=1000),
               comment=None,
               existing_comment='图片URL',
               existing_nullable=False)
    op.alter_column('manga_pages', 'width',
               existing_type=mysql.INTEGER(display_width=11),
               comment=None,
               existing_comment='图片宽度',
               existing_nullable=True)
    op.alter_column('manga_pages', 'height',
               existing_type=mysql.INTEGER(display_width=11),
               comment=None,
               existing_comment='图片高度',
               existing_nullable=True)
    op.alter_column('manga_pages', 'file_size',
               existing_type=mysql.INTEGER(display_width=11),
               comment=None,
               existing_comment='文件大小（字节）',
               existing_nullable=True)
    op.alter_column('manga_pages', 'created_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('current_timestamp()'))
    op.drop_index(op.f('uk_chapter_page'), table_name='manga_pages')
    op.create_index(op.f('ix_manga_pages_chapter_id'), 'manga_pages', ['chapter_id'], unique=False)
    op.create_index(op.f('ix_manga_pages_id'), 'manga_pages', ['id'], unique=False)
    op.create_unique_constraint('uq_chapter_page', 'manga_pages', ['chapter_id', 'page_number'])
    op.drop_constraint(op.f('manga_pages_ibfk_1'), 'manga_pages', type_='foreignkey')
    op.create_foreign_key(None, 'manga_pages', 'manga_chapters', ['chapter_id'], ['id'])
    op.alter_column('manga_reading_progress', 'user_id',
               existing_type=mysql.INTEGER(display_width=11),
               comment=None,
               existing_comment='用户ID，匹配users表',
               existing_nullable=False)
    op.alter_column('manga_reading_progress', 'manga_id',
               existing_type=mysql.BIGINT(display_width=20),
               comment=None,
               existing_comment='漫画ID',
               existing_nullable=False)
    op.alter_column('manga_reading_progress', 'chapter_id',
               existing_type=mysql.BIGINT(display_width=20),
               comment=None,
               existing_comment='当前阅读章节',
               existing_nullable=False)
    op.alter_column('manga_reading_progress', 'last_read_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('current_timestamp() ON UPDATE current_timestamp()'))
    op.alter_column('manga_reading_progress', 'created_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('current_timestamp()'))
    op.drop_index(op.f('idx_chapter_id'), table_name='manga_reading_progress')
    op.drop_index(op.f('idx_manga_id'), table_name='manga_reading_progress')
    op.drop_index(op.f('idx_user_id'), table_name='manga_reading_progress')
    op.drop_index(op.f('uk_user_manga'), table_name='manga_reading_progress')
    op.create_index(op.f('ix_manga_reading_progress_id'), 'manga_reading_progress', ['id'], unique=False)
    op.create_index(op.f('ix_manga_reading_progress_manga_id'), 'manga_reading_progress', ['manga_id'], unique=False)
    op.create_index(op.f('ix_manga_reading_progress_user_id'), 'manga_reading_progress', ['user_id'], unique=False)
    op.create_unique_constraint('uq_user_manga_progress', 'manga_reading_progress', ['user_id', 'manga_id'])
    op.drop_constraint(op.f('manga_reading_progress_ibfk_1'), 'manga_reading_progress', type_='foreignkey')
    op.drop_constraint(op.f('manga_reading_progress_ibfk_3'), 'manga_reading_progress', type_='foreignkey')
    op.drop_constraint(op.f('manga_reading_progress_ibfk_2'), 'manga_reading_progress', type_='foreignkey')
    op.create_foreign_key(None, 'manga_reading_progress', 'users', ['user_id'], ['id'])
    op.create_foreign_key(None, 'manga_reading_progress', 'mangas', ['manga_id'], ['id'])
    op.create_foreign_key(None, 'manga_reading_progress', 'manga_chapters', ['chapter_id'], ['id'])
    op.add_column('manga_tags', sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True))
    op.alter_column('manga_tags', 'id',
               existing_type=mysql.BIGINT(display_width=20),
               type_=sa.Integer(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('manga_tags', 'tag_id',
               existing_type=mysql.INTEGER(display_width=11),
               comment=None,
               existing_comment='复用tags表',
               existing_nullable=False)
    op.alter_column('manga_tags', 'created_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('current_timestamp()'))
    op.drop_index(op.f('uk_manga_tag'), table_name='manga_tags')
    op.create_index(op.f('ix_manga_tags_id'), 'manga_tags', ['id'], unique=False)
    op.create_unique_constraint('uq_manga_tag', 'manga_tags', ['manga_id', 'tag_id'])
    op.drop_constraint(op.f('manga_tags_ibfk_2'), 'manga_tags', type_='foreignkey')
    op.drop_constraint(op.f('manga_tags_ibfk_1'), 'manga_tags', type_='foreignkey')
    op.create_foreign_key(None, 'manga_tags', 'mangas', ['manga_id'], ['id'])
    op.create_foreign_key(None, 'manga_tags', 'tags', ['tag_id'], ['id'])
    op.alter_column('mangas', 'title',
               existing_type=mysql.VARCHAR(length=500),
               comment=None,
               existing_comment='漫画标题',
               existing_nullable=False)
    op.alter_column('mangas', 'title_original',
               existing_type=mysql.VARCHAR(length=500),
               comment=None,
               existing_comment='原版标题',
               existing_nullable=True)
    op.alter_column('mangas', 'category_id',
               existing_type=mysql.INTEGER(display_width=11),
               comment=None,
               existing_comment='复用categories表，用于漫画分类',
               existing_nullable=True)
    op.alter_column('mangas', 'region_code',
               existing_type=mysql.VARCHAR(length=10),
               nullable=True,
               comment=None,
               existing_comment='地区代码：jp/kr/cn')
    op.alter_column('mangas', 'manga_type',
               existing_type=mysql.ENUM('serial', 'tankoubon', 'doujinshi'),
               type_=sa.Enum('serial', 'tankoubon', 'doujinshi', name='mangatype', native_enum=False),
               nullable=True,
               comment=None,
               existing_comment='漫画类型：系列/单行本/同人志')
    op.alter_column('mangas', 'author',
               existing_type=mysql.VARCHAR(length=200),
               comment=None,
               existing_comment='作者',
               existing_nullable=True)
    op.alter_column('mangas', 'artist',
               existing_type=mysql.VARCHAR(length=200),
               comment=None,
               existing_comment='画师',
               existing_nullable=True)
    op.alter_column('mangas', 'publisher',
               existing_type=mysql.VARCHAR(length=200),
               comment=None,
               existing_comment='出版社',
               existing_nullable=True)
    op.alter_column('mangas', 'description',
               existing_type=mysql.TEXT(),
               comment=None,
               existing_comment='简介',
               existing_nullable=True)
    op.alter_column('mangas', 'cover',
               existing_type=mysql.VARCHAR(length=1000),
               comment=None,
               existing_comment='封面图片URL',
               existing_nullable=True)
    op.alter_column('mangas', 'banner',
               existing_type=mysql.VARCHAR(length=1000),
               comment=None,
               existing_comment='横幅图片URL',
               existing_nullable=True)
    op.alter_column('mangas', 'view_count',
               existing_type=mysql.BIGINT(display_width=20),
               comment=None,
               existing_comment='浏览次数',
               existing_nullable=True,
               existing_server_default=sa.text('0'))
    op.alter_column('mangas', 'favorite_count',
               existing_type=mysql.INTEGER(display_width=11),
               comment=None,
               existing_comment='收藏次数',
               existing_nullable=True,
               existing_server_default=sa.text('0'))
    op.alter_column('mangas', 'chapter_count',
               existing_type=mysql.INTEGER(display_width=11),
               comment=None,
               existing_comment='章节总数',
               existing_nullable=True,
               existing_server_default=sa.text('0'))
    op.alter_column('mangas', 'status',
               existing_type=mysql.ENUM('ongoing', 'completed', 'hiatus', 'cancelled'),
               type_=sa.Enum('ongoing', 'completed', 'hiatus', 'cancelled', name='mangastatus', native_enum=False),
               comment=None,
               existing_comment='连载状态',
               existing_nullable=True,
               existing_server_default=sa.text("'ongoing'"))
    op.alter_column('mangas', 'is_active',
               existing_type=mysql.TINYINT(display_width=1),
               comment=None,
               existing_comment='是否启用',
               existing_nullable=True,
               existing_server_default=sa.text('1'))
    op.alter_column('mangas', 'release_date',
               existing_type=sa.DATE(),
               comment=None,
               existing_comment='发布日期',
               existing_nullable=True)
    op.alter_column('mangas', 'created_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('current_timestamp()'))
    op.alter_column('mangas', 'updated_at',
               existing_type=mysql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('current_timestamp() ON UPDATE current_timestamp()'))
    op.create_index(op.f('ix_mangas_id'), 'mangas', ['id'], unique=False)
    op.create_index(op.f('ix_mangas_title'), 'mangas', ['title'], unique=False)
    op.drop_column('tags', 'applicable_to')
    op.add_column('users', sa.Column('max_favorites', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'max_favorites')
    op.add_column('tags', sa.Column('applicable_to', mysql.SET('anime', 'manga'), server_default=sa.text("'anime'"), nullable=True, comment='适用内容类型'))
    op.drop_index(op.f('ix_mangas_title'), table_name='mangas')
    op.drop_index(op.f('ix_mangas_id'), table_name='mangas')
    op.alter_column('mangas', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=mysql.TIMESTAMP(),
               existing_nullable=True,
               existing_server_default=sa.text('current_timestamp() ON UPDATE current_timestamp()'))
    op.alter_column('mangas', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=mysql.TIMESTAMP(),
               existing_nullable=True,
               existing_server_default=sa.text('current_timestamp()'))
    op.alter_column('mangas', 'release_date',
               existing_type=sa.DATE(),
               comment='发布日期',
               existing_nullable=True)
    op.alter_column('mangas', 'is_active',
               existing_type=mysql.TINYINT(display_width=1),
               comment='是否启用',
               existing_nullable=True,
               existing_server_default=sa.text('1'))
    op.alter_column('mangas', 'status',
               existing_type=sa.Enum('ongoing', 'completed', 'hiatus', 'cancelled', name='mangastatus', native_enum=False),
               type_=mysql.ENUM('ongoing', 'completed', 'hiatus', 'cancelled'),
               comment='连载状态',
               existing_nullable=True,
               existing_server_default=sa.text("'ongoing'"))
    op.alter_column('mangas', 'chapter_count',
               existing_type=mysql.INTEGER(display_width=11),
               comment='章节总数',
               existing_nullable=True,
               existing_server_default=sa.text('0'))
    op.alter_column('mangas', 'favorite_count',
               existing_type=mysql.INTEGER(display_width=11),
               comment='收藏次数',
               existing_nullable=True,
               existing_server_default=sa.text('0'))
    op.alter_column('mangas', 'view_count',
               existing_type=mysql.BIGINT(display_width=20),
               comment='浏览次数',
               existing_nullable=True,
               existing_server_default=sa.text('0'))
    op.alter_column('mangas', 'banner',
               existing_type=mysql.VARCHAR(length=1000),
               comment='横幅图片URL',
               existing_nullable=True)
    op.alter_column('mangas', 'cover',
               existing_type=mysql.VARCHAR(length=1000),
               comment='封面图片URL',
               existing_nullable=True)
    op.alter_column('mangas', 'description',
               existing_type=mysql.TEXT(),
               comment='简介',
               existing_nullable=True)
    op.alter_column('mangas', 'publisher',
               existing_type=mysql.VARCHAR(length=200),
               comment='出版社',
               existing_nullable=True)
    op.alter_column('mangas', 'artist',
               existing_type=mysql.VARCHAR(length=200),
               comment='画师',
               existing_nullable=True)
    op.alter_column('mangas', 'author',
               existing_type=mysql.VARCHAR(length=200),
               comment='作者',
               existing_nullable=True)
    op.alter_column('mangas', 'manga_type',
               existing_type=sa.Enum('serial', 'tankoubon', 'doujinshi', name='mangatype', native_enum=False),
               type_=mysql.ENUM('serial', 'tankoubon', 'doujinshi'),
               nullable=False,
               comment='漫画类型：系列/单行本/同人志')
    op.alter_column('mangas', 'region_code',
               existing_type=mysql.VARCHAR(length=10),
               nullable=False,
               comment='地区代码：jp/kr/cn')
    op.alter_column('mangas', 'category_id',
               existing_type=mysql.INTEGER(display_width=11),
               comment='复用categories表，用于漫画分类',
               existing_nullable=True)
    op.alter_column('mangas', 'title_original',
               existing_type=mysql.VARCHAR(length=500),
               comment='原版标题',
               existing_nullable=True)
    op.alter_column('mangas', 'title',
               existing_type=mysql.VARCHAR(length=500),
               comment='漫画标题',
               existing_nullable=False)
    op.drop_constraint(None, 'manga_tags', type_='foreignkey')
    op.drop_constraint(None, 'manga_tags', type_='foreignkey')
    op.create_foreign_key(op.f('manga_tags_ibfk_1'), 'manga_tags', 'mangas', ['manga_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key(op.f('manga_tags_ibfk_2'), 'manga_tags', 'tags', ['tag_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('uq_manga_tag', 'manga_tags', type_='unique')
    op.drop_index(op.f('ix_manga_tags_id'), table_name='manga_tags')
    op.create_index(op.f('uk_manga_tag'), 'manga_tags', ['manga_id', 'tag_id'], unique=True)
    op.alter_column('manga_tags', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=mysql.TIMESTAMP(),
               existing_nullable=True,
               existing_server_default=sa.text('current_timestamp()'))
    op.alter_column('manga_tags', 'tag_id',
               existing_type=mysql.INTEGER(display_width=11),
               comment='复用tags表',
               existing_nullable=False)
    op.alter_column('manga_tags', 'id',
               existing_type=sa.Integer(),
               type_=mysql.BIGINT(display_width=20),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('manga_tags', 'updated_at')
    op.drop_constraint(None, 'manga_reading_progress', type_='foreignkey')
    op.drop_constraint(None, 'manga_reading_progress', type_='foreignkey')
    op.drop_constraint(None, 'manga_reading_progress', type_='foreignkey')
    op.create_foreign_key(op.f('manga_reading_progress_ibfk_2'), 'manga_reading_progress', 'mangas', ['manga_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key(op.f('manga_reading_progress_ibfk_3'), 'manga_reading_progress', 'manga_chapters', ['chapter_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key(op.f('manga_reading_progress_ibfk_1'), 'manga_reading_progress', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('uq_user_manga_progress', 'manga_reading_progress', type_='unique')
    op.drop_index(op.f('ix_manga_reading_progress_user_id'), table_name='manga_reading_progress')
    op.drop_index(op.f('ix_manga_reading_progress_manga_id'), table_name='manga_reading_progress')
    op.drop_index(op.f('ix_manga_reading_progress_id'), table_name='manga_reading_progress')
    op.create_index(op.f('uk_user_manga'), 'manga_reading_progress', ['user_id', 'manga_id'], unique=True)
    op.create_index(op.f('idx_user_id'), 'manga_reading_progress', ['user_id'], unique=False)
    op.create_index(op.f('idx_manga_id'), 'manga_reading_progress', ['manga_id'], unique=False)
    op.create_index(op.f('idx_chapter_id'), 'manga_reading_progress', ['chapter_id'], unique=False)
    op.alter_column('manga_reading_progress', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=mysql.TIMESTAMP(),
               existing_nullable=True,
               existing_server_default=sa.text('current_timestamp()'))
    op.alter_column('manga_reading_progress', 'last_read_at',
               existing_type=sa.DateTime(timezone=True),
               type_=mysql.TIMESTAMP(),
               existing_nullable=True,
               existing_server_default=sa.text('current_timestamp() ON UPDATE current_timestamp()'))
    op.alter_column('manga_reading_progress', 'chapter_id',
               existing_type=mysql.BIGINT(display_width=20),
               comment='当前阅读章节',
               existing_nullable=False)
    op.alter_column('manga_reading_progress', 'manga_id',
               existing_type=mysql.BIGINT(display_width=20),
               comment='漫画ID',
               existing_nullable=False)
    op.alter_column('manga_reading_progress', 'user_id',
               existing_type=mysql.INTEGER(display_width=11),
               comment='用户ID，匹配users表',
               existing_nullable=False)
    op.drop_constraint(None, 'manga_pages', type_='foreignkey')
    op.create_foreign_key(op.f('manga_pages_ibfk_1'), 'manga_pages', 'manga_chapters', ['chapter_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('uq_chapter_page', 'manga_pages', type_='unique')
    op.drop_index(op.f('ix_manga_pages_id'), table_name='manga_pages')
    op.drop_index(op.f('ix_manga_pages_chapter_id'), table_name='manga_pages')
    op.create_index(op.f('uk_chapter_page'), 'manga_pages', ['chapter_id', 'page_number'], unique=True)
    op.alter_column('manga_pages', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=mysql.TIMESTAMP(),
               existing_nullable=True,
               existing_server_default=sa.text('current_timestamp()'))
    op.alter_column('manga_pages', 'file_size',
               existing_type=mysql.INTEGER(display_width=11),
               comment='文件大小（字节）',
               existing_nullable=True)
    op.alter_column('manga_pages', 'height',
               existing_type=mysql.INTEGER(display_width=11),
               comment='图片高度',
               existing_nullable=True)
    op.alter_column('manga_pages', 'width',
               existing_type=mysql.INTEGER(display_width=11),
               comment='图片宽度',
               existing_nullable=True)
    op.alter_column('manga_pages', 'image_url',
               existing_type=mysql.VARCHAR(length=1000),
               comment='图片URL',
               existing_nullable=False)
    op.alter_column('manga_pages', 'page_number',
               existing_type=mysql.INTEGER(display_width=11),
               comment='页码',
               existing_nullable=False)
    op.drop_constraint(None, 'manga_chapters', type_='foreignkey')
    op.create_foreign_key(op.f('manga_chapters_ibfk_1'), 'manga_chapters', 'mangas', ['manga_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_manga_chapters_manga_id'), table_name='manga_chapters')
    op.drop_index(op.f('ix_manga_chapters_id'), table_name='manga_chapters')
    op.create_index(op.f('uk_manga_chapter'), 'manga_chapters', ['manga_id', 'chapter_number'], unique=True)
    op.alter_column('manga_chapters', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=mysql.TIMESTAMP(),
               existing_nullable=True,
               existing_server_default=sa.text('current_timestamp() ON UPDATE current_timestamp()'))
    op.alter_column('manga_chapters', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=mysql.TIMESTAMP(),
               existing_nullable=True,
               existing_server_default=sa.text('current_timestamp()'))
    op.alter_column('manga_chapters', 'release_date',
               existing_type=mysql.DATETIME(),
               comment='发布时间',
               existing_nullable=True)
    op.alter_column('manga_chapters', 'status',
               existing_type=sa.Enum('draft', 'published', name='chapterstatus', native_enum=False),
               type_=mysql.ENUM('draft', 'published'),
               comment='发布状态',
               existing_nullable=True,
               existing_server_default=sa.text("'published'"))
    op.alter_column('manga_chapters', 'price',
               existing_type=mysql.DECIMAL(precision=8, scale=2),
               comment='价格',
               existing_nullable=True,
               existing_server_default=sa.text('0.00'))
    op.alter_column('manga_chapters', 'is_free',
               existing_type=mysql.TINYINT(display_width=1),
               comment='是否免费',
               existing_nullable=True,
               existing_server_default=sa.text('1'))
    op.alter_column('manga_chapters', 'page_count',
               existing_type=mysql.INTEGER(display_width=11),
               comment='页面数量',
               existing_nullable=True,
               existing_server_default=sa.text('0'))
    op.alter_column('manga_chapters', 'volume_number',
               existing_type=mysql.INTEGER(display_width=11),
               comment='卷号',
               existing_nullable=True)
    op.alter_column('manga_chapters', 'chapter_number',
               existing_type=mysql.DECIMAL(precision=8, scale=2),
               comment='章节号，支持小数如1.5',
               existing_nullable=False)
    op.alter_column('manga_chapters', 'title',
               existing_type=mysql.VARCHAR(length=300),
               nullable=False,
               comment='章节标题')
    op.drop_constraint(None, 'favorites', type_='foreignkey')
    op.create_foreign_key(op.f('favorites_ibfk_3'), 'favorites', 'mangas', ['manga_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('uq_user_manga_favorite', 'favorites', type_='unique')
    op.drop_constraint('uq_user_anime_favorite', 'favorites', type_='unique')
    op.alter_column('favorites', 'manga_id',
               existing_type=mysql.BIGINT(display_width=20),
               comment='漫画ID（当content_type为manga时使用）',
               existing_nullable=True)
    op.alter_column('favorites', 'content_type',
               existing_type=sa.Enum('anime', 'manga', name='contenttype', native_enum=False),
               type_=mysql.ENUM('anime', 'manga'),
               nullable=True,
               comment='内容类型',
               existing_server_default=sa.text("'anime'"))
    op.drop_constraint(None, 'comments', type_='foreignkey')
    op.create_foreign_key(op.f('comments_ibfk_6'), 'comments', 'mangas', ['manga_id'], ['id'], ondelete='CASCADE')
    op.alter_column('comments', 'manga_id',
               existing_type=mysql.BIGINT(display_width=20),
               comment='漫画ID（当content_type为manga时使用）',
               existing_nullable=True)
    op.alter_column('comments', 'anime_id',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=False)
    op.alter_column('comments', 'content_type',
               existing_type=sa.Enum('anime', 'manga', name='contenttype', native_enum=False),
               type_=mysql.ENUM('anime', 'manga'),
               nullable=True,
               comment='内容类型',
               existing_server_default=sa.text("'anime'"))
    # ### end Alembic commands ###
