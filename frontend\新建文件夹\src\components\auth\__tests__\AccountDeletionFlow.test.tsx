/**
 * @jest-environment jsdom
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { apiClient } from '@/lib/api'
import AccountDeletionFlow from '../AccountDeletionFlow'

// Mock the API client
jest.mock('@/lib/api')
const mockApiClient = apiClient as jest.Mocked<typeof apiClient>

// Mock router
const mockPush = jest.fn()
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// Mock toast notifications
const mockSetToastMessage = jest.fn()

// Mock user data
const mockUser = {
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  is_active: true,
  is_admin: false,
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z',
}

describe('AccountDeletionFlow', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockApiClient.deleteAccount.mockClear()
    mockPush.mockClear()
    localStorageMock.removeItem.mockClear()
  })

  const defaultProps = {
    user: mockUser,
    deletionForm: {
      username_confirmation: '',
    },
    setDeletionForm: jest.fn(),
    deletionStep: 0,
    setDeletionStep: jest.fn(),
    deletionLoading: false,
    setToastMessage: mockSetToastMessage,
  }

  describe('Step 0: Initial Confirmation', () => {
    it('renders initial confirmation step', () => {
      render(<AccountDeletionFlow {...defaultProps} />)

      expect(screen.getByText('删除账户')).toBeInTheDocument()
      expect(screen.getByText('危险操作 - 此操作不可逆')).toBeInTheDocument()
      expect(screen.getByText('删除账户将永久删除您的：')).toBeInTheDocument()
      expect(screen.getByText('个人信息和头像')).toBeInTheDocument()
      expect(screen.getByText('所有收藏记录')).toBeInTheDocument()
      expect(screen.getByText('阅读历史和进度')).toBeInTheDocument()
      expect(screen.getByText('评论和互动记录')).toBeInTheDocument()
      expect(screen.getByText('所有相关数据')).toBeInTheDocument()
    })

    it('shows username confirmation input', () => {
      render(<AccountDeletionFlow {...defaultProps} />)

      const input = screen.getByPlaceholderText('请输入 "testuser" 以确认删除')
      expect(input).toBeInTheDocument()
      expect(input).toHaveAttribute('type', 'text')
    })

    it('disables continue button when username is not entered', () => {
      render(<AccountDeletionFlow {...defaultProps} />)

      const continueButton = screen.getByRole('button', { name: /继续删除账户/ })
      expect(continueButton).toBeDisabled()
    })

    it('enables continue button when correct username is entered', () => {
      const props = {
        ...defaultProps,
        deletionForm: {
          username_confirmation: 'testuser',
        },
      }

      render(<AccountDeletionFlow {...props} />)

      const continueButton = screen.getByRole('button', { name: /继续删除账户/ })
      expect(continueButton).toBeEnabled()
    })

    it('handles username input change', async () => {
      const mockSetDeletionForm = jest.fn()
      const props = {
        ...defaultProps,
        setDeletionForm: mockSetDeletionForm,
      }

      render(<AccountDeletionFlow {...props} />)

      const input = screen.getByPlaceholderText('请输入 "testuser" 以确认删除')
      await userEvent.type(input, 'testuser')

      expect(mockSetDeletionForm).toHaveBeenCalledWith({
        username_confirmation: 'testuser',
      })
    })

    it('shows error for empty username confirmation', async () => {
      const mockSetDeletionStep = jest.fn()
      const props = {
        ...defaultProps,
        setDeletionStep: mockSetDeletionStep,
      }

      render(<AccountDeletionFlow {...props} />)

      const continueButton = screen.getByRole('button', { name: /继续删除账户/ })
      await userEvent.click(continueButton)

      expect(mockSetToastMessage).toHaveBeenCalledWith({
        message: '请输入用户名确认',
        type: 'error',
      })
      expect(mockSetDeletionStep).not.toHaveBeenCalled()
    })

    it('shows error for incorrect username confirmation', async () => {
      const mockSetDeletionStep = jest.fn()
      const props = {
        ...defaultProps,
        deletionForm: {
          username_confirmation: 'wronguser',
        },
        setDeletionStep: mockSetDeletionStep,
      }

      render(<AccountDeletionFlow {...props} />)

      const continueButton = screen.getByRole('button', { name: /继续删除账户/ })
      await userEvent.click(continueButton)

      expect(mockSetToastMessage).toHaveBeenCalledWith({
        message: '用户名确认不匹配',
        type: 'error',
      })
      expect(mockSetDeletionStep).not.toHaveBeenCalled()
    })

    it('proceeds to step 1 with correct username', async () => {
      const mockSetDeletionStep = jest.fn()
      const props = {
        ...defaultProps,
        deletionForm: {
          username_confirmation: 'testuser',
        },
        setDeletionStep: mockSetDeletionStep,
      }

      render(<AccountDeletionFlow {...props} />)

      const continueButton = screen.getByRole('button', { name: /继续删除账户/ })
      await userEvent.click(continueButton)

      expect(mockSetDeletionStep).toHaveBeenCalledWith(1)
    })
  })

  describe('Step 1: Second Confirmation', () => {
    const step1Props = {
      ...defaultProps,
      deletionStep: 1,
    }

    it('renders second confirmation step', () => {
      render(<AccountDeletionFlow {...step1Props} />)

      expect(screen.getByText('第二次确认')).toBeInTheDocument()
      expect(screen.getByText('您确定要删除账户')).toBeInTheDocument()
      expect(screen.getByText('testuser')).toBeInTheDocument()
      expect(screen.getByText('这将删除所有相关数据，且无法恢复。')).toBeInTheDocument()
    })

    it('shows cancel and continue buttons', () => {
      render(<AccountDeletionFlow {...step1Props} />)

      expect(screen.getByRole('button', { name: '取消' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /我确定要删除/ })).toBeInTheDocument()
    })

    it('handles cancel button click', async () => {
      const mockSetDeletionStep = jest.fn()
      const mockSetDeletionForm = jest.fn()
      const props = {
        ...step1Props,
        setDeletionStep: mockSetDeletionStep,
        setDeletionForm: mockSetDeletionForm,
      }

      render(<AccountDeletionFlow {...props} />)

      const cancelButton = screen.getByRole('button', { name: '取消' })
      await userEvent.click(cancelButton)

      expect(mockSetDeletionStep).toHaveBeenCalledWith(0)
      expect(mockSetDeletionForm).toHaveBeenCalledWith({ username_confirmation: '' })
    })

    it('proceeds to step 2 when confirmed', async () => {
      const mockSetDeletionStep = jest.fn()
      const props = {
        ...step1Props,
        setDeletionStep: mockSetDeletionStep,
      }

      render(<AccountDeletionFlow {...props} />)

      const confirmButton = screen.getByRole('button', { name: /我确定要删除/ })
      await userEvent.click(confirmButton)

      expect(mockSetDeletionStep).toHaveBeenCalledWith(2)
    })
  })

  describe('Step 2: Final Confirmation', () => {
    const step2Props = {
      ...defaultProps,
      deletionStep: 2,
      deletionForm: {
        username_confirmation: 'testuser',
      },
    }

    it('renders final confirmation step', () => {
      render(<AccountDeletionFlow {...step2Props} />)

      expect(screen.getByText('最终确认')).toBeInTheDocument()
      expect(screen.getByText('这是最后一次确认。')).toBeInTheDocument()
      expect(screen.getByText('点击下方按钮将立即删除您的账户')).toBeInTheDocument()
      expect(screen.getByText('无法撤销')).toBeInTheDocument()
    })

    it('shows cancel and delete buttons', () => {
      render(<AccountDeletionFlow {...step2Props} />)

      expect(screen.getByRole('button', { name: '取消删除' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /永久删除账户/ })).toBeInTheDocument()
    })

    it('handles cancel button click', async () => {
      const mockSetDeletionStep = jest.fn()
      const mockSetDeletionForm = jest.fn()
      const props = {
        ...step2Props,
        setDeletionStep: mockSetDeletionStep,
        setDeletionForm: mockSetDeletionForm,
      }

      render(<AccountDeletionFlow {...props} />)

      const cancelButton = screen.getByRole('button', { name: '取消删除' })
      await userEvent.click(cancelButton)

      expect(mockSetDeletionStep).toHaveBeenCalledWith(0)
      expect(mockSetDeletionForm).toHaveBeenCalledWith({ username_confirmation: '' })
    })

    it('executes account deletion successfully', async () => {
      mockApiClient.deleteAccount.mockResolvedValue({ message: '账户删除成功' })

      render(<AccountDeletionFlow {...step2Props} />)

      const deleteButton = screen.getByRole('button', { name: /永久删除账户/ })
      await userEvent.click(deleteButton)

      await waitFor(() => {
        expect(mockApiClient.deleteAccount).toHaveBeenCalledWith('testuser')
      })

      expect(mockSetToastMessage).toHaveBeenCalledWith({
        message: '账户删除成功，正在跳转...',
        type: 'success',
      })

      // Wait for the setTimeout to complete
      await waitFor(
        () => {
          expect(localStorageMock.removeItem).toHaveBeenCalledWith('access_token')
          expect(mockPush).toHaveBeenCalledWith('/auth')
        },
        { timeout: 3000 }
      )
    })

    it('handles account deletion API error', async () => {
      const errorMessage = '删除失败，请重试'
      mockApiClient.deleteAccount.mockRejectedValue(new Error(errorMessage))

      const mockSetDeletionStep = jest.fn()
      const mockSetDeletionForm = jest.fn()
      const props = {
        ...step2Props,
        setDeletionStep: mockSetDeletionStep,
        setDeletionForm: mockSetDeletionForm,
      }

      render(<AccountDeletionFlow {...props} />)

      const deleteButton = screen.getByRole('button', { name: /永久删除账户/ })
      await userEvent.click(deleteButton)

      await waitFor(() => {
        expect(mockSetToastMessage).toHaveBeenCalledWith({
          message: errorMessage,
          type: 'error',
        })
      })

      expect(mockSetDeletionStep).toHaveBeenCalledWith(0)
      expect(mockSetDeletionForm).toHaveBeenCalledWith({ username_confirmation: '' })
    })

    it('shows loading state during deletion', () => {
      const props = {
        ...step2Props,
        deletionLoading: true,
      }

      render(<AccountDeletionFlow {...props} />)

      expect(screen.getByText('删除中...')).toBeInTheDocument()
      const deleteButton = screen.getByRole('button', { name: /删除中.../ })
      expect(deleteButton).toBeDisabled()
    })
  })

  describe('Security and Edge Cases', () => {
    it('is case sensitive for username confirmation', async () => {
      const mockSetDeletionStep = jest.fn()
      const props = {
        ...defaultProps,
        deletionForm: {
          username_confirmation: 'TESTUSER', // Wrong case
        },
        setDeletionStep: mockSetDeletionStep,
      }

      render(<AccountDeletionFlow {...props} />)

      const continueButton = screen.getByRole('button', { name: /继续删除账户/ })
      await userEvent.click(continueButton)

      expect(mockSetToastMessage).toHaveBeenCalledWith({
        message: '用户名确认不匹配',
        type: 'error',
      })
      expect(mockSetDeletionStep).not.toHaveBeenCalled()
    })

    it('trims whitespace from username confirmation', async () => {
      const mockSetDeletionStep = jest.fn()
      const props = {
        ...defaultProps,
        deletionForm: {
          username_confirmation: '  testuser  ',
        },
        setDeletionStep: mockSetDeletionStep,
      }

      render(<AccountDeletionFlow {...props} />)

      const continueButton = screen.getByRole('button', { name: /继续删除账户/ })
      await userEvent.click(continueButton)

      expect(mockSetDeletionStep).toHaveBeenCalledWith(1)
    })

    it('handles unicode usernames correctly', async () => {
      const unicodeUser = {
        ...mockUser,
        username: '用户测试',
      }

      const mockSetDeletionStep = jest.fn()
      const props = {
        ...defaultProps,
        user: unicodeUser,
        deletionForm: {
          username_confirmation: '用户测试',
        },
        setDeletionStep: mockSetDeletionStep,
      }

      render(<AccountDeletionFlow {...props} />)

      const continueButton = screen.getByRole('button', { name: /继续删除账户/ })
      await userEvent.click(continueButton)

      expect(mockSetDeletionStep).toHaveBeenCalledWith(1)
    })

    it('prevents malicious username confirmation attempts', async () => {
      const mockSetDeletionStep = jest.fn()
      const props = {
        ...defaultProps,
        deletionForm: {
          username_confirmation: "testuser'; DROP TABLE users; --",
        },
        setDeletionStep: mockSetDeletionStep,
      }

      render(<AccountDeletionFlow {...props} />)

      const continueButton = screen.getByRole('button', { name: /继续删除账户/ })
      await userEvent.click(continueButton)

      expect(mockSetToastMessage).toHaveBeenCalledWith({
        message: '用户名确认不匹配',
        type: 'error',
      })
      expect(mockSetDeletionStep).not.toHaveBeenCalled()
    })

    it('disables form during loading state', () => {
      const props = {
        ...defaultProps,
        deletionLoading: true,
      }

      render(<AccountDeletionFlow {...props} />)

      const input = screen.getByPlaceholderText('请输入 "testuser" 以确认删除')
      const continueButton = screen.getByRole('button', { name: /继续删除账户/ })

      expect(input).toBeDisabled()
      expect(continueButton).toBeDisabled()
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(<AccountDeletionFlow {...defaultProps} />)

      const input = screen.getByLabelText(/确认删除，请输入您的用户名/)
      expect(input).toBeInTheDocument()

      const dangerSection = screen.getByText('危险操作 - 此操作不可逆')
      expect(dangerSection).toBeInTheDocument()
    })

    it('provides clear visual hierarchy', () => {
      render(<AccountDeletionFlow {...defaultProps} />)

      // Check that warning messages are prominently displayed
      const warningElements = screen.getAllByText(/危险|删除|永久/)
      expect(warningElements.length).toBeGreaterThan(0)
    })
  })
})