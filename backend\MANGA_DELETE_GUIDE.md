# 漫画数据删除工具使用指南

本项目提供了两个Python脚本来管理和删除漫画数据：

1. `delete_manga_with_relations.py` - 专门用于删除单个漫画
2. `manage_manga_data.py` - 综合管理工具（推荐）

## 安装依赖

首先确保安装了必要的依赖：

```bash
pip install tabulate
```

## 使用方法

### 1. 使用 manage_manga_data.py（推荐）

#### 交互式模式

直接运行脚本进入交互式界面：

```bash
python manage_manga_data.py
```

功能菜单：
- 列出所有漫画
- 搜索漫画
- 查看漫画详情
- 删除单个漫画
- 批量删除漫画

#### 命令行模式

```bash
# 列出所有漫画
python manage_manga_data.py --list

# 搜索漫画
python manage_manga_data.py --search "借妻"

# 查看漫画详情
python manage_manga_data.py --details 123

# 删除单个漫画
python manage_manga_data.py --delete 123

# 批量删除漫画
python manage_manga_data.py --batch-delete "123,456,789"

# 跳过确认提示
python manage_manga_data.py --delete 123 --no-confirm
```

### 2. 使用 delete_manga_with_relations.py

这个脚本专门用于删除单个漫画：

```bash
# 通过ID删除
python delete_manga_with_relations.py 123

# 通过标题搜索并删除
python delete_manga_with_relations.py "借妻條約"

# 搜索模式（列出匹配的漫画让你选择）
python delete_manga_with_relations.py --search "借妻"

# 模拟运行（不实际删除，只显示将要删除的内容）
python delete_manga_with_relations.py 123 --dry-run

# 强制删除（跳过确认）
python delete_manga_with_relations.py 123 --force
```

## 删除的数据范围

运行删除操作时，以下相关数据都会被删除：

1. **漫画基本信息** - manga表中的记录
2. **章节数据** - manga_chapters表中的所有章节
3. **页面数据** - manga_pages表中的所有页面
4. **类型关联** - manga_genres表中的关联
5. **标签关联** - manga_tags表中的关联
6. **用户收藏** - favorites表中的收藏记录
7. **用户书签** - manga_bookmarks表中的书签
8. **阅读进度** - manga_reading_progress表中的进度记录
9. **用户评论** - comments表中的评论

## 示例操作

### 删除"借妻條約"漫画

1. 首先搜索确认漫画信息：
```bash
python manage_manga_data.py --search "借妻"
```

2. 查看详细信息：
```bash
python manage_manga_data.py --details [找到的ID]
```

3. 确认后删除：
```bash
python manage_manga_data.py --delete [找到的ID]
```

### 批量删除多部漫画

```bash
# 交互式模式
python manage_manga_data.py
# 选择5 -> 输入ID列表如: 123,456,789

# 或命令行模式
python manage_manga_data.py --batch-delete "123,456,789"
```

## 安全提示

1. **删除操作不可逆**：删除的数据无法恢复，请谨慎操作
2. **建议先备份**：在删除重要数据前，建议先备份数据库
3. **使用dry-run**：不确定时，可以先使用--dry-run参数查看将要删除的内容
4. **检查依赖**：某些数据可能被其他系统引用，删除前请确认

## 数据库备份建议

在执行删除操作前，建议先备份数据库：

```bash
# MySQL备份示例
mysqldump -u root -p hentai > backup_$(date +%Y%m%d_%H%M%S).sql
```

## 故障排除

1. **数据库连接错误**：检查backend/.env中的数据库配置
2. **权限错误**：确保数据库用户有DELETE权限
3. **外键约束错误**：脚本已处理大部分外键关系，如仍有错误请检查数据库结构

## 注意事项

- 删除漫画不会删除实际的图片文件，只删除数据库记录
- 如果漫画有大量章节和页面，删除操作可能需要一些时间
- 建议在低峰期执行批量删除操作