# 统一爬虫系统 (Unified Crawler System)

## 概述

统一爬虫系统整合了三个原始爬虫的功能：
- **craw.py** - 获取海报和视频
- **getchuscraw.py** - 爬取剧照
- **hanimecraw.py** - 获取视频详细信息

现在所有功能都集成在 `unified_crawler.py` 中，支持完整的视频抓取、信息获取、剧照下载和数据库导入。

## 主要功能

### 🎯 核心特性

1. **视频抓取**: 从 Hanime1.me 按日期筛选抓取视频
2. **信息刮削**: 获取标题、简介、标签、制作商等详细信息
3. **剧照下载**: 从 Getchu.com 搜索并下载高清剧照
4. **数据库导入**: 自动导入到 MySQL 数据库 (animes, tags, anime_tags, categories)
5. **文件组织**: 按年/月/标题结构组织文件，每个视频独立目录
6. **NFO生成**: 为媒体中心生成标准NFO文件

### 📁 文件结构

```
downloads/
├── 2025/
│   └── 01/
│       └── 视频标题/
│           ├── 视频标题.mp4          # 主视频文件
│           ├── fanart.jpg           # 封面图片
│           ├── poster.jpg           # 海报图片
│           ├── 视频标题.nfo          # NFO信息文件
│           └── extrafanart/         # 剧照目录
│               ├── fanart1.jpg
│               ├── fanart2.jpg
│               └── ...
```

## 安装与配置

### 🔧 环境要求

```bash
# Python包依赖
pip install requests beautifulsoup4 lxml selenium cloudscraper pymysql pyyaml

# Chrome浏览器和ChromeDriver
# 确保Chrome和ChromeDriver版本匹配
```

### ⚙️ 配置文件

编辑 `config.yml` 文件：

```yaml
# 爬取配置
crawl:
  skip_keywords:
    - '中字後補'
    - '简中补字'  
    - 'Chinese Sub'
    - '中文字幕後補'
  quality_priority:
    - '1080'
    - '720' 
    - '480'
  date_filter:
    year: 2025      # 目标年份
    month: 1        # 目标月份

# 下载配置
download:
  download_dir: 'downloads'
  organize_by_date: true

# 数据库配置
database:
  host: "************:3306"
  user: "sql23721_hentai"
  password: "507877550@lihao"
  database: "sql23721_hentai"
  charset: "utf8mb4"

# Web访问配置 (用于生成完整URL)
web_access:
  domain_prefix: 'https://static.denlu.top'
  base_path: '/downloads'
```

### 🗄️ 数据库表结构

系统使用以下数据库表：

```sql
-- 动画主表
CREATE TABLE animes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255),              -- 中文标题
    title_english VARCHAR(255),      -- 英文标题 (留空)
    title_japanese VARCHAR(255),     -- 日文原标题
    description TEXT,                -- 简介
    cover VARCHAR(500),              -- 封面URL
    fanart TEXT,                     -- 剧照URLs (逗号分隔)
    video_url VARCHAR(500),          -- 视频URL
    release_year INT,                -- 发布年份
    release_date DATE,               -- 发布日期
    view_count INT DEFAULT 0,        -- 观看次数 (随机1000-10000)
    favorite_count INT DEFAULT 0,    -- 收藏次数
    is_active BOOLEAN DEFAULT TRUE,  -- 是否活跃
    category_id INT,                 -- 分类ID (里番)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 标签表
CREATE TABLE tags (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) UNIQUE
);

-- 动画标签关联表
CREATE TABLE anime_tags (
    anime_id INT,
    tag_id INT,
    PRIMARY KEY (anime_id, tag_id)
);

-- 分类表
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50),
    num INT DEFAULT 0
);
```

## 使用方法

### 🚀 基本使用

```python
from unified_crawler import UnifiedCrawler

# 创建爬虫实例
crawler = UnifiedCrawler(config_path="config.yml")

# 执行完整爬虫流程
crawler.run_full_crawl()
```

### 📝 运行脚本

使用提供的测试脚本：

```bash
# 爬取2025年1月数据
python test_crawler_jan2025.py

# 验证数据库结果
python verify_db.py
```

### 🎯 按标题爬取

```python
# 根据特定标题爬取
test_title = "某个动画标题"
crawler.crawl_by_title(test_title)
```

## 数据库映射

### 📊 字段映射关系

| 源数据字段 | 数据库字段 | 说明 |
|-----------|-----------|------|
| `tagline` | `title` | 中文标题 |
| 无 | `title_english` | 英文标题 (留空) |
| `title/originaltitle` | `title_japanese` | 日文原标题 |
| `plot` | `description` | 简介 (移除CDATA，转换<br>为换行) |
| 封面文件路径 | `cover` | 封面完整URL |
| extrafanart目录 | `fanart` | 剧照URLs逗号分隔字符串 |
| 视频文件路径 | `video_url` | 视频文件完整URL |
| `year` | `release_year` | 发布年份 |
| `premiered` | `release_date` | 发布日期 |
| 随机生成 | `view_count` | 观看次数 (1000-10000) |
| "里番" | `category_id` | 分类ID |

### 🏷️ 标签处理

- 从 `tag` 或 `genre` 字段提取标签列表
- 自动创建不存在的标签
- 建立 `anime_tags` 关联关系
- 支持中文、英文、日文标签

## URL生成规则

### 🌐 Web访问URL

基于配置文件的 `web_access` 部分生成完整URL：

```yaml
web_access:
  domain_prefix: 'https://static.denlu.top'
  base_path: '/downloads'
```

生成的URL格式：
- 视频: `https://static.denlu.top/downloads/2025/01/视频标题/视频标题.mp4`
- 封面: `https://static.denlu.top/downloads/2025/01/视频标题/fanart.jpg`  
- 剧照: `https://static.denlu.top/downloads/2025/01/视频标题/extrafanart/fanart1.jpg`

### 📂 本地文件路径

相对于 `download_dir` 的路径：
- `2025/01/视频标题/视频标题.mp4`
- `2025/01/视频标题/extrafanart/fanart1.jpg`

## 高级功能

### 🎨 图片处理

1. **封面图片**: 从列表页获取，保存为 `fanart.jpg`
2. **剧照下载**: 从 Getchu.com 搜索相关游戏，下载样本图片
3. **海报图片**: package.jpg 重命名为 `poster.jpg` 保存到视频目录
4. **剧照重命名**: sample1.jpg → fanart1.jpg, sample2.jpg → fanart2.jpg

### 🔍 搜索匹配

**Getchu.com 搜索算法**:
1. 完全匹配 (得分1000)
2. 忽略大小写匹配 (得分900) 
3. 包含匹配 (得分500)
4. 关键词匹配 (每个词50分)

### 📋 合集检测

自动检测动画系列：
- 识别卷号: Vol.1, 第1话, 前编, 后编等
- 提取系列名称，设置NFO的 `<set>` 标签
- 支持中日文格式识别

## 故障排除

### ❗ 常见问题

1. **Chrome/ChromeDriver版本不匹配**
   ```bash
   # 检查Chrome版本
   chrome --version
   # 下载对应版本的ChromeDriver
   ```

2. **数据库连接失败**
   - 检查数据库服务是否运行
   - 验证连接参数 (host, port, user, password)
   - 确保数据库存在且有权限

3. **标签插入失败**
   - 检查 `tags` 表是否存在
   - 确认字符编码为 utf8mb4
   - 查看错误日志获取详细信息

4. **文件下载失败** 
   - 检查网络连接
   - 验证目标网站是否可访问
   - 确认磁盘空间充足

### 🔧 调试方法

1. **启用详细日志**:
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **检查数据库状态**:
   ```bash
   python verify_db.py
   ```

3. **测试单个功能**:
   ```python
   # 只测试搜索功能
   getchu_url = crawler.search_getchu("测试标题")
   
   # 只测试信息获取
   info = crawler.scrape_hanime_info("102260")
   ```

## 性能优化

### ⚡ 优化建议

1. **并发下载**: 设置合理的并发数量
2. **缓存机制**: 避免重复下载已存在文件  
3. **断点续传**: 支持大文件断点续传
4. **数据库索引**: 为常用查询字段添加索引
5. **定期清理**: 清理临时文件和无效记录

### 📈 监控指标

- 爬取成功率
- 数据库导入成功率  
- 平均处理时间
- 存储空间使用情况
- 标签关联完整性

## 更新日志

### v2.0.0 (2025-01-13)
- ✅ 修复 fanart URL 存储格式 (最后一个URL后不加逗号)
- ✅ 修复 tags 和 anime_tags 插入问题
- ✅ 完善错误处理和日志记录
- ✅ 测试通过 2025年1月数据爬取和数据库导入
- ✅ 支持完整的 Web 访问 URL 生成

### v1.0.0 (2025-01-12)
- 🎉 初始版本，整合三个爬虫功能
- 📁 实现年/月/标题目录结构
- 🗄️ 支持 MySQL 数据库导入
- 🎨 集成 Getchu.com 剧照下载

## 许可证

此项目仅供学习和研究使用。请遵守相关网站的使用条款和版权规定。

---

**联系方式**: 如有问题请提交 Issue 或联系开发团队。