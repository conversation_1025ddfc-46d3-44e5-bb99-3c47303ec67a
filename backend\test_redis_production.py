"""
测试Redis生产环境配置
包括密码认证、内存限制等功能
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import SessionLocal
from app.core.redis_config import RedisConfigManager
from app.core.redis_client import redis_client
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_production_config():
    """测试生产环境配置"""
    
    print("\n" + "="*60)
    print("Redis生产环境配置测试")
    print("="*60 + "\n")
    
    db = SessionLocal()
    
    try:
        # 1. 测试开发环境配置
        print("1. 测试开发环境配置（无密码）...")
        dev_config = {
            'redis_enabled': True,
            'redis_host': 'localhost',
            'redis_port': 6379,
            'redis_db': 0,
            'redis_password': '',  # 无密码
            'redis_max_memory': 100,
            'redis_expire_time': 3600,
            'redis_max_connections': 20
        }
        
        success = RedisConfigManager.update_configs(db, dev_config)
        if success:
            print("   [OK] 开发环境配置已设置")
            
            # 测试连接
            await redis_client.init_redis(force_reconnect=True)
            if redis_client.is_available:
                print("   [OK] 开发环境连接成功（无密码）")
                
                # 测试基本操作
                test_key = "test:dev:config"
                test_value = {"env": "development", "auth": "none"}
                await redis_client.set(test_key, test_value, 60)
                cached = await redis_client.get(test_key)
                if cached:
                    print(f"   [OK] 缓存操作成功: {cached}")
                await redis_client.delete(test_key)
            else:
                print("   [FAIL] 开发环境连接失败")
        else:
            print("   [FAIL] 开发环境配置失败")
        
        # 2. 测试生产环境配置预设
        print("\n2. 测试生产环境配置预设...")
        prod_config = {
            'redis_enabled': True,
            'redis_host': '***********',
            'redis_port': 6379,
            'redis_db': 0,
            'redis_password': '',  # 生产环境应该有密码，这里测试无密码情况
            'redis_max_memory': 512,  # 512MB限制
            'redis_expire_time': 7200,  # 2小时
            'redis_max_connections': 100
        }
        
        # 验证配置
        is_valid, message = RedisConfigManager.validate_config(prod_config)
        if is_valid:
            print("   [OK] 生产环境配置验证通过")
            
            success = RedisConfigManager.update_configs(db, prod_config)
            if success:
                print("   [OK] 生产环境配置已设置")
                
                # 测试连接（如果Redis服务器可用）
                await redis_client.init_redis(force_reconnect=True)
                if redis_client.is_available:
                    print("   [OK] 生产环境连接成功")
                    
                    # 检查内存限制
                    memory_info = await redis_client.get_memory_info()
                    if memory_info:
                        print(f"   [INFO] 内存限制已设置: {memory_info['maxmemory_human']}")
                    
                    # 测试高负载操作
                    print("   [INFO] 测试高负载缓存操作...")
                    for i in range(10):
                        key = f"test:prod:load:{i}"
                        value = {"id": i, "data": "x" * 1000}  # 1KB数据
                        await redis_client.set(key, value, 60)
                    print("   [OK] 高负载测试完成")
                    
                    # 清理测试数据
                    for i in range(10):
                        await redis_client.delete(f"test:prod:load:{i}")
                    print("   [OK] 测试数据已清理")
                else:
                    print("   [WARN] 生产Redis服务器不可用，跳过连接测试")
            else:
                print("   [FAIL] 生产环境配置失败")
        else:
            print(f"   [FAIL] 生产环境配置无效: {message}")
        
        # 3. 测试配置验证功能
        print("\n3. 测试配置验证功能...")
        
        invalid_configs = [
            ({'redis_port': 99999}, "端口号超出范围"),
            ({'redis_db': 16}, "数据库编号超出范围"),
            ({'redis_max_memory': -1}, "负数内存限制"),
            ({'redis_expire_time': -1}, "负数过期时间"),
            ({'redis_max_connections': 0}, "连接数为0"),
        ]
        
        for invalid_config, desc in invalid_configs:
            is_valid, message = RedisConfigManager.validate_config(invalid_config)
            if not is_valid:
                print(f"   [OK] {desc}验证失败: {message}")
            else:
                print(f"   [FAIL] {desc}应该验证失败但通过了")
        
        # 4. 测试密码功能（模拟）
        print("\n4. 测试密码配置功能...")
        password_config = {
            'redis_password': 'production_password_123'
        }
        
        success = RedisConfigManager.update_configs(db, password_config)
        if success:
            print("   [OK] 密码配置已保存")
            
            # 验证Redis URL构建
            redis_url = RedisConfigManager.get_redis_url(db)
            if redis_url and ":production_password_123@" in redis_url:
                print("   [OK] Redis URL正确包含密码")
                print(f"   [INFO] Redis URL格式: redis://:****@host:port/db")
            else:
                print("   [FAIL] Redis URL密码格式错误")
        else:
            print("   [FAIL] 密码配置保存失败")
        
        # 5. 恢复默认配置
        print("\n5. 恢复默认配置...")
        RedisConfigManager.update_configs(db, {
            'redis_enabled': False,
            'redis_password': ''
        })
        await redis_client.init_redis(force_reconnect=True)
        print("   [OK] 已恢复默认配置并禁用Redis")
        
        print("\n" + "="*60)
        print("生产环境配置测试完成！")
        print("="*60 + "\n")
        
        print("生产环境部署建议:")
        print("- 设置强密码保护Redis服务器")
        print("- 配置合适的内存限制（如512MB-2GB）")
        print("- 设置适当的过期时间（1-4小时）")
        print("- 根据并发需求调整连接池大小")
        print("- 定期监控内存使用情况")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        print(f"\n[ERROR] 测试失败: {e}")
    finally:
        db.close()
        await redis_client.close_redis()

if __name__ == "__main__":
    asyncio.run(test_production_config())