'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Database, Server, Wifi, Lock, Timer, HardDrive, 
  Play, Pause, RefreshCw, Trash2, TestTube, Save,
  AlertCircle, CheckCircle, XCircle, Info
} from 'lucide-react';
import { apiClient } from '@/lib/api';

interface RedisConfig {
  redis_enabled: boolean;
  redis_host: string;
  redis_port: number;
  redis_db: number;
  redis_password: string;
  redis_max_memory: number;
  redis_expire_time: number;
  redis_max_connections: number;
}

interface RedisStatus {
  connected: boolean;
  enabled: boolean;
  host?: string;
  port?: number;
  db?: number;
  memory_info?: {
    used_memory: number;
    used_memory_human: string;
    used_memory_peak: number;
    used_memory_peak_human: string;
    maxmemory: number;
    maxmemory_human: string;
    maxmemory_policy: string;
    mem_fragmentation_ratio: number;
  };
  error?: string;
}

export default function RedisConfiguration() {
  const [config, setConfig] = useState<RedisConfig>({
    redis_enabled: false,
    redis_host: 'localhost',
    redis_port: 6379,
    redis_db: 0,
    redis_password: '',
    redis_max_memory: 0,
    redis_expire_time: 3600,
    redis_max_connections: 50
  });

  const [status, setStatus] = useState<RedisStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);

  // 获取配置
  const fetchConfig = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${apiClient.baseURL}/admin/redis/config`, {
        headers: apiClient.getAuthHeaders()
      });
      if (response.ok) {
        const data = await response.json();
        setConfig(data);
      }
    } catch (error) {
      console.error('获取Redis配置失败:', error);
      setMessage({ type: 'error', text: '获取配置失败' });
    } finally {
      setLoading(false);
    }
  };

  // 获取状态
  const fetchStatus = async () => {
    try {
      const response = await fetch(`${apiClient.baseURL}/admin/redis/status`, {
        headers: apiClient.getAuthHeaders()
      });
      if (response.ok) {
        const data = await response.json();
        setStatus(data);
      }
    } catch (error) {
      console.error('获取Redis状态失败:', error);
    }
  };

  // 保存配置
  const saveConfig = async () => {
    setSaving(true);
    setMessage(null);
    try {
      const response = await fetch(`${apiClient.baseURL}/admin/redis/config`, {
        method: 'PUT',
        headers: {
          ...apiClient.getAuthHeaders(),
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      });

      if (response.ok) {
        setMessage({ type: 'success', text: '配置保存成功，Redis已重新连接' });
        // 刷新状态
        setTimeout(fetchStatus, 1000);
      } else {
        const error = await response.json();
        setMessage({ type: 'error', text: error.detail || '保存失败' });
      }
    } catch (error) {
      console.error('保存Redis配置失败:', error);
      setMessage({ type: 'error', text: '保存配置失败' });
    } finally {
      setSaving(false);
    }
  };

  // 测试连接
  const testConnection = async () => {
    setTesting(true);
    setMessage(null);
    try {
      const response = await fetch(`${apiClient.baseURL}/admin/redis/test-connection`, {
        method: 'POST',
        headers: {
          ...apiClient.getAuthHeaders(),
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          host: config.redis_host,
          port: config.redis_port,
          db: config.redis_db,
          password: config.redis_password
        })
      });

      const result = await response.json();
      if (result.success) {
        setMessage({ type: 'success', text: `连接成功! Redis版本: ${result.server_info?.redis_version}` });
      } else {
        setMessage({ type: 'error', text: result.message || '连接失败' });
      }
    } catch (error) {
      console.error('测试连接失败:', error);
      setMessage({ type: 'error', text: '测试连接失败' });
    } finally {
      setTesting(false);
    }
  };

  // 清空缓存
  const flushCache = async () => {
    if (!confirm('确定要清空所有Redis缓存吗？此操作不可恢复！')) {
      return;
    }

    try {
      const response = await fetch(`${apiClient.baseURL}/admin/redis/flush`, {
        method: 'POST',
        headers: apiClient.getAuthHeaders()
      });

      if (response.ok) {
        setMessage({ type: 'success', text: 'Redis缓存已清空' });
        setTimeout(fetchStatus, 1000);
      } else {
        const error = await response.json();
        setMessage({ type: 'error', text: error.detail || '清空失败' });
      }
    } catch (error) {
      console.error('清空缓存失败:', error);
      setMessage({ type: 'error', text: '清空缓存失败' });
    }
  };

  // 初始化默认配置
  const initializeConfig = async () => {
    try {
      const response = await fetch(`${apiClient.baseURL}/admin/redis/initialize`, {
        method: 'POST',
        headers: apiClient.getAuthHeaders()
      });

      if (response.ok) {
        setMessage({ type: 'success', text: '默认配置已初始化' });
        fetchConfig();
      } else {
        const error = await response.json();
        setMessage({ type: 'error', text: error.detail || '初始化失败' });
      }
    } catch (error) {
      console.error('初始化配置失败:', error);
      setMessage({ type: 'error', text: '初始化配置失败' });
    }
  };

  useEffect(() => {
    fetchConfig();
    fetchStatus();
    // 定期刷新状态
    const interval = setInterval(fetchStatus, 30000); // 30秒刷新一次
    return () => clearInterval(interval);
  }, []);

  // 格式化内存大小
  const formatMemory = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* 状态卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Wifi className="w-4 h-4" />
              连接状态
            </CardTitle>
          </CardHeader>
          <CardContent>
            {status?.connected ? (
              <Badge className="bg-green-500">已连接</Badge>
            ) : (
              <Badge variant="destructive">未连接</Badge>
            )}
            {status?.host && (
              <p className="text-xs text-muted-foreground mt-2">
                {status.host}:{status.port}/{status.db}
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <HardDrive className="w-4 h-4" />
              内存使用
            </CardTitle>
          </CardHeader>
          <CardContent>
            {status?.memory_info ? (
              <div className="space-y-1">
                <p className="text-sm font-medium">
                  {status.memory_info.used_memory_human}
                </p>
                {status.memory_info.maxmemory > 0 && (
                  <p className="text-xs text-muted-foreground">
                    限制: {status.memory_info.maxmemory_human}
                  </p>
                )}
                <p className="text-xs text-muted-foreground">
                  峰值: {status.memory_info.used_memory_peak_human}
                </p>
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">无数据</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Database className="w-4 h-4" />
              缓存策略
            </CardTitle>
          </CardHeader>
          <CardContent>
            {status?.memory_info ? (
              <div className="space-y-1">
                <p className="text-sm font-medium">
                  {status.memory_info.maxmemory_policy}
                </p>
                <p className="text-xs text-muted-foreground">
                  碎片率: {status.memory_info.mem_fragmentation_ratio}
                </p>
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">无数据</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 消息提示 */}
      {message && (
        <Alert className={
          message.type === 'success' ? 'border-green-500' :
          message.type === 'error' ? 'border-red-500' : 'border-blue-500'
        }>
          {message.type === 'success' && <CheckCircle className="w-4 h-4" />}
          {message.type === 'error' && <XCircle className="w-4 h-4" />}
          {message.type === 'info' && <Info className="w-4 h-4" />}
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      {/* 配置表单 */}
      <Card>
        <CardHeader>
          <CardTitle>Redis/DragonflyDB配置</CardTitle>
          <CardDescription>
            配置Redis或DragonflyDB缓存服务器连接参数和性能设置
            （DragonflyDB与Redis协讬完全兼容）
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 启用开关 */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>启用Redis/DragonflyDB缓存</Label>
              <p className="text-sm text-muted-foreground">
                启用后将使用Redis或DragonflyDB加速网站访问
              </p>
            </div>
            <Switch
              checked={config.redis_enabled}
              onCheckedChange={(checked) => 
                setConfig({ ...config, redis_enabled: checked })
              }
            />
          </div>

          {/* 连接配置 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="redis_host">
                <Server className="w-4 h-4 inline mr-1" />
                服务器地址
              </Label>
              <Input
                id="redis_host"
                value={config.redis_host}
                onChange={(e) => setConfig({ ...config, redis_host: e.target.value })}
                placeholder="localhost"
                disabled={!config.redis_enabled}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="redis_port">
                端口
              </Label>
              <Input
                id="redis_port"
                type="number"
                value={config.redis_port}
                onChange={(e) => setConfig({ ...config, redis_port: parseInt(e.target.value) || 6379 })}
                placeholder="6379"
                disabled={!config.redis_enabled}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="redis_db">
                <Database className="w-4 h-4 inline mr-1" />
                数据库编号
              </Label>
              <Input
                id="redis_db"
                type="number"
                value={config.redis_db}
                onChange={(e) => setConfig({ ...config, redis_db: parseInt(e.target.value) || 0 })}
                placeholder="0"
                min="0"
                max="15"
                disabled={!config.redis_enabled}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="redis_password">
                <Lock className="w-4 h-4 inline mr-1" />
                密码（生产环境建议设置）
              </Label>
              <Input
                id="redis_password"
                type="password"
                value={config.redis_password}
                onChange={(e) => setConfig({ ...config, redis_password: e.target.value })}
                placeholder="留空表示无密码认证"
                disabled={!config.redis_enabled}
              />
              <p className="text-xs text-muted-foreground">
                生产环境强烈建议设置密码以提高安全性
              </p>
            </div>
          </div>

          {/* 性能配置 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="redis_max_memory">
                <HardDrive className="w-4 h-4 inline mr-1" />
                最大内存限制 (MB)
              </Label>
              <Input
                id="redis_max_memory"
                type="number"
                value={config.redis_max_memory}
                onChange={(e) => setConfig({ ...config, redis_max_memory: parseInt(e.target.value) || 0 })}
                placeholder="0"
                min="0"
                disabled={!config.redis_enabled}
              />
              <p className="text-xs text-muted-foreground">
                0 表示无限制。生产环境建议设置合理值（如512MB）避免内存溢出
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="redis_expire_time">
                <Timer className="w-4 h-4 inline mr-1" />
                默认过期时间 (秒)
              </Label>
              <Input
                id="redis_expire_time"
                type="number"
                value={config.redis_expire_time}
                onChange={(e) => setConfig({ ...config, redis_expire_time: parseInt(e.target.value) || 3600 })}
                placeholder="3600"
                min="0"
                disabled={!config.redis_enabled}
              />
              <p className="text-xs text-muted-foreground">
                缓存数据的默认生存时间
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="redis_max_connections">
                最大连接数
              </Label>
              <Input
                id="redis_max_connections"
                type="number"
                value={config.redis_max_connections}
                onChange={(e) => setConfig({ ...config, redis_max_connections: parseInt(e.target.value) || 50 })}
                placeholder="50"
                min="1"
                disabled={!config.redis_enabled}
              />
              <p className="text-xs text-muted-foreground">
                连接池的最大连接数
              </p>
            </div>
          </div>

          {/* 快速配置预设 */}
          <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-900">
            <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
              <Info className="w-4 h-4" />
              快速配置预设
            </h4>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setConfig({
                    ...config,
                    redis_host: 'localhost',
                    redis_port: 6379,
                    redis_db: 0,
                    redis_password: '',
                    redis_max_memory: 100,
                    redis_expire_time: 3600,
                    redis_max_connections: 20
                  });
                  setMessage({ type: 'info', text: '已应用开发环境配置' });
                }}
                disabled={!config.redis_enabled}
              >
                开发环境
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setConfig({
                    ...config,
                    redis_host: '***********',
                    redis_port: 6379,
                    redis_db: 0,
                    redis_password: '',
                    redis_max_memory: 512,
                    redis_expire_time: 7200,
                    redis_max_connections: 100
                  });
                  setMessage({ type: 'info', text: '已应用生产环境配置（请设置密码）' });
                }}
                disabled={!config.redis_enabled}
              >
                生产环境
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setConfig({
                    ...config,
                    redis_host: 'localhost',
                    redis_port: 6379,
                    redis_db: 1,
                    redis_password: '',
                    redis_max_memory: 256,
                    redis_expire_time: 1800,
                    redis_max_connections: 50
                  });
                  setMessage({ type: 'info', text: '已应用测试环境配置' });
                }}
                disabled={!config.redis_enabled}
              >
                测试环境
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setConfig({
                    ...config,
                    redis_host: '***********',
                    redis_port: 16379,  // DragonflyDB默认端口
                    redis_db: 0,
                    redis_password: '',
                    redis_max_memory: 1024,  // DragonflyDB性能更好，可设置更大
                    redis_expire_time: 7200,
                    redis_max_connections: 200  // DragonflyDB支持更多连接
                  });
                  setMessage({ type: 'info', text: '已应用DragonflyDB配置（高性能）' });
                }}
                disabled={!config.redis_enabled}
              >
                DragonflyDB
              </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              根据环境选择合适的配置预设。DragonflyDB提供更高的性能和内存效率
            </p>
          </div>

          {/* 操作按钮 */}
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={saveConfig}
              disabled={saving || loading}
            >
              {saving ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  保存配置
                </>
              )}
            </Button>

            <Button
              variant="outline"
              onClick={testConnection}
              disabled={testing || loading || !config.redis_enabled}
            >
              {testing ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  测试中...
                </>
              ) : (
                <>
                  <TestTube className="w-4 h-4 mr-2" />
                  测试连接
                </>
              )}
            </Button>

            <Button
              variant="outline"
              onClick={flushCache}
              disabled={!status?.connected}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              清空缓存
            </Button>

            <Button
              variant="outline"
              onClick={initializeConfig}
              disabled={loading}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              初始化默认配置
            </Button>

            <Button
              variant="outline"
              onClick={() => {
                fetchConfig();
                fetchStatus();
              }}
              disabled={loading}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              刷新状态
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">使用说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-muted-foreground">
          <p>• Redis缓存可以显著提升网站访问速度，减少数据库压力</p>
          <p>• 内存限制设置为0表示无限制，建议根据服务器内存合理设置</p>
          <p>• 当内存达到限制时，将根据LRU策略自动淘汰旧数据</p>
          <p>• 修改配置后会自动重新连接Redis服务器</p>
          <p>• 清空缓存操作不可恢复，请谨慎使用</p>
        </CardContent>
      </Card>
    </div>
  );
}