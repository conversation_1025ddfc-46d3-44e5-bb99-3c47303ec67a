#!/usr/bin/env python3
"""
删除指定漫画及其所有相关数据的脚本
包括：章节、页面、收藏、书签、阅读进度、评论等
"""

import sys
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import argparse
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.core.database import Base
from app.models import (
    Manga, MangaChapter, MangaPage, MangaTag, 
    MangaReadingProgress, Favorite, Comment, ContentType
)


def create_db_engine():
    """创建数据库连接引擎"""
    return create_engine(
        settings.DATABASE_URL,
        pool_pre_ping=True,
        echo=False
    )


def get_manga_info(session, manga_identifier):
    """获取漫画信息，支持ID或标题查询"""
    # 首先尝试按ID查询
    if manga_identifier.isdigit():
        manga = session.query(Manga).filter(Manga.id == int(manga_identifier)).first()
        if manga:
            return manga
    
    # 按标题查询
    manga = session.query(Manga).filter(Manga.title.like(f'%{manga_identifier}%')).first()
    return manga


def confirm_deletion(manga):
    """确认删除操作"""
    print(f"\n准备删除漫画：")
    print(f"  ID: {manga.id}")
    print(f"  标题: {manga.title}")
    print(f"  作者: {manga.author}")
    print(f"  创建时间: {manga.created_at}")
    
    response = input("\n确定要删除这部漫画及其所有相关数据吗？(yes/no): ")
    return response.lower() == 'yes'


def delete_manga_data(session, manga_id, dry_run=False):
    """删除漫画及其所有相关数据"""
    
    deleted_counts = {}
    
    try:
        # 1. 删除漫画页面（通过章节级联）
        page_count = session.execute(
            text("""
                SELECT COUNT(*) FROM manga_pages mp
                JOIN manga_chapters mc ON mp.chapter_id = mc.id
                WHERE mc.manga_id = :manga_id
            """),
            {"manga_id": manga_id}
        ).scalar()
        deleted_counts['manga_pages'] = page_count
        
        if not dry_run:
            session.execute(
                text("""
                    DELETE mp FROM manga_pages mp
                    JOIN manga_chapters mc ON mp.chapter_id = mc.id
                    WHERE mc.manga_id = :manga_id
                """),
                {"manga_id": manga_id}
            )
        
        # 2. 删除漫画章节
        chapter_count = session.query(MangaChapter).filter(
            MangaChapter.manga_id == manga_id
        ).count()
        deleted_counts['manga_chapters'] = chapter_count
        
        if not dry_run:
            session.query(MangaChapter).filter(
                MangaChapter.manga_id == manga_id
            ).delete()
        
        # 3. 删除漫画标签关联
        tag_count = session.query(MangaTag).filter(
            MangaTag.manga_id == manga_id
        ).count()
        deleted_counts['manga_tags'] = tag_count
        
        if not dry_run:
            session.query(MangaTag).filter(
                MangaTag.manga_id == manga_id
            ).delete()
        
        # 4. 删除阅读进度
        progress_count = session.query(MangaReadingProgress).filter(
            MangaReadingProgress.manga_id == manga_id
        ).count()
        deleted_counts['reading_progress'] = progress_count
        
        if not dry_run:
            session.query(MangaReadingProgress).filter(
                MangaReadingProgress.manga_id == manga_id
            ).delete()
        
        # 5. 删除收藏
        favorite_count = session.query(Favorite).filter(
            Favorite.content_type == ContentType.MANGA,
            Favorite.manga_id == manga_id
        ).count()
        deleted_counts['favorites'] = favorite_count
        
        if not dry_run:
            session.query(Favorite).filter(
                Favorite.content_type == ContentType.MANGA,
                Favorite.manga_id == manga_id
            ).delete()
        
        # 6. 删除评论
        comment_count = session.query(Comment).filter(
            Comment.content_type == ContentType.MANGA,
            Comment.manga_id == manga_id
        ).count()
        deleted_counts['comments'] = comment_count
        
        if not dry_run:
            session.query(Comment).filter(
                Comment.content_type == ContentType.MANGA,
                Comment.manga_id == manga_id
            ).delete()
        
        # 7. 最后删除漫画本身
        deleted_counts['manga'] = 1
        
        if not dry_run:
            session.query(Manga).filter(Manga.id == manga_id).delete()
            session.commit()
            print("\n✅ 删除成功！")
        else:
            print("\n🔍 模拟运行结果（未实际删除）：")
        
        # 显示删除统计
        print("\n删除数据统计：")
        print(f"  漫画记录: {deleted_counts.get('manga', 0)} 条")
        print(f"  章节记录: {deleted_counts.get('manga_chapters', 0)} 条")
        print(f"  页面记录: {deleted_counts.get('manga_pages', 0)} 条")
        print(f"  标签关联: {deleted_counts.get('manga_tags', 0)} 条")
        print(f"  阅读进度: {deleted_counts.get('reading_progress', 0)} 条")
        print(f"  收藏记录: {deleted_counts.get('favorites', 0)} 条")
        print(f"  评论记录: {deleted_counts.get('comments', 0)} 条")
        
        total = sum(deleted_counts.values())
        print(f"\n总计删除: {total} 条记录")
        
    except Exception as e:
        session.rollback()
        print(f"\n❌ 删除失败: {str(e)}")
        raise


def search_manga(session, keyword):
    """搜索漫画"""
    mangas = session.query(Manga).filter(
        Manga.title.like(f'%{keyword}%')
    ).all()
    
    if not mangas:
        print(f"\n未找到包含 '{keyword}' 的漫画")
        return None
    
    print(f"\n找到 {len(mangas)} 部漫画：")
    for i, manga in enumerate(mangas, 1):
        print(f"{i}. ID:{manga.id} - {manga.title} (作者: {manga.author})")
    
    if len(mangas) == 1:
        return mangas[0]
    
    # 让用户选择
    while True:
        try:
            choice = input(f"\n请选择要删除的漫画 (1-{len(mangas)}) 或输入 0 取消: ")
            if choice == '0':
                return None
            
            index = int(choice) - 1
            if 0 <= index < len(mangas):
                return mangas[index]
            else:
                print("无效的选择，请重试")
        except ValueError:
            print("请输入有效的数字")


def main():
    parser = argparse.ArgumentParser(description='删除指定漫画及其所有相关数据')
    parser.add_argument('manga', help='漫画ID或标题关键字')
    parser.add_argument('--dry-run', action='store_true', help='模拟运行，不实际删除')
    parser.add_argument('--force', action='store_true', help='跳过确认直接删除')
    parser.add_argument('--search', action='store_true', help='搜索模式，列出匹配的漫画')
    
    args = parser.parse_args()
    
    # 创建数据库会话
    engine = create_db_engine()
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # 搜索模式
        if args.search:
            manga = search_manga(session, args.manga)
            if not manga:
                return
        else:
            # 获取漫画信息
            manga = get_manga_info(session, args.manga)
            if not manga:
                print(f"\n❌ 未找到漫画: {args.manga}")
                print("提示：可以使用 --search 参数进行搜索")
                return
        
        # 确认删除
        if not args.force and not args.dry_run:
            if not confirm_deletion(manga):
                print("\n已取消删除操作")
                return
        
        # 执行删除
        delete_manga_data(session, manga.id, dry_run=args.dry_run)
        
    except Exception as e:
        print(f"\n❌ 发生错误: {str(e)}")
        sys.exit(1)
    finally:
        session.close()


if __name__ == "__main__":
    main()