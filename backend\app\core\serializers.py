import json
import datetime
import enum
from decimal import Decimal
from typing import Any
from sqlalchemy.ext.declarative import DeclarativeMeta
from sqlalchemy.orm import ColumnProperty
import logging

logger = logging.getLogger(__name__)

def sqlalchemy_to_dict(obj):
    """将SQLAlchemy对象转换为字典"""
    if hasattr(obj, '__table__'):
        # 这是一个SQLAlchemy模型对象
        result = {}
        for column in obj.__table__.columns:
            value = getattr(obj, column.name)
            # 处理特殊类型
            if isinstance(value, datetime.datetime):
                result[column.name] = value.isoformat()
            elif isinstance(value, datetime.date):
                result[column.name] = value.isoformat()
            elif isinstance(value, Decimal):
                result[column.name] = float(value)
            elif isinstance(value, enum.Enum):
                result[column.name] = value.value
            else:
                result[column.name] = value
        return result
    return obj

def serialize_for_cache(data: Any) -> Any:
    """为缓存准备数据的递归序列化"""
    if data is None:
        return None
    
    # 处理枚举类型
    if isinstance(data, enum.Enum):
        return data.value
    
    # 处理SQLAlchemy对象
    elif hasattr(data, '__table__'):
        return sqlalchemy_to_dict(data)
    
    # 处理列表
    elif isinstance(data, list):
        return [serialize_for_cache(item) for item in data]
    
    # 处理字典
    elif isinstance(data, dict):
        return {key: serialize_for_cache(value) for key, value in data.items()}
    
    # 处理日期时间
    elif isinstance(data, datetime.datetime):
        return data.isoformat()
    elif isinstance(data, datetime.date):
        return data.isoformat()
    
    # 处理Decimal
    elif isinstance(data, Decimal):
        return float(data)
    
    # 处理其他对象（可能是Pydantic模型或其他自定义对象）
    elif hasattr(data, '__dict__') and not isinstance(data, (str, int, float, bool)):
        # 尝试使用对象的__dict__
        try:
            if hasattr(data, 'dict') and callable(data.dict):
                # Pydantic模型
                return data.dict()
            elif hasattr(data, 'model_dump') and callable(data.model_dump):
                # Pydantic v2模型
                return data.model_dump()
            else:
                # 普通对象，使用__dict__
                result = {}
                for k, v in data.__dict__.items():
                    if not k.startswith('_'):
                        result[k] = serialize_for_cache(v)
                return result
        except Exception as e:
            logger.warning(f"Failed to serialize object {type(data)}: {e}")
            # 如果序列化失败，返回对象的字符串表示
            return str(data)
    
    # 基本类型直接返回
    return data

class CacheJSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，支持SQLAlchemy对象、枚举类型和其他特殊类型"""
    
    def default(self, obj):
        # 处理枚举类型
        if isinstance(obj, enum.Enum):
            return obj.value
        
        # 处理SQLAlchemy对象
        elif hasattr(obj, '__table__'):
            return sqlalchemy_to_dict(obj)
        
        # 处理日期时间
        elif isinstance(obj, datetime.datetime):
            return obj.isoformat()
        elif isinstance(obj, datetime.date):
            return obj.isoformat()
        
        # 处理Decimal
        elif isinstance(obj, Decimal):
            return float(obj)
        
        # 处理Pydantic模型
        elif hasattr(obj, 'dict') and callable(obj.dict):
            return obj.dict()
        elif hasattr(obj, 'model_dump') and callable(obj.model_dump):
            return obj.model_dump()
        
        # 其他情况使用默认处理
        try:
            return super().default(obj)
        except TypeError:
            # 如果仍然无法序列化，记录错误并返回字符串表示
            logger.warning(f"Cannot serialize object of type {type(obj)}: {obj}")
            return str(obj)