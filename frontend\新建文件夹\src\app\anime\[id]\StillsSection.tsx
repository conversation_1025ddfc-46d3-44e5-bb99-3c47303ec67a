'use client';

import { useRef, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface StillsSectionProps {
  fanart?: string | string[];
}

export default function StillsSection({ fanart }: StillsSectionProps) {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const lightboxRef = useRef<HTMLDivElement | null>(null);
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);

  // 处理fanart数据，确保总是返回数组
  const getFanartImages = (): string[] => {
    if (!fanart) return [];
    if (typeof fanart === 'string') {
      // 如果是字符串，可能是JSON数组或逗号分隔
      try {
        const parsed = JSON.parse(fanart);
        return Array.isArray(parsed) ? parsed.filter(url => url && url.trim()) : [fanart];
      } catch {
        // 不是JSON，尝试逗号分割
        if (fanart.includes(',')) {
          return fanart.split(',').map(url => url.trim()).filter(url => url);
        }
        return [fanart];
      }
    }
    return Array.isArray(fanart) ? fanart.filter(url => url && url.trim()) : [];
  };

  const images = getFanartImages();

  if (images.length === 0) return null;

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const openLightbox = (index: number) => {
    setCurrentImageIndex(index);
    setLightboxOpen(true);
  };

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -200, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 200, behavior: 'smooth' });
    }
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">
            剧照 ({images.length}张)
          </h3>
          {images.length > 4 && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={scrollLeft}
                className="h-8 w-8 p-0"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={scrollRight}
                className="h-8 w-8 p-0"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>
        
        {/* 水平滚动显示剧照 */}
        <div 
          ref={scrollContainerRef}
          className="flex gap-3 overflow-x-auto scrollbar-hide pb-2"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {images.map((image, index) => (
            <div
              key={index}
              className="flex-shrink-0 w-32 aspect-[4/3] overflow-hidden rounded-lg cursor-pointer group"
              onClick={() => openLightbox(index)}
            >
              <img
                src={image || ''}
                alt={`剧照 ${index + 1}`}
                className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
              />
            </div>
          ))}
        </div>

        {/* Lightbox - 提高z-index防止被视频遮挡 */}
        {lightboxOpen && (
          <div
            className="fixed inset-0 bg-black/95 flex items-center justify-center"
            style={{ zIndex: 9999 }} // 设置很高的z-index
            onClick={() => setLightboxOpen(false)}
          >
            <div
              className="relative max-w-[95vw] max-h-[95vh]"
              onClick={(e) => e.stopPropagation()}
              ref={lightboxRef}
            >
              <img
                src={images[currentImageIndex] || ''}
                alt={`剧照 ${currentImageIndex + 1}`}
                className="max-w-[95vw] max-h-[85vh] object-contain select-none"
                draggable={false}
              />

              {/* Lightbox 控制按钮 */}
              <button
                aria-label="关闭"
                className="absolute top-4 right-4 text-white/90 hover:text-white text-3xl font-light bg-black/50 rounded-full w-12 h-12 flex items-center justify-center"
                onClick={() => setLightboxOpen(false)}
              >
                ×
              </button>

              {/* Lightbox 图片导航 */}
              {images.length > 1 && (
                <>
                  <button
                    aria-label="上一张"
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/90 hover:text-white bg-black/50 rounded-full w-12 h-12 flex items-center justify-center"
                    onClick={prevImage}
                  >
                    <ChevronLeft className="w-6 h-6" />
                  </button>
                  <button
                    aria-label="下一张"
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/90 hover:text-white bg-black/50 rounded-full w-12 h-12 flex items-center justify-center"
                    onClick={nextImage}
                  >
                    <ChevronRight className="w-6 h-6" />
                  </button>
                </>
              )}

              {/* 图片计数器 */}
              {images.length > 1 && (
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white/90 bg-black/50 rounded-full px-4 py-2">
                  {currentImageIndex + 1} / {images.length}
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

<style jsx>{`
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
`}</style>


