import requests
import re
import os
import json
from bs4 import BeautifulSoup
from lxml import etree
import pymysql
import time
from datetime import datetime
from urllib.parse import urljoin
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
import threading

class IkanmhCrawler:
    def __init__(self, config_path='config.json'):
        """初始化爬虫，加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
        
        self.headers = self.config['crawler']['headers']
        self.base_url = self.config['crawler']['base_url']
        
        # 多线程配置
        self.max_workers = self.config.get('threading', {}).get('max_workers', 5)
        self.image_download_workers = self.config.get('threading', {}).get('image_workers', 10)
        
        # 线程锁
        self.db_lock = Lock()
        self.print_lock = Lock()
        
        print(f"爬虫初始化完成，目标网站: {self.base_url}")
        print(f"多线程配置: 主线程池={self.max_workers}, 图片下载线程池={self.image_download_workers}")
    
    def thread_safe_print(self, message):
        """线程安全的打印"""
        with self.print_lock:
            print(f"[{threading.current_thread().name}] {message}")
    
    def get_database_connection(self):
        """获取数据库连接"""
        db_config = self.config['database']
        return pymysql.connect(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['user'],
            password=db_config['password'],
            database=db_config['database'],
            charset='utf8mb4'
        )
    
    def manga_exists(self, title):
        """检查漫画是否已存在"""
        try:
            conn = self.get_database_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM mangas WHERE title = %s", (title,))
            result = cursor.fetchone()
            return result is not None
        except Exception as error:
            print(f"数据库查询错误: {error}")
            return False
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()
    
    def chapter_exists(self, manga_id, chapter_number, chapter_title=None):
        """检查章节是否已存在 - 改进版，同时检查章节号和标题"""
        try:
            conn = self.get_database_connection()
            cursor = conn.cursor()
            
            # 同时检查chapter_number和title，避免误判
            if chapter_title:
                cursor.execute("""
                    SELECT id FROM manga_chapters 
                    WHERE manga_id = %s AND (chapter_number = %s OR title = %s)
                """, (manga_id, str(chapter_number), chapter_title))
            else:
                cursor.execute("""
                    SELECT id FROM manga_chapters 
                    WHERE manga_id = %s AND chapter_number = %s
                """, (manga_id, str(chapter_number)))
                
            result = cursor.fetchone()
            return result is not None
        except Exception as error:
            print(f"数据库查询错误: {error}")
            return False
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()
    
    def make_request_with_retry(self, url, retries=3, delay=1):
        """发起请求并重试"""
        for attempt in range(retries):
            try:
                response = requests.get(url, headers=self.headers, timeout=30)
                if response.status_code == 200:
                    response.encoding = 'utf-8'
                    return response
                else:
                    print(f"请求失败，状态码：{response.status_code}")
            except Exception as e:
                print(f"请求异常: {e}")
            
            if attempt < retries - 1:
                time.sleep(delay * (2 ** attempt))
        
        print("达到最大重试次数，请求失败")
        return None
    
    def extract_chapter_number(self, chapter_title, fallback_index=None):
        """从章节标题中提取章节号，支持小数"""
        # 常见的章节标题格式：
        # "第1话", "第1.5话", "第10话", "1话", "1.5话", "Episode 1", "Ch.1"
        
        # 提取数字（包括小数）- 更精确的模式匹配
        number_patterns = [
            r'第(\d+(?:\.\d+)?)话',           # 第1话, 第1.5话
            r'第(\d+(?:\.\d+)?)章',           # 第1章, 第1.5章  
            r'^(\d+(?:\.\d+)?)话',            # 1话, 1.5话 (开头)
            r'^(\d+(?:\.\d+)?)章',            # 1章, 1.5章 (开头)
            r'[Ee]pisode\s*(\d+(?:\.\d+)?)',  # Episode 1, episode 1.5
            r'[Cc]h\.?\s*(\d+(?:\.\d+)?)',    # Ch.1, ch 1.5
            r'(\d+(?:\.\d+)?)话',             # X话 (任意位置)
            r'(\d+(?:\.\d+)?)章',             # X章 (任意位置)
        ]
        
        for pattern in number_patterns:
            match = re.search(pattern, chapter_title, re.IGNORECASE)
            if match:
                try:
                    chapter_num = float(match.group(1))
                    print(f"从标题 '{chapter_title}' 提取到章节号: {chapter_num}")
                    return chapter_num
                except ValueError:
                    continue
        
        # 如果都无法匹配，使用fallback_index
        print(f"无法从标题 '{chapter_title}' 提取章节号，使用fallback: {fallback_index}")
        return fallback_index
    
    def download_image(self, image_url, local_path):
        """下载图片"""
        try:
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            response = self.make_request_with_retry(image_url)
            if response and response.status_code == 200:
                with open(local_path, 'wb') as f:
                    f.write(response.content)
                print(f"图片下载成功: {os.path.basename(local_path)}")
                return True
            return False
        except Exception as e:
            print(f"下载图片失败: {e}")
            return False
    
    def download_cover(self, manga_title, cover_url):
        """下载漫画封面"""
        if not cover_url:
            print("封面URL为空，跳过下载")
            return None
            
        safe_title = re.sub(r'[<>:"/\\|?*]', '_', manga_title)
        cover_folder = os.path.join(self.config['download']['base_path'], safe_title)
        cover_path = os.path.join(cover_folder, 'cover.jpg')
        
        # 如果封面已存在，跳过下载
        if os.path.exists(cover_path):
            print(f"封面已存在: {cover_path}")
            # 生成媒体服务器URL
            relative_path = os.path.relpath(cover_path, self.config['download']['base_path'])
            relative_path = relative_path.replace('\\', self.config['media_server']['path_separator'])
            return f"{self.config['media_server']['base_url']}/{relative_path}"
        
        try:
            os.makedirs(cover_folder, exist_ok=True)
            response = self.make_request_with_retry(cover_url)
            if response and response.status_code == 200:
                with open(cover_path, 'wb') as f:
                    f.write(response.content)
                print(f"封面下载成功: {cover_path}")
                
                # 生成媒体服务器URL
                relative_path = os.path.relpath(cover_path, self.config['download']['base_path'])
                relative_path = relative_path.replace('\\', self.config['media_server']['path_separator'])
                return f"{self.config['media_server']['base_url']}/{relative_path}"
            else:
                print(f"封面下载失败，状态码: {response.status_code if response else 'None'}")
                return None
        except Exception as e:
            print(f"下载封面失败: {e}")
            return None
    
    def create_download_folder(self, manga_title, chapter_number):
        """创建下载文件夹"""
        safe_title = re.sub(r'[<>:"/\\|?*]', '_', manga_title)
        
        # 处理小数章节号，如1.5话
        if isinstance(chapter_number, float) and chapter_number.is_integer():
            chapter_str = f"第{int(chapter_number)}话"
        else:
            chapter_str = f"第{chapter_number}话"
        
        # 使用新的文件夹结构：漫画名/章节
        folder_structure = self.config['download']['folder_structure']
        folder_path = os.path.join(
            self.config['download']['base_path'],
            folder_structure.format(
                manga_title=safe_title,
                chapter_number=chapter_str
            )
        )
        
        return folder_path
    
    def insert_manga(self, manga_data):
        """插入漫画基本信息到数据库"""
        try:
            conn = self.get_database_connection()
            cursor = conn.cursor()
            
            # 插入漫画信息，包含category_id字段
            manga_sql = """
            INSERT INTO mangas (title, title_original, author, artist, description, cover, 
                              region_code, category_id, status, manga_type, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
            """
            
            cursor.execute(manga_sql, (
                manga_data['title'],
                manga_data.get('title_original', ''),  # 添加原始标题字段
                manga_data.get('author', ''),  # 如果为空则插入空字符串
                manga_data.get('artist', ''),  # 画师留空
                manga_data.get('description', ''),
                manga_data.get('cover', ''),
                manga_data.get('region_code', 'kr'),
                manga_data.get('category_id', 3),
                'completed',
                'serial'
            ))
            
            manga_id = cursor.lastrowid
            conn.commit()
            
            print(f"漫画 '{manga_data['title']}' 插入成功，ID: {manga_id}")
            return manga_id
            
        except Exception as error:
            print(f"插入漫画失败: {error}")
            if 'conn' in locals():
                conn.rollback()
            return None
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()
    
    def get_or_create_tag(self, tag_name):
        """获取或创建标签"""
        try:
            conn = self.get_database_connection()
            cursor = conn.cursor()
            
            # 先查找标签是否存在
            cursor.execute("SELECT id FROM tags WHERE name = %s", (tag_name,))
            result = cursor.fetchone()
            
            if result:
                return result[0]  # 返回存在的标签ID
            else:
                # 创建新标签（移除updated_at字段）
                cursor.execute("INSERT INTO tags (name, created_at) VALUES (%s, NOW())", 
                              (tag_name,))
                conn.commit()
                return cursor.lastrowid
                
        except Exception as error:
            print(f"标签操作错误: {error}")
            if 'conn' in locals():
                conn.rollback()
            return None
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()
    
    def add_manga_tags(self, manga_id, tag_names):
        """为漫画添加标签"""
        if not tag_names:
            return
            
        try:
            conn = self.get_database_connection()
            cursor = conn.cursor()
            
            for tag_name in tag_names:
                # 获取或创建标签
                tag_id = self.get_or_create_tag(tag_name)
                if not tag_id:
                    continue
                
                # 检查关联是否已存在
                cursor.execute("SELECT id FROM manga_tags WHERE manga_id = %s AND tag_id = %s", 
                              (manga_id, tag_id))
                if not cursor.fetchone():
                    # 添加漫画标签关联（移除updated_at字段）
                    cursor.execute("INSERT INTO manga_tags (manga_id, tag_id, created_at) VALUES (%s, %s, NOW())", 
                                  (manga_id, tag_id))
            
            conn.commit()
            print(f"成功为漫画 {manga_id} 添加 {len(tag_names)} 个标签")
            
        except Exception as error:
            print(f"添加漫画标签错误: {error}")
            if 'conn' in locals():
                conn.rollback()
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()
    
    def get_manga_id_by_title(self, title):
        """根据标题获取漫画ID"""
        try:
            conn = self.get_database_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM mangas WHERE title = %s", (title,))
            result = cursor.fetchone()
            return result[0] if result else None
        except Exception as error:
            print(f"查询漫画ID失败: {error}")
            return None
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()
    
    def insert_chapter(self, manga_id, chapter_data, pages_data):
        """插入章节和页面信息"""
        try:
            conn = self.get_database_connection()
            cursor = conn.cursor()
            
            # 插入章节信息
            chapter_sql = """
            INSERT INTO manga_chapters (manga_id, title, chapter_number, page_count, 
                                      status, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
            """
            
            cursor.execute(chapter_sql, (
                manga_id,
                chapter_data['title'],
                str(chapter_data['chapter_number']),  # 转换为字符串存储，支持小数
                len(pages_data),
                'published'
            ))
            
            chapter_id = cursor.lastrowid
            
            # 插入页面信息
            for page_data in pages_data:
                page_sql = """
                INSERT INTO manga_pages (chapter_id, page_number, image_url, created_at)
                VALUES (%s, %s, %s, NOW())
                """
                cursor.execute(page_sql, (
                    chapter_id,
                    page_data['page_number'],
                    page_data['image_url']
                ))
            
            conn.commit()
            print(f"章节 '{chapter_data['title']}' 插入成功，包含 {len(pages_data)} 页")
            return chapter_id
            
        except Exception as error:
            print(f"插入章节失败: {error}")
            if 'conn' in locals():
                conn.rollback()
            return None
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()
    
    def parse_manga_list(self, page_num):
        """解析漫画列表页"""
        url = self.config['crawler']['booklist_url'].format(page_num)
        print(f"正在爬取第 {page_num} 页漫画列表...")
        
        response = self.make_request_with_retry(url)
        if not response:
            return []
        
        manga_list = []
        try:
            soup = etree.HTML(response.text)
            # 根据ikanmh.top网站结构解析漫画链接
            manga_items = soup.xpath('//div[@class="mh-item"]/div[@class="mh-item-detali"]/h2/a/@href')
            
            for item in manga_items:
                if '/book/' in item:
                    manga_url = urljoin(self.base_url, item)
                    manga_list.append(manga_url)
            
            # 倒序处理漫画列表，最后出现的先处理（如642, 641, 1, 2, 3...）
            manga_list = list(reversed(manga_list))
            
            print(f"第 {page_num} 页找到 {len(manga_list)} 部漫画")
            
        except Exception as e:
            print(f"解析第 {page_num} 页失败: {e}")
        
        return manga_list
    
    def parse_manga_detail(self, manga_url):
        """解析漫画详情页"""
        print(f"正在获取漫画详情: {manga_url}")
        
        response = self.make_request_with_retry(manga_url)
        if not response:
            return None, []
        
        try:
            soup = etree.HTML(response.text)
            
            # 提取漫画基本信息（根据实际HTML结构）
            # 标题从 h1 标签中提取
            title_elements = soup.xpath('//div[@class="info"]/h1/text()')
            title = title_elements[0].strip() if title_elements else "Unknown"
            
            # 提取作者信息 - 从"作者："后的文本提取，如果没有则留空
            author_elements = soup.xpath('//p[@class="subtitle"][contains(text(), "作者：")]/text()')
            if author_elements:
                author_text = author_elements[0]
                author = author_text.replace('作者：', '').strip()
            else:
                author = ""  # 留空而不是Unknown
            
            # 提取原始标题（别名）
            title_original_elements = soup.xpath('//p[@class="subtitle"][contains(text(), "别名：")]/text()')
            if title_original_elements:
                title_original_text = title_original_elements[0]
                title_original = title_original_text.replace('别名：', '').strip()
            else:
                title_original = ""  # 如果没有别名则留空
            
            # 提取封面图片（根据实际HTML结构）
            cover_elements = soup.xpath('//div[@class="cover"]/img/@src')
            cover = cover_elements[0] if cover_elements else ""
            # 如果是相对路径，转换为绝对路径
            if cover and not cover.startswith('http'):
                cover = urljoin(self.base_url, cover)
            
            # 提取简介/描述（从实际的content段落）
            description_elements = soup.xpath('//p[@class="content"]/text()')
            if description_elements:
                description = description_elements[0].strip()
            else:
                description = title  # 使用标题作为描述
            
            # 清理描述内容
            if description:
                description = ' '.join(description.split())
                if len(description) > 1000:
                    description = description[:1000] + '...'
            
            print(f"提取到简介: {description[:50]}..." if len(description) > 50 else f"提取到简介: {description}")
            print(f"提取到封面: {cover}")
            
            # 提取标签信息（根据实际HTML结构）
            tag_elements = []
            # 从标签区域提取 - 根据实际HTML结构
            tag_links = soup.xpath('//span[@class="block"][contains(text(), "标签：")]//a/text()')
            if tag_links:
                tag_elements.extend([tag.strip() for tag in tag_links if tag.strip()])
            
            # 清理和去重标签
            tags = list(set([tag.strip() for tag in tag_elements if tag.strip() and len(tag.strip()) > 0 and not tag.strip() in ['更多', '全部', '首页', '连载', '完结']]))
            print(f"找到标签: {tags}")
            print(f"标签数量: {len(tags)}")
            
            manga_data = {
                'title': title,
                'title_original': title_original,  # 添加原始标题
                'author': author,
                'description': description,
                'cover': cover,
                'tags': tags,
                'category_id': 3,
                'status': 'completed',
                'region_code': 'kr'  # ikanmh.top主要是韩漫
            }
            
            # 获取章节列表（修复版：正确处理章节顺序和编号）
            chapter_list = []
            chapter_elements = soup.xpath('//div[@id="chapterlistload"]/ul/li/a')
            
            print(f"找到 {len(chapter_elements)} 个章节元素")
            
            # 先收集所有章节信息
            temp_chapters = []
            
            for i, chapter_elem in enumerate(chapter_elements):
                chapter_href = chapter_elem.get('href')
                chapter_title = chapter_elem.text or chapter_elem.get('title', f'章节{i+1}')
                
                if chapter_href and '/chapter/' in chapter_href:
                    chapter_url = urljoin(self.base_url, chapter_href)
                    
                    # 尝试从章节标题中提取章节号
                    extracted_number = self.extract_chapter_number(chapter_title, None)
                    
                    temp_chapters.append({
                        'title': chapter_title.strip(),
                        'extracted_number': extracted_number,
                        'original_index': i,  # 保存原始顺序
                        'url': chapter_url
                    })
            
            # 根据网站章节的实际排列来决定编号策略
            # 检查是否有可提取的章节号
            chapters_with_numbers = [ch for ch in temp_chapters if ch['extracted_number'] is not None]
            
            if len(chapters_with_numbers) > len(temp_chapters) * 0.5:
                # 如果超过一半的章节都有可提取的编号，优先使用提取的编号
                print("使用提取的章节号作为主要编号方式")
                
                for chapter in temp_chapters:
                    if chapter['extracted_number'] is not None:
                        chapter['chapter_number'] = chapter['extracted_number']
                    else:
                        # 对于无法提取编号的章节，使用其在列表中的位置
                        chapter['chapter_number'] = chapter['original_index'] + 1
                        print(f"章节 '{chapter['title']}' 无法提取编号，使用位置编号: {chapter['chapter_number']}")
            else:
                # 如果大部分章节都没有明确编号，使用顺序编号
                print("大部分章节无明确编号，使用正序编号")
                
                # 直接按章节在网页中的顺序分配编号
                for i, chapter in enumerate(temp_chapters):
                    chapter['chapter_number'] = i + 1
                    print(f"章节 '{chapter['title']}' 分配正序编号: {chapter['chapter_number']}")
            
            # 按章节号正序排列（从第1话开始）
            chapter_list = sorted([{
                'title': ch['title'],
                'chapter_number': ch['chapter_number'],
                'url': ch['url']
            } for ch in temp_chapters], key=lambda x: float(x['chapter_number']))
            
            print(f"成功获取漫画: {title}, 章节数: {len(chapter_list)}")
            return manga_data, chapter_list
            
        except Exception as e:
            print(f"解析漫画详情失败: {e}")
            return None, []
    
    def parse_chapter_pages(self, chapter_url, chapter_number):
        """解析章节页面"""
        print(f"正在获取第{chapter_number}话页面...")
        
        response = self.make_request_with_retry(chapter_url)
        if not response:
            return []
        
        pages_data = []
        try:
            soup = etree.HTML(response.text)
            
            # 提取图片链接（使用与hanman.py一致的方式）
            img_elements = soup.xpath('//div[@class="comicpage"]/div/img/@data-original')
            
            for i, img_src in enumerate(img_elements, 1):
                if img_src:
                    # 处理相对路径
                    if img_src.startswith('/'):
                        img_src = urljoin(self.base_url, img_src)
                    elif not img_src.startswith('http'):
                        img_src = urljoin(self.base_url, img_src)
                    
                    pages_data.append({
                        'page_number': i,
                        'image_url': img_src,
                        'local_filename': f"{i:03d}.jpg"
                    })
            
            print(f"第{chapter_number}话包含 {len(pages_data)} 页")
            
        except Exception as e:
            print(f"解析章节页面失败: {e}")
        
        return pages_data
    
    def download_chapter_images(self, manga_title, chapter_number, pages_data):
        """多线程下载章节所有图片"""
        folder_path = self.create_download_folder(manga_title, chapter_number)
        
        def download_single_image(page_data):
            """下载单张图片的工作函数"""
            local_path = os.path.join(folder_path, page_data['local_filename'])
            
            if self.download_image(page_data['image_url'], local_path):
                # 生成媒体服务器URL
                relative_path = os.path.relpath(local_path, self.config['download']['base_path'])
                relative_path = relative_path.replace('\\', self.config['media_server']['path_separator'])
                media_url = f"{self.config['media_server']['base_url']}/{relative_path}"
                
                return {
                    'page_number': page_data['page_number'],
                    'image_url': media_url
                }
            return None
        
        downloaded_pages = []
        
        # 使用线程池并发下载图片
        with ThreadPoolExecutor(max_workers=self.image_download_workers) as executor:
            # 提交所有下载任务
            future_to_page = {
                executor.submit(download_single_image, page_data): page_data
                for page_data in pages_data
            }
            
            # 收集结果
            for future in as_completed(future_to_page):
                page_data = future_to_page[future]
                try:
                    result = future.result()
                    if result:
                        downloaded_pages.append(result)
                except Exception as e:
                    self.thread_safe_print(f"下载图片失败 {page_data['local_filename']}: {e}")
        
        # 按页号排序
        downloaded_pages.sort(key=lambda x: x['page_number'])
        self.thread_safe_print(f"章节图片下载完成: {len(downloaded_pages)}/{len(pages_data)} 张")
        
        return downloaded_pages
    
    def process_single_chapter(self, manga_id, manga_title, chapter):
        """处理单个章节的工作函数"""
        try:
            if self.chapter_exists(manga_id, chapter['chapter_number'], chapter['title']):
                self.thread_safe_print(f"章节 '{chapter['title']}' (章节号: {chapter['chapter_number']}) 已存在，跳过")
                return True
            
            # 解析章节页面
            pages_data = self.parse_chapter_pages(chapter['url'], chapter['chapter_number'])
            if not pages_data:
                return False
            
            # 下载图片
            downloaded_pages = self.download_chapter_images(
                manga_title, 
                chapter['chapter_number'], 
                pages_data
            )
            
            if downloaded_pages:
                # 使用数据库锁进行线程安全的数据库操作
                with self.db_lock:
                    return self.insert_chapter(manga_id, chapter, downloaded_pages) is not None
            
            return False
        except Exception as e:
            self.thread_safe_print(f"处理章节失败 '{chapter['title']}': {e}")
            return False
    
    def process_single_manga(self, manga_url):
        """处理单个漫画的工作函数"""
        try:
            # 解析漫画详情
            manga_data, chapter_list = self.parse_manga_detail(manga_url)
            if not manga_data:
                return
            
            # 检查漫画是否已存在（数据库操作需要加锁）
            with self.db_lock:
                if self.manga_exists(manga_data['title']):
                    self.thread_safe_print(f"漫画 '{manga_data['title']}' 已存在，检查新章节...")
                    manga_id = self.get_manga_id_by_title(manga_data['title'])
                else:
                    # 下载封面
                    if manga_data['cover']:
                        cover_media_url = self.download_cover(manga_data['title'], manga_data['cover'])
                        if cover_media_url:
                            manga_data['cover'] = cover_media_url
                    
                    # 插入新漫画
                    manga_id = self.insert_manga(manga_data)
                    if not manga_id:
                        return
                    
                    # 添加标签关联
                    if manga_data.get('tags'):
                        self.add_manga_tags(manga_id, manga_data['tags'])
            
            # 多线程处理章节
            with ThreadPoolExecutor(max_workers=3) as chapter_executor:  # 限制章节并发数
                chapter_futures = {
                    chapter_executor.submit(
                        self.process_single_chapter, 
                        manga_id, 
                        manga_data['title'], 
                        chapter
                    ): chapter for chapter in chapter_list
                }
                
                # 等待所有章节处理完成
                completed_chapters = 0
                for future in as_completed(chapter_futures):
                    chapter = chapter_futures[future]
                    try:
                        success = future.result()
                        if success:
                            completed_chapters += 1
                    except Exception as e:
                        self.thread_safe_print(f"章节处理异常 '{chapter['title']}': {e}")
                
                self.thread_safe_print(f"漫画 '{manga_data['title']}' 处理完成: {completed_chapters}/{len(chapter_list)} 章节")
            
        except Exception as e:
            self.thread_safe_print(f"处理漫画失败: {e}")
    
    def crawl_manga(self):
        """主爬虫函数 - 多线程版本"""
        print("开始多线程爬取漫画...")
        
        # 从配置获取页面范围
        page_range = self.config['crawler']['page_range']
        start_page, end_page = page_range[0], page_range[1]
        
        # 处理页面范围，支持正序和倒序
        if start_page > end_page:
            page_range_list = list(range(start_page, end_page - 1, -1))
        else:
            page_range_list = list(range(start_page, end_page + 1))
        
        # 收集所有漫画URL
        all_manga_urls = []
        for page in page_range_list:
            manga_list = self.parse_manga_list(page)
            if not manga_list:
                print(f"第 {page} 页没有找到漫画，继续下一页")
                continue
            all_manga_urls.extend(manga_list)
            time.sleep(1)  # 页面间隔
        
        print(f"总共找到 {len(all_manga_urls)} 部漫画，开始多线程处理...")
        
        # 使用线程池并发处理漫画
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            manga_futures = {
                executor.submit(self.process_single_manga, manga_url): manga_url 
                for manga_url in all_manga_urls
            }
            
            completed_count = 0
            total_count = len(all_manga_urls)
            
            for future in as_completed(manga_futures):
                manga_url = manga_futures[future]
                try:
                    future.result()  # 获取结果，如果有异常会抛出
                    completed_count += 1
                    self.thread_safe_print(f"进度: {completed_count}/{total_count} 部漫画处理完成")
                except Exception as e:
                    self.thread_safe_print(f"处理漫画失败 {manga_url}: {e}")
                    completed_count += 1
        
        print(f"爬虫任务完成! 处理了 {completed_count} 部漫画")

def main():
    """主函数"""
    crawler = IkanmhCrawler()
    
    # 开始爬取：根据配置文件的page_range设置
    crawler.crawl_manga()
    
    print("爬虫任务完成!")

if __name__ == "__main__":
    main()