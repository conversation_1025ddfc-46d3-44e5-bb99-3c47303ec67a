#!/usr/bin/env python3
"""
测试生产环境多年份配置功能
"""

import sys
import os
sys.path.append('.')

from production_crawler import ProductionCrawler

def test_production_multi_year():
    """测试生产环境多年份配置"""
    print("Testing production multi-year configuration...")
    
    config_file = 'production_config.yml'
    
    if not os.path.exists(config_file):
        print(f"ERROR: Configuration file not found: {config_file}")
        return False
    
    try:
        # 创建生产环境爬虫实例
        crawler = ProductionCrawler(config_file)
        
        # 测试初始化（不实际运行爬取）
        print("Testing crawler initialization...")
        
        # 创建基础爬虫实例来检查配置
        from unified_crawler import UnifiedCrawler
        base_crawler = UnifiedCrawler(config_file)
        
        print(f"Target years: {base_crawler.TARGET_YEARS}")
        print(f"Target months: {base_crawler.TARGET_MONTHS}")
        
        # 验证多年份逻辑
        if hasattr(base_crawler, 'TARGET_YEARS'):
            print("✅ Multi-year support detected")
            
            total_combinations = len(base_crawler.TARGET_YEARS) * len(base_crawler.TARGET_MONTHS)
            print(f"Total combinations to process: {total_combinations}")
            
            # 显示前几个组合示例
            print("\nSample combinations to be processed:")
            count = 0
            for year in base_crawler.TARGET_YEARS[:3]:
                for month in base_crawler.TARGET_MONTHS[:3]:
                    count += 1
                    print(f"  {count}. {year}年{month:02d}月")
                    if count >= 6:
                        break
                if count >= 6:
                    break
            
            if total_combinations > 6:
                print(f"  ... (total {total_combinations} combinations)")
            
            return True
        else:
            print("❌ Multi-year support not found")
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        return False

def show_configuration_guide():
    """显示配置指南"""
    print("\n" + "="*60)
    print("Production Multi-Year Configuration Guide")
    print("="*60)
    
    print("\nTo configure multi-year crawling:")
    print("1. Edit production_config.yml")
    print("2. Update the date_filter section:")
    
    print("\nExample configurations:")
    
    print("\n# Small scale test (recommended first):")
    print("crawl:")
    print("  date_filter:")
    print("    year: [2004, 2005]")
    print("    month: [1, 2, 3]")
    print("# Total: 2 years × 3 months = 6 combinations")
    
    print("\n# Medium scale:")
    print("crawl:")
    print("  date_filter:")
    print("    year: [2003, 2004, 2005]")
    print("    month: [1,2,3,4,5,6,7,8,9,10,11,12]")
    print("# Total: 3 years × 12 months = 36 combinations")
    
    print("\n# Large scale (use with caution):")
    print("crawl:")
    print("  date_filter:")
    print("    year: [2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010]")
    print("    month: [1,2,3,4,5,6,7,8,9,10,11,12]")
    print("# Total: 11 years × 12 months = 132 combinations")
    
    print("\n3. Run the crawler:")
    print("   python production_crawler.py")
    
    print("\n⚠️  Recommendations:")
    print("- Start with small scale testing")
    print("- Monitor disk space and network usage")
    print("- Consider increasing request delays for large scale crawling")

if __name__ == "__main__":
    print("Production Multi-Year Configuration Test")
    print("="*60)
    
    success = test_production_multi_year()
    
    show_configuration_guide()
    
    print("\n" + "="*60)
    if success:
        print("✅ Production multi-year configuration is working correctly!")
        print("\nYou can now run multi-year crawling with:")
        print("python production_crawler.py")
    else:
        print("❌ Production multi-year configuration has issues!")
    print("="*60)