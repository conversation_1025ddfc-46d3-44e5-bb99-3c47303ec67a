#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hanime1.me 视频抓取和处理系统
主程序入口
"""

import os
import sys
import yaml
import logging
import argparse
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class HanimeProcessor:
    """Hanime1.me 视频处理器主类"""
    
    def __init__(self, config_path="config.yml"):
        """初始化处理器"""
        self.config_path = config_path
        self.config = None
        self.logger = None
        
        # 加载配置
        self.load_config()
        
        # 设置日志
        self.setup_logging()
        
        # 创建必要目录
        self.create_directories()
    
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            
            # 设置默认日期（上一个月）
            default_year, default_month = self._get_default_date()
            crawl_config = self.config.setdefault('crawl', {})
            date_filter = crawl_config.setdefault('date_filter', {})
            
            if date_filter.get('year') is None:
                date_filter['year'] = default_year
            if date_filter.get('month') is None:
                date_filter['month'] = default_month
            
            print(f"✓ 配置文件加载成功: {self.config_path}")
            print(f"✓ 目标日期: {date_filter['year']}年{date_filter['month']}月")
            
        except FileNotFoundError:
            print(f"✗ 配置文件不存在: {self.config_path}")
            sys.exit(1)
        except yaml.YAMLError as e:
            print(f"✗ 配置文件格式错误: {e}")
            sys.exit(1)
    
    def _get_default_date(self):
        """获取默认的年月（上一个月）- 静态方法"""
        today = datetime.now()
        if today.month == 1:
            # 如果当前是1月，则上一个月是去年12月
            return today.year - 1, 12
        else:
            return today.year, today.month - 1
    
    def setup_logging(self):
        """设置日志系统"""
        log_config = self.config.get('logging', {})
        
        # 创建logger
        self.logger = logging.getLogger('HanimeProcessor')
        self.logger.setLevel(getattr(logging, log_config.get('level', 'INFO')))
        
        # 创建formatter
        formatter = logging.Formatter(
            log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器
        if log_config.get('file'):
            file_handler = logging.FileHandler(
                log_config['file'], 
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
        
        self.logger.info("日志系统初始化完成")
    
    def create_directories(self):
        """创建必要的目录"""
        directories = [
            self.config['download']['download_dir'],
            self.config['download']['temp_dir']
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"创建目录: {directory}")
    
    def get_download_path(self, year=None, month=None):
        """获取下载路径"""
        base_dir = self.config['download']['download_dir']
        
        if self.config['download'].get('organize_by_date', True):
            if year and month:
                return os.path.join(base_dir, str(year), f"{month:02d}")
            else:
                # 使用配置中的年月
                config_year = self.config['crawl']['date_filter']['year']
                config_month = self.config['crawl']['date_filter']['month']
                return os.path.join(base_dir, str(config_year), f"{config_month:02d}")
        
        return base_dir
    
    def _download_image_to_path(self, image_url, target_path):
        """下载图片到指定路径"""
        try:
            import requests
            
            self.logger.info(f"开始下载图片: {image_url}")
            
            # 下载图片
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
            response = requests.get(image_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # 保存图片
            with open(target_path, 'wb') as f:
                f.write(response.content)
            
            self.logger.info(f"图片已保存为: {target_path}")
            return str(target_path)
            
        except Exception as e:
            self.logger.error(f"下载图片失败: {e}")
            return False
    
    def run_crawler(self):
        """运行爬虫"""
        self.logger.info("开始运行爬虫...")
        
        try:
            # 直接运行 craw.py 脚本
            import subprocess
            import sys
            
            result = subprocess.run([sys.executable, "craw.py"], 
                                  capture_output=True, 
                                  text=True,
                                  cwd=str(Path(__file__).parent))
            
            if result.returncode == 0:
                self.logger.info("爬虫运行完成")
                if result.stdout:
                    self.logger.info(f"输出:\n{result.stdout}")
                return True
            else:
                self.logger.error(f"爬虫运行失败，返回码: {result.returncode}")
                if result.stderr:
                    self.logger.error(f"错误信息:\n{result.stderr}")
                return False
            
        except Exception as e:
            self.logger.error(f"爬虫运行失败: {e}")
            return False
    
    def run_renamer(self):
        """运行重命名器"""
        self.logger.info("开始运行重命名器...")
        
        try:
            from rename import HanimeRenamer
            
            renamer = HanimeRenamer(self.config, self.logger)
            renamer.rename_all()
            
        except ImportError as e:
            self.logger.error(f"无法导入重命名模块: {e}")
            return False
        except Exception as e:
            self.logger.error(f"重命名失败: {e}")
            return False
        
        return True
    
    def scrape(self, directory=None):
        """刮削视频信息并生成NFO文件"""
        try:
            if not hasattr(self, 'scraper') or self.scraper is None:
                from hanimecraw import HanimeScraper
                self.scraper = HanimeScraper(self.logger)
            
            if directory:
                target_dir = Path(directory)
            else:
                downloads_dir = Path(self.config['download']['download_dir'])
                current_year = self.config['crawl']['date_filter']['year']
                current_month = self.config['crawl']['date_filter']['month']
                target_dir = downloads_dir / str(current_year) / f"{current_month:02d}"
            
            self.logger.info(f"开始刮削目录: {target_dir}")
            self.logger.info("使用hanime1.me在线搜索进行刮削")
            
            # 查找目录中的所有视频文件
            if not target_dir.exists():
                self.logger.warning(f"目标目录不存在: {target_dir}")
                return False
            
            video_files = list(target_dir.glob("*.mp4"))
            if not video_files:
                self.logger.warning(f"目录中没有找到视频文件: {target_dir}")
                return False
            
            success_count = 0
            total_files = len(video_files)
            
            for i, video_file in enumerate(video_files, 1):
                # 从文件名提取标题（去掉.mp4扩展名）
                title = video_file.stem
                self.logger.info(f"[{i}/{total_files}] 刮削: {title}")
                
                try:
                    # 刮削视频信息
                    info = self.scraper.scrape_video_file(title)
                    
                    if info:
                        # 生成NFO文件（与视频文件同目录）
                        nfo_content = self.scraper.generate_nfo(info)
                        nfo_path = video_file.with_suffix('.nfo')
                        
                        with open(nfo_path, 'w', encoding='utf-8') as f:
                            f.write(nfo_content)
                        
                        self.logger.info(f"✓ NFO文件已生成: {nfo_path.name}")
                        
                        # 下载fanart图片（与视频文件同目录）
                        image_url = info.get('image_url')
                        if image_url:
                            # 设置图片保存路径为视频文件同目录
                            fanart_path = video_file.parent / f"{video_file.stem}-fanart.jpg"
                            fanart_filename = self._download_image_to_path(image_url, fanart_path)
                            if fanart_filename:
                                self.logger.info(f"✓ 图片已下载: {fanart_path.name}")
                        
                        success_count += 1
                    else:
                        self.logger.warning(f"✗ 未能获取视频信息: {title}")
                        
                except Exception as e:
                    self.logger.error(f"✗ 刮削失败 {title}: {e}")
            
            if success_count > 0:
                self.logger.info(f"✅ 刮削完成: 成功 {success_count}/{total_files} 个文件")
            else:
                self.logger.warning("⚠ 所有文件刮削失败")
            
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"刮削过程出错: {e}")
            return False
    
    def run_all(self):
        """运行完整流程"""
        self.logger.info("=" * 50)
        self.logger.info("开始完整处理流程")
        self.logger.info("=" * 50)
        
        steps = [
            ("抓取下载", self.run_crawler),
            ("重命名", self.run_renamer),
            ("刮削", self.scrape)
        ]
        
        for step_name, step_func in steps:
            self.logger.info(f"开始执行步骤: {step_name}")
            if step_func():
                self.logger.info(f"✓ 步骤完成: {step_name}")
            else:
                self.logger.error(f"✗ 步骤失败: {step_name}")
                return False
        
        self.logger.info("=" * 50)
        self.logger.info("完整流程执行完成")
        self.logger.info("=" * 50)
        return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Hanime1.me 视频抓取和处理系统')
    parser.add_argument('-c', '--config', default='config.yml', help='配置文件路径')
    parser.add_argument('-m', '--mode', choices=['all', 'crawl', 'rename', 'scrape'], 
                        default='all', help='运行模式')
    parser.add_argument('-y', '--year', type=int, help='指定年份')
    parser.add_argument('-M', '--month', type=int, help='指定月份')
    
    args = parser.parse_args()
    
    # 创建处理器
    processor = HanimeProcessor(args.config)
    
    # 如果指定了年月，更新配置
    if args.year:
        processor.config['crawl']['date_filter']['year'] = args.year
    if args.month:
        processor.config['crawl']['date_filter']['month'] = args.month
    
    # 运行指定模式
    if args.mode == 'all':
        success = processor.run_all()
    elif args.mode == 'crawl':
        success = processor.run_crawler()
    elif args.mode == 'rename':
        success = processor.run_renamer()
    elif args.mode == 'scrape':
        success = processor.scrape()
    else:
        processor.logger.error(f"未知模式: {args.mode}")
        success = False
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
