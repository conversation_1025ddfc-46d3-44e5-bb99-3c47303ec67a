#!/usr/bin/env python3
"""
Comprehensive unit tests for user password management endpoints.

Tests cover:
- Password change validation
- Security requirements
- Error handling
- Edge cases
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, MagicMock
from fastapi import HTTPException
from sqlalchemy.orm import Session
from app.api.user import change_password
from app.models import User
from app.schemas.user import PasswordChangeRequest, PasswordChangeResponse
from app.core.security import get_password_hash, verify_password
import bcrypt

class TestPasswordChangeEndpoint:
    """Test suite for password change functionality"""

    def setup_method(self):
        """Set up test fixtures before each test"""
        self.mock_db = Mock(spec=Session)
        self.mock_user = Mock(spec=User)
        self.mock_user.id = 1
        self.mock_user.username = "testuser"
        self.mock_user.password_hash = get_password_hash("oldpassword123")

    def test_password_change_success(self):
        """Test successful password change with valid data"""
        # Arrange
        password_data = PasswordChangeRequest(
            old_password="oldpassword123",
            new_password="newpassword456",
            confirm_password="newpassword456"
        )
        
        # Mock database operations
        self.mock_db.commit = Mock()
        self.mock_db.refresh = Mock()
        
        # Act
        with patch('app.api.user.verify_password', return_value=True) as mock_verify:
            with patch('app.api.user.get_password_hash', return_value='hashed_new_password') as mock_hash:
                result = change_password(password_data, self.mock_user, self.mock_db)
        
        # Assert
        assert isinstance(result, PasswordChangeResponse)
        assert result.success == True
        assert result.message == "密码修改成功"
        mock_verify.assert_called_once_with("oldpassword123", self.mock_user.password_hash)
        mock_hash.assert_called_once_with("newpassword456")
        assert self.mock_user.password_hash == 'hashed_new_password'
        self.mock_db.commit.assert_called_once()
        self.mock_db.refresh.assert_called_once_with(self.mock_user)

    def test_password_change_wrong_old_password(self):
        """Test password change with incorrect old password"""
        # Arrange
        password_data = PasswordChangeRequest(
            old_password="wrongpassword",
            new_password="newpassword456",
            confirm_password="newpassword456"
        )
        
        # Act & Assert
        with patch('app.api.user.verify_password', return_value=False):
            with pytest.raises(HTTPException) as exc_info:
                change_password(password_data, self.mock_user, self.mock_db)
            
            assert exc_info.value.status_code == 400
            assert exc_info.value.detail == "原密码错误"

    def test_password_change_same_old_and_new_password(self):
        """Test password change when new password is same as old password"""
        # Arrange
        password_data = PasswordChangeRequest(
            old_password="samepassword123",
            new_password="samepassword123",
            confirm_password="samepassword123"
        )
        
        # Act
        with patch('app.api.user.verify_password', return_value=True):
            with patch('app.api.user.get_password_hash', return_value='hashed_same_password') as mock_hash:
                result = change_password(password_data, self.mock_user, self.mock_db)
        
        # Assert - should still work but is not recommended
        assert result.success == True
        mock_hash.assert_called_once_with("samepassword123")

    def test_password_change_database_error(self):
        """Test password change when database operation fails"""
        # Arrange
        password_data = PasswordChangeRequest(
            old_password="oldpassword123",
            new_password="newpassword456",
            confirm_password="newpassword456"
        )
        
        # Mock database error
        self.mock_db.commit.side_effect = Exception("Database error")
        
        # Act & Assert
        with patch('app.api.user.verify_password', return_value=True):
            with patch('app.api.user.get_password_hash', return_value='hashed_new_password'):
                with pytest.raises(Exception) as exc_info:
                    change_password(password_data, self.mock_user, self.mock_db)
                
                assert str(exc_info.value) == "Database error"

class TestPasswordValidation:
    """Test suite for password validation logic"""

    def test_password_validation_schema_success(self):
        """Test valid password passes schema validation"""
        # Arrange & Act
        password_data = PasswordChangeRequest(
            old_password="oldpass123",
            new_password="newpass456",
            confirm_password="newpass456"
        )
        
        # Assert
        assert password_data.old_password == "oldpass123"
        assert password_data.new_password == "newpass456"
        assert password_data.confirm_password == "newpass456"

    def test_password_validation_too_short(self):
        """Test password validation fails for short password"""
        with pytest.raises(ValueError, match="密码长度至少为6位"):
            PasswordChangeRequest(
                old_password="old123",
                new_password="123",  # Too short
                confirm_password="123"
            )

    def test_password_validation_too_long(self):
        """Test password validation fails for overly long password"""
        long_password = "a" * 129  # 129 characters, exceeds limit
        with pytest.raises(ValueError, match="密码长度不能超过128位"):
            PasswordChangeRequest(
                old_password="old123",
                new_password=long_password,
                confirm_password=long_password
            )

    def test_password_validation_no_letter(self):
        """Test password validation fails when no letters present"""
        with pytest.raises(ValueError, match="密码必须包含至少一个字母"):
            PasswordChangeRequest(
                old_password="old123",
                new_password="123456",  # Only numbers
                confirm_password="123456"
            )

    def test_password_validation_no_digit(self):
        """Test password validation fails when no digits present"""
        with pytest.raises(ValueError, match="密码必须包含至少一个数字"):
            PasswordChangeRequest(
                old_password="old123",
                new_password="abcdef",  # Only letters
                confirm_password="abcdef"
            )

    def test_password_validation_mismatch(self):
        """Test password validation fails when passwords don't match"""
        with pytest.raises(ValueError, match="确认密码与新密码不一致"):
            PasswordChangeRequest(
                old_password="old123",
                new_password="new123",
                confirm_password="different456"
            )

    def test_password_validation_complex_valid(self):
        """Test complex but valid password passes validation"""
        password_data = PasswordChangeRequest(
            old_password="oldComplex123!",
            new_password="newComplex456@",
            confirm_password="newComplex456@"
        )
        
        assert password_data.new_password == "newComplex456@"

class TestPasswordSecurity:
    """Test suite for password security features"""

    def test_password_hashing_consistency(self):
        """Test that password hashing is consistent and secure"""
        password = "test123"
        
        # Hash the same password multiple times
        hash1 = get_password_hash(password)
        hash2 = get_password_hash(password)
        
        # Hashes should be different (due to salt)
        assert hash1 != hash2
        
        # But both should verify correctly
        assert verify_password(password, hash1)
        assert verify_password(password, hash2)

    def test_password_verification_wrong_password(self):
        """Test that wrong password fails verification"""
        correct_password = "correct123"
        wrong_password = "wrong456"
        password_hash = get_password_hash(correct_password)
        
        assert verify_password(correct_password, password_hash) == True
        assert verify_password(wrong_password, password_hash) == False

    def test_password_hash_format(self):
        """Test that password hash has correct bcrypt format"""
        password = "test123"
        password_hash = get_password_hash(password)
        
        # bcrypt hashes start with $2b$ and have specific length
        assert password_hash.startswith('$2b$')
        assert len(password_hash) == 60  # Standard bcrypt hash length

class TestPasswordChangeEdgeCases:
    """Test suite for edge cases and boundary conditions"""

    def test_password_change_with_special_characters(self):
        """Test password change with special characters"""
        password_data = PasswordChangeRequest(
            old_password="old!@#$%^&*()123",
            new_password="new!@#$%^&*()456",
            confirm_password="new!@#$%^&*()456"
        )
        
        assert password_data.new_password == "new!@#$%^&*()456"

    def test_password_change_with_unicode_characters(self):
        """Test password change with unicode characters"""
        password_data = PasswordChangeRequest(
            old_password="老密码123",
            new_password="新密码456",
            confirm_password="新密码456"
        )
        
        assert password_data.new_password == "新密码456"

    def test_password_change_boundary_length(self):
        """Test password change with boundary lengths"""
        # Test minimum length (6 characters)
        min_password = "abc123"
        password_data = PasswordChangeRequest(
            old_password="old123",
            new_password=min_password,
            confirm_password=min_password
        )
        assert password_data.new_password == min_password
        
        # Test maximum length (128 characters)
        max_password = "a" * 127 + "1"  # 128 characters with letter and digit
        password_data = PasswordChangeRequest(
            old_password="old123",
            new_password=max_password,
            confirm_password=max_password
        )
        assert password_data.new_password == max_password

if __name__ == "__main__":
    pytest.main([__file__])