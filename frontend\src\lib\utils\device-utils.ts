/**
 * 设备检测和适配工具函数
 */

/**
 * 检查是否为移动设备
 */
export function isMobile(): boolean {
  if (typeof window === 'undefined') return false;
  
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
}

/**
 * 检查是否为平板设备
 */
export function isTablet(): boolean {
  if (typeof window === 'undefined') return false;
  
  return /iPad|Android(?!.*Mobile)/i.test(navigator.userAgent);
}

/**
 * 检查是否为桌面设备
 */
export function isDesktop(): boolean {
  return !isMobile() && !isTablet();
}

/**
 * 检查是否为iOS设备
 */
export function isIOS(): boolean {
  if (typeof window === 'undefined') return false;
  
  return /iPad|iPhone|iPod/.test(navigator.userAgent);
}

/**
 * 检查是否为Android设备
 */
export function isAndroid(): boolean {
  if (typeof window === 'undefined') return false;
  
  return /Android/.test(navigator.userAgent);
}

/**
 * 检查是否为Safari浏览器
 */
export function isSafari(): boolean {
  if (typeof window === 'undefined') return false;
  
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
}

/**
 * 检查是否为Chrome浏览器
 */
export function isChrome(): boolean {
  if (typeof window === 'undefined') return false;
  
  return /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);
}

/**
 * 检查是否为Firefox浏览器
 */
export function isFirefox(): boolean {
  if (typeof window === 'undefined') return false;
  
  return /Firefox/.test(navigator.userAgent);
}

/**
 * 检查是否为Edge浏览器
 */
export function isEdge(): boolean {
  if (typeof window === 'undefined') return false;
  
  return /Edge/.test(navigator.userAgent);
}

/**
 * 获取屏幕尺寸
 */
export function getScreenSize(): { width: number; height: number } {
  if (typeof window === 'undefined') {
    return { width: 0, height: 0 };
  }
  
  return {
    width: window.screen.width,
    height: window.screen.height,
  };
}

/**
 * 获取视口尺寸
 */
export function getViewportSize(): { width: number; height: number } {
  if (typeof window === 'undefined') {
    return { width: 0, height: 0 };
  }
  
  return {
    width: window.innerWidth,
    height: window.innerHeight,
  };
}

/**
 * 检查是否支持触摸
 */
export function isTouchDevice(): boolean {
  if (typeof window === 'undefined') return false;
  
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

/**
 * 检查是否为横屏模式
 */
export function isLandscape(): boolean {
  if (typeof window === 'undefined') return false;
  
  return window.innerWidth > window.innerHeight;
}

/**
 * 检查是否为竖屏模式
 */
export function isPortrait(): boolean {
  return !isLandscape();
}

/**
 * 获取设备像素比
 */
export function getDevicePixelRatio(): number {
  if (typeof window === 'undefined') return 1;
  
  return window.devicePixelRatio || 1;
}

/**
 * 检查是否支持全屏API
 */
export function isFullscreenSupported(): boolean {
  if (typeof document === 'undefined') return false;
  
  return !!(
    document.fullscreenEnabled ||
    (document as any).webkitFullscreenEnabled ||
    (document as any).mozFullScreenEnabled ||
    (document as any).msFullscreenEnabled
  );
}

/**
 * 检查是否支持画中画
 */
export function isPictureInPictureSupported(): boolean {
  if (typeof document === 'undefined') return false;
  
  return 'pictureInPictureEnabled' in document;
}

/**
 * 检查是否支持Web Share API
 */
export function isWebShareSupported(): boolean {
  if (typeof navigator === 'undefined') return false;
  
  return 'share' in navigator;
}

/**
 * 检查是否支持剪贴板API
 */
export function isClipboardSupported(): boolean {
  if (typeof navigator === 'undefined') return false;
  
  return 'clipboard' in navigator;
}

/**
 * 检查是否支持通知API
 */
export function isNotificationSupported(): boolean {
  if (typeof window === 'undefined') return false;
  
  return 'Notification' in window;
}

/**
 * 检查是否支持Service Worker
 */
export function isServiceWorkerSupported(): boolean {
  if (typeof navigator === 'undefined') return false;
  
  return 'serviceWorker' in navigator;
}

/**
 * 获取网络连接信息
 */
export function getNetworkInfo(): {
  online: boolean;
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
} {
  if (typeof navigator === 'undefined') {
    return { online: true };
  }
  
  const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
  
  return {
    online: navigator.onLine,
    effectiveType: connection?.effectiveType,
    downlink: connection?.downlink,
    rtt: connection?.rtt,
  };
}

/**
 * 检查是否为慢速网络
 */
export function isSlowNetwork(): boolean {
  const networkInfo = getNetworkInfo();
  return networkInfo.effectiveType === 'slow-2g' || networkInfo.effectiveType === '2g';
}

/**
 * 获取用户代理信息
 */
export function getUserAgent(): string {
  if (typeof navigator === 'undefined') return '';
  
  return navigator.userAgent;
}

/**
 * 检查是否支持WebGL
 */
export function isWebGLSupported(): boolean {
  if (typeof window === 'undefined') return false;
  
  try {
    const canvas = document.createElement('canvas');
    return !!(
      window.WebGLRenderingContext &&
      (canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
    );
  } catch {
    return false;
  }
}
