"""Fix manga_reading_progress schema to match model definition

Revision ID: fix_manga_reading_progress
Revises: e58c6029231d
Create Date: 2025-08-25 21:25:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'f1a2b3c4d5e6'
down_revision: Union[str, None] = 'e58c6029231d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Fix manga_reading_progress table schema to match model definition"""
    
    # Add missing columns and modify existing ones to match model
    
    # 1. Add total_pages column (required by model)
    op.add_column('manga_reading_progress', sa.Column('total_pages', sa.Integer(), default=0, nullable=True))
    
    # 2. Add progress_percentage column (Float type as in model)
    op.add_column('manga_reading_progress', sa.Column('progress_percentage', sa.Float(), default=0.0, nullable=True))
    
    # 3. Add updated_at column to match model (missing in current schema)
    op.add_column('manga_reading_progress', sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=True))
    
    # 4. Modify page_number to have default value and not nullable (to match model)
    op.alter_column('manga_reading_progress', 'page_number',
                   existing_type=sa.Integer(),
                   nullable=True,
                   server_default='1')
    
    # 5. Migrate data from read_percentage to progress_percentage and set total_pages
    # First, copy data from read_percentage to progress_percentage
    op.execute("""
        UPDATE manga_reading_progress 
        SET progress_percentage = COALESCE(read_percentage, 0.0),
            total_pages = CASE 
                WHEN page_number IS NOT NULL AND page_number > 0 
                THEN page_number  -- Assume total_pages equals current page_number as estimate
                ELSE 0 
            END,
            updated_at = COALESCE(last_read_at, created_at, NOW())
        WHERE progress_percentage IS NULL OR total_pages IS NULL
    """)
    
    # 6. Drop old columns that don't match the model
    op.drop_column('manga_reading_progress', 'read_percentage')
    op.drop_column('manga_reading_progress', 'is_completed')
    op.drop_column('manga_reading_progress', 'sync_version') 
    op.drop_column('manga_reading_progress', 'device_type')
    
    # 7. Set proper defaults for the new columns
    op.alter_column('manga_reading_progress', 'total_pages',
                   existing_type=sa.Integer(),
                   nullable=True,
                   server_default='0')
                   
    op.alter_column('manga_reading_progress', 'progress_percentage',
                   existing_type=sa.Float(),
                   nullable=True,
                   server_default='0.0')


def downgrade() -> None:
    """Rollback schema changes"""
    
    # Add back the old columns
    op.add_column('manga_reading_progress', sa.Column('read_percentage', mysql.DECIMAL(precision=5, scale=2), nullable=True))
    op.add_column('manga_reading_progress', sa.Column('is_completed', mysql.TINYINT(display_width=1), nullable=True))
    op.add_column('manga_reading_progress', sa.Column('sync_version', sa.Integer(), nullable=True))
    op.add_column('manga_reading_progress', sa.Column('device_type', sa.VARCHAR(length=20), nullable=True))
    
    # Migrate data back
    op.execute("""
        UPDATE manga_reading_progress 
        SET read_percentage = COALESCE(progress_percentage, 0.0)
        WHERE read_percentage IS NULL
    """)
    
    # Remove the new columns
    op.drop_column('manga_reading_progress', 'updated_at')
    op.drop_column('manga_reading_progress', 'progress_percentage')
    op.drop_column('manga_reading_progress', 'total_pages')
    
    # Reset page_number column
    op.alter_column('manga_reading_progress', 'page_number',
                   existing_type=sa.Integer(),
                   nullable=True,
                   server_default=None)