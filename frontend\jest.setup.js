import '@testing-library/jest-dom'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
}))

// Mock API client
jest.mock('@/lib/api', () => ({
  apiClient: {
    changePassword: jest.fn(),
    deleteAccount: jest.fn(),
    getCurrentUser: jest.fn(),
    getUserStats: jest.fn(),
    getUserFavorites: jest.fn(),
    getUserReadingHistory: jest.fn(),
  },
}))

// Mock auth context
const mockAuthContext = {
  user: null,
  isAuthenticated: false,
  login: jest.fn(),
  logout: jest.fn(),
  register: jest.fn(),
}

jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => mockAuthContext,
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
}))

// Global test utilities
global.mockAuthContext = mockAuthContext