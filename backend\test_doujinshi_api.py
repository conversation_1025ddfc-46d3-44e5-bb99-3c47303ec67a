#!/usr/bin/env python3
"""
测试同人志API请求
"""

import requests
import json

def test_doujinshi_api():
    """测试同人志API"""
    base_url = "http://localhost:8000/api/v1"

    # 测试参数
    params = {
        'skip': 0,
        'limit': 24,
        'manga_type': 'doujinshi',
        'sort_by': 'created_at',
        'sort_order': 'desc'
    }

    print("=== 测试同人志API请求 ===")
    print(f"请求URL: {base_url}/manga")
    print(f"请求参数: {params}")

    try:
        response = requests.get(f"{base_url}/manga", params=params, timeout=10)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if 'mangas' in data:
                print(f"\n找到 {len(data['mangas'])} 个同人志")
                print(f"总数: {data.get('total', 0)}")
                
                for manga in data['mangas']:
                    print(f"- ID: {manga['id']}, 标题: {manga['title']}, 类型: {manga.get('manga_type', 'N/A')}")
            else:
                print("响应中没有mangas字段")
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
    
    # 也测试一下不带筛选的请求
    print("\n=== 测试不带manga_type筛选的请求 ===")
    params_no_filter = {
        'skip': 0,
        'limit': 5,
        'sort_by': 'created_at',
        'sort_order': 'desc'
    }
    
    try:
        response = requests.get(f"{base_url}/manga", params=params_no_filter, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"总数: {data.get('total', 0)}")
            print("前5个漫画:")
            for manga in data.get('mangas', []):
                print(f"- ID: {manga['id']}, 标题: {manga['title']}, 类型: {manga.get('manga_type', 'N/A')}")
        else:
            print(f"请求失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    test_doujinshi_api()
