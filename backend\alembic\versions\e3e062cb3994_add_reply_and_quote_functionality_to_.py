"""add_reply_and_quote_functionality_to_comments

Revision ID: e3e062cb3994
Revises: d937dce08feb
Create Date: 2025-08-20 22:56:05.226515

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e3e062cb3994'
down_revision: Union[str, None] = 'd937dce08feb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('idx_animes_title_english'), table_name='animes')
    op.drop_index(op.f('idx_animes_title_japanese'), table_name='animes')
    op.add_column('comments', sa.Column('parent_id', sa.Integer(), nullable=True))
    op.add_column('comments', sa.Column('reply_to_user_id', sa.Integer(), nullable=True))
    op.add_column('comments', sa.Column('quoted_comment_id', sa.Integer(), nullable=True))
    op.add_column('comments', sa.Column('quoted_content', sa.Text(), nullable=True))
    op.create_index(op.f('ix_comments_parent_id'), 'comments', ['parent_id'], unique=False)
    op.create_foreign_key(None, 'comments', 'users', ['reply_to_user_id'], ['id'])
    op.create_foreign_key(None, 'comments', 'comments', ['parent_id'], ['id'])
    op.create_foreign_key(None, 'comments', 'comments', ['quoted_comment_id'], ['id'])
    op.drop_index(op.f('idx_tags_name_english'), table_name='tags')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('idx_tags_name_english'), 'tags', ['name_english'], unique=False)
    op.drop_constraint(None, 'comments', type_='foreignkey')
    op.drop_constraint(None, 'comments', type_='foreignkey')
    op.drop_constraint(None, 'comments', type_='foreignkey')
    op.drop_index(op.f('ix_comments_parent_id'), table_name='comments')
    op.drop_column('comments', 'quoted_content')
    op.drop_column('comments', 'quoted_comment_id')
    op.drop_column('comments', 'reply_to_user_id')
    op.drop_column('comments', 'parent_id')
    op.create_index(op.f('idx_animes_title_japanese'), 'animes', ['title_japanese'], unique=False)
    op.create_index(op.f('idx_animes_title_english'), 'animes', ['title_english'], unique=False)
    # ### end Alembic commands ###
