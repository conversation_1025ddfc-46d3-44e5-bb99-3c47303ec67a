'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MangaChapter } from '@/lib/api';
import { useRouter } from 'next/navigation';
import { X, BookOpen } from 'lucide-react';

interface ChapterListSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  chapters: MangaChapter[];
  currentChapterId: number;
  mangaId: number;
}

export function ChapterListSidebar({
  isOpen,
  onClose,
  chapters,
  currentChapterId,
  mangaId
}: ChapterListSidebarProps) {
  const router = useRouter();

  const handleChapterClick = (chapterNumber: number) => {
    router.push(`/manga/${mangaId}/chapter/${chapterNumber}`);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <>
      {/* 背景遮罩 */}
      <div
        className="fixed inset-0 bg-black/50 z-40 transition-opacity duration-300"
        onClick={onClose}
      />
      
      {/* 侧边栏 - 移动端优化宽度 */}
      <div className={`fixed left-0 top-0 h-full w-full sm:w-80 max-w-sm bg-background border-r shadow-2xl z-50 transform transition-transform duration-300 ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-4 border-b bg-muted/50">
          <div className="flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-primary" />
            <h3 className="font-semibold">章节目录</h3>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* 章节列表 - 修复滚动问题 */}
        <div
          className="flex-1 overflow-y-auto overscroll-contain"
          style={{
            WebkitOverflowScrolling: 'touch',
            height: 'calc(100vh - 120px)',
            minHeight: 0
          }}
        >
          <div className="p-2 space-y-1">
            {chapters.map((chapter) => {
              const isCurrent = chapter.id === currentChapterId;
              
              return (
                <Button
                  key={chapter.id}
                  variant={isCurrent ? "default" : "ghost"}
                  className={`w-full justify-start h-auto p-3 text-left ${
                    isCurrent ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'
                  }`}
                  onClick={() => handleChapterClick(chapter.chapter_number)}
                >
                  <div className="flex flex-col items-start w-full">
                    <div className="flex items-center justify-between w-full">
                      <span className="font-medium">
                        第 {chapter.chapter_number} 话
                      </span>
                      {isCurrent && (
                        <Badge variant="secondary">
                          当前
                        </Badge>
                      )}
                    </div>
                    {chapter.title && (
                      <span className={`text-sm mt-1 truncate w-full ${
                        isCurrent ? 'text-primary-foreground/80' : 'text-muted-foreground'
                      }`}>
                        {chapter.title}
                      </span>
                    )}
                    {chapter.created_at && (
                      <span className={`text-xs mt-1 ${
                        isCurrent ? 'text-primary-foreground/60' : 'text-muted-foreground/60'
                      }`}>
                        {new Date(chapter.created_at).toLocaleDateString('zh-CN')}
                      </span>
                    )}
                  </div>
                </Button>
              );
            })}
          </div>
        </div>

        {/* 移除底部信息，简化界面 */}
      </div>
    </>
  );
}