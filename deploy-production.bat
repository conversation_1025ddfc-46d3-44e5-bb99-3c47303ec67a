@echo off
echo 🚀 开始部署动漫网站到生产环境...

REM 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 请先安装Node.js
    pause
    exit /b 1
)

echo ✅ Node.js版本: 
node --version

REM 进入前端目录
cd frontend
if errorlevel 1 (
    echo ❌ 前端目录不存在
    pause
    exit /b 1
)

REM 安装依赖
echo 📦 安装前端依赖...
npm ci
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

REM 构建生产版本
echo 🔨 构建生产版本...
npm run build
if errorlevel 1 (
    echo ❌ 前端构建失败!
    pause
    exit /b 1
)

echo ✅ 前端构建成功!

REM 启动生产服务器
echo 🌟 启动生产服务器...
echo 📍 前端服务将运行在: http://localhost:3000
echo 🛑 按Ctrl+C停止服务
echo.

npm run start