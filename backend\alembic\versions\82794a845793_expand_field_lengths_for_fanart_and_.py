"""Expand field lengths for fanart and other fields

Revision ID: 82794a845793
Revises: af6d641ffebc
Create Date: 2025-08-13 23:18:44.436161

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '82794a845793'
down_revision: Union[str, None] = 'af6d641ffebc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('animes', 'title',
               existing_type=mysql.VARCHAR(length=200),
               type_=sa.String(length=500),
               existing_nullable=False)
    op.alter_column('animes', 'title_english',
               existing_type=mysql.VARCHAR(length=200),
               type_=sa.String(length=500),
               existing_nullable=True)
    op.alter_column('animes', 'title_japanese',
               existing_type=mysql.VARCHAR(length=200),
               type_=sa.String(length=500),
               existing_nullable=True)
    op.alter_column('animes', 'cover',
               existing_type=mysql.VARCHAR(length=500),
               type_=sa.String(length=1000),
               existing_nullable=True)
    op.alter_column('animes', 'fanart',
               existing_type=mysql.VARCHAR(length=500),
               type_=sa.Text(),
               existing_nullable=True)
    op.alter_column('animes', 'video_url',
               existing_type=mysql.VARCHAR(length=500),
               type_=sa.String(length=1000),
               existing_nullable=False)
    op.alter_column('users', 'avatar_url',
               existing_type=mysql.VARCHAR(length=500),
               type_=sa.String(length=1000),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'avatar_url',
               existing_type=sa.String(length=1000),
               type_=mysql.VARCHAR(length=500),
               existing_nullable=True)
    op.alter_column('animes', 'video_url',
               existing_type=sa.String(length=1000),
               type_=mysql.VARCHAR(length=500),
               existing_nullable=False)
    op.alter_column('animes', 'fanart',
               existing_type=sa.Text(),
               type_=mysql.VARCHAR(length=500),
               existing_nullable=True)
    op.alter_column('animes', 'cover',
               existing_type=sa.String(length=1000),
               type_=mysql.VARCHAR(length=500),
               existing_nullable=True)
    op.alter_column('animes', 'title_japanese',
               existing_type=sa.String(length=500),
               type_=mysql.VARCHAR(length=200),
               existing_nullable=True)
    op.alter_column('animes', 'title_english',
               existing_type=sa.String(length=500),
               type_=mysql.VARCHAR(length=200),
               existing_nullable=True)
    op.alter_column('animes', 'title',
               existing_type=sa.String(length=500),
               type_=mysql.VARCHAR(length=200),
               existing_nullable=False)
    # ### end Alembic commands ###
