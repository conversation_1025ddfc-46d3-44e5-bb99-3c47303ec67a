# Hanime1.me 视频抓取和处理系统配置文件

# ========== 全局配置 ==========
app:
  name: "Hanime1.me 视频抓取系统"
  version: "2.0.0"
  author: "AI Assistant"

# ========== 网络配置 ==========
network:
  # 代理配置
  proxy:
    enabled: false # 是否启用代理
    http: "*************************************************" # HTTP代理地址，例如: "http://127.0.0.1:7890"
    https: "*************************************************" # HTTPS代理地址，例如: "http://127.0.0.1:7890"
    socks5: "" # SOCKS5代理地址，例如: "socks5://127.0.0.1:7890"

  # 请求配置
  timeout: 30 # 请求超时时间(秒)
  max_retries: 3 # 最大重试次数
  retry_delay: 1 # 重试延迟(秒)

  # 用户代理
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36"

# ========== 爬取配置 ==========
crawl:
  # 目标网站
  base_url: "https://hanime1.me"

  # 日期过滤 - 支持单个或多个年份/月份
  date_filter:
    # 年份配置 - 支持单个年份或年份数组
    year: 2025 # 单个年份
    # year: [2023, 2024, 2025] # 多个年份
    # year: !!python/object/apply:range [2020, 2026] # 年份范围 2020-2025
    
    # 月份配置 - 支持单个月份或月份数组  
    month: 1 # 单个月份
    # month: [1, 2, 3] # 多个月份
    # month: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12] # 全年
    
    # 使用示例:
    # 1. 爬取2025年1月: year: 2025, month: 1
    # 2. 爬取2025年前3个月: year: 2025, month: [1, 2, 3] 
    # 3. 爬取2023-2025年全年: year: [2023, 2024, 2025], month: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
    # 4. 爬取2020-2025年指定月份: year: [2020, 2021, 2022, 2023, 2024, 2025], month: [6, 7, 8]

  # 搜索配置
  search:
    genre: "%E8%A3%8F%E7%95%AA" # 里番类型的URL编码
    sort: "" # 排序方式
    type: "" # 视频类型

  # 质量优先级 (从高到低)
  quality_priority:
    - "1080"
    - "720"
    - "480"

  # 跳过关键词
  skip_keywords:
    - "中字後補"
    - "简中补字"
    - "Chinese Sub"
    - "中文字幕後補"

# ========== 下载配置 ==========
download:
  # 下载目录
  download_dir: "downloads" # 下载根目录
  temp_dir: "temp" # 临时文件目录

  # 目录组织
  organize_by_date: true # 是否按日期组织目录 (年/月)

  # 下载选项
  enable_video: true # 是否下载视频
  enable_cover: true # 是否下载封面
  enable_fanart: true # 是否下载高清图片

  # 下载优化
  chunk_size: 8192 # 下载块大小(字节)
  max_concurrent: 3 # 最大并发下载数

  # 文件命名
  clean_filename: true # 是否清理文件名中的非法字符

# ========== Selenium配置 ==========
selenium:
  # Chrome选项
  headless: true # 是否无头模式
  no_sandbox: true # 是否禁用沙箱
  disable_dev_shm: true # 是否禁用/dev/shm使用

  # 等待时间
  implicit_wait: 5 # 隐式等待时间(秒)
  page_load_timeout: 30 # 页面加载超时(秒)

# ========== 刮削配置 ==========
scrape:
  # 刮削模式
  mode: "online" # online: 在线搜索, offline: 仅本地文件名解析

  # 搜索配置
  search_sites:
    hanime1: true # 是否启用hanime1.me搜索

  # NFO文件配置
  nfo:
    enabled: true # 是否生成NFO文件
    encoding: "utf-8" # NFO文件编码

  # 图片下载
  images:
    fanart: true # 是否下载fanart图片
    poster: true # 是否下载poster图片
    thumb: false # 是否下载缩略图

  # 合集检测
  collection:
    enabled: true # 是否启用合集检测

# ========== 重命名配置 ==========
rename:
  # 重命名规则
  enabled: true # 是否启用重命名

  # 文件名模板
  template: "{title}" # 重命名模板

  # 清理规则
  remove_brackets: true # 是否移除方括号内容
  clean_special_chars: true # 是否清理特殊字符

# ========== 日志配置 ==========
logging:
  level: "INFO" # 日志级别: DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "scraper.log" # 日志文件路径
  max_size: "10MB" # 日志文件最大大小
  backup_count: 5 # 日志文件备份数量

# ========== 性能配置 ==========
performance:
  # curl_cffi配置
  http2: true # 是否启用HTTP/2
  connection_pool_size: 10 # 连接池大小
  max_redirects: 10 # 最大重定向次数

  # 内存优化
  gc_threshold: 100 # 垃圾回收阈值

# ========== 数据库配置 ==========
database:
  host: "************:3306"
  user: "sql23721_hentai"
  password: "507877550@lihao"
  database: "sql23721_hentai"
  charset: "utf8mb4"

# ========== Web访问配置 ==========
web_access:
  domain_prefix: 'https://static.denlu.top'
  base_path: '/downloads'

