from fastapi import APIRouter, Depends, HTTPException
from app.core.deps import get_current_admin_user
from app.middleware.cache import CacheStats, invalidate_cache_pattern
from app.models import User

router = APIRouter()

@router.get("/stats", summary="获取缓存统计信息（管理员）")
async def get_cache_stats(
    current_user: User = Depends(get_current_admin_user)
):
    """获取Redis和内存缓存的统计信息"""
    try:
        stats = await CacheStats.get_stats()
        return {"success": True, "data": stats}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缓存统计失败: {str(e)}")

@router.post("/clear", summary="清空所有缓存（管理员）")
async def clear_all_cache(
    current_user: User = Depends(get_current_admin_user)
):
    """清空所有缓存数据"""
    try:
        result = await CacheStats.clear_all()
        return {
            "success": result["success"],
            "message": "缓存清理完成",
            "details": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清空缓存失败: {str(e)}")

@router.post("/invalidate/{pattern}", summary="清除指定模式的缓存（管理员）")
async def invalidate_cache(
    pattern: str,
    current_user: User = Depends(get_current_admin_user)
):
    """根据模式清除缓存"""
    try:
        deleted_count = await invalidate_cache_pattern(pattern)
        return {
            "success": True,
            "message": f"已清除 {deleted_count} 个匹配的缓存项",
            "pattern": pattern,
            "deleted_count": deleted_count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")

@router.get("/health", summary="缓存健康检查")
async def cache_health_check():
    """检查缓存系统健康状态（公开接口）"""
    try:
        stats = await CacheStats.get_stats()
        
        redis_status = stats.get("redis", {}).get("status", "unknown")
        fallback_status = stats.get("fallback", {}).get("status", "unknown")
        
        overall_status = "healthy" if (redis_status == "available" or fallback_status == "available") else "unhealthy"
        
        return {
            "status": overall_status,
            "redis_available": redis_status == "available",
            "fallback_available": fallback_status == "available",
            "message": "缓存系统正常运行" if overall_status == "healthy" else "缓存系统异常"
        }
    except Exception as e:
        return {
            "status": "error",
            "redis_available": False,
            "fallback_available": False,
            "message": f"健康检查失败: {str(e)}"
        }