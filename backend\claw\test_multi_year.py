#!/usr/bin/env python3
"""
测试多年份配置支持
"""

import sys
import os
import yaml
import tempfile
sys.path.append('.')

def test_multiple_years_config():
    """测试多年份配置"""
    print("Testing multiple years configuration support...")
    
    # 测试用例1: 单个年份 (现有功能)
    print("\n=== Test Case 1: Single Year ===")
    config1 = {
        'crawl': {
            'date_filter': {
                'year': 2025,
                'month': [1, 2]
            }
        },
        'database': {
            'host': '************:3306',
            'user': 'sql23721_hentai',
            'password': '507877550@lihao',
            'database': 'sql23721_hentai'
        }
    }
    
    test_config_processing(config1, "single year")
    
    # 测试用例2: 多个年份
    print("\n=== Test Case 2: Multiple Years ===")
    config2 = {
        'crawl': {
            'date_filter': {
                'year': [2023, 2024, 2025],
                'month': [1, 2, 3]
            }
        },
        'database': {
            'host': '************:3306',
            'user': 'sql23721_hentai',
            'password': '507877550@lihao',
            'database': 'sql23721_hentai'
        }
    }
    
    test_config_processing(config2, "multiple years")
    
    # 测试用例3: 跨年度大范围
    print("\n=== Test Case 3: Wide Range Multiple Years ===")
    config3 = {
        'crawl': {
            'date_filter': {
                'year': list(range(2020, 2026)),  # 2020-2025
                'month': list(range(1, 13))       # 全年月份
            }
        },
        'database': {
            'host': '************:3306',
            'user': 'sql23721_hentai',
            'password': '507877550@lihao',
            'database': 'sql23721_hentai'
        }
    }
    
    test_config_processing(config3, "wide range years")

def test_config_processing(config, test_name):
    """测试配置处理逻辑"""
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yml', delete=False, encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        temp_config_path = f.name
    
    try:
        # 模拟配置加载逻辑
        crawl_config = config.get('crawl', {})
        date_filter = crawl_config.get('date_filter', {})
        
        # Handle both single year and array of years
        year_config = date_filter.get('year', 2025)
        if isinstance(year_config, list):
            target_years = year_config
        else:
            target_years = [year_config]
        
        # Handle both single month and array of months
        month_config = date_filter.get('month', 6)
        if isinstance(month_config, list):
            target_months = month_config
        else:
            target_months = [month_config]
        
        print(f"Configuration for {test_name}:")
        print(f"  Target years: {target_years}")
        print(f"  Target months: {target_months}")
        print(f"  Total combinations: {len(target_years)} years × {len(target_months)} months = {len(target_years) * len(target_months)} combinations")
        
        # 模拟URL生成
        print(f"  Sample URLs:")
        count = 0
        for year in target_years[:2]:  # 只显示前2年
            for month in target_months[:3]:  # 只显示前3个月
                count += 1
                url = f"https://hanime1.me/search?query=&type=&genre=%E8%A3%8F%E7%95%AA&sort=&date={year}+%E5%B9%B4+{month}+%E6%9C%88&duration="
                print(f"    {count}. {url}")
                if count >= 3:  # 限制显示数量
                    break
            if count >= 3:
                break
        
        if len(target_years) * len(target_months) > 3:
            print(f"    ... (total {len(target_years) * len(target_months)} combinations)")
        
        print(f"✅ {test_name.capitalize()} configuration test PASSED")
        return True
        
    except Exception as e:
        print(f"❌ {test_name.capitalize()} configuration test FAILED: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(temp_config_path):
            os.remove(temp_config_path)

def create_example_config():
    """创建示例配置文件"""
    print("\n=== Creating Example Configuration ===")
    
    example_config = {
        'crawl': {
            'date_filter': {
                # 示例1: 单个年份
                # 'year': 2025,
                # 'month': [1, 2, 3]
                
                # 示例2: 多个年份 
                'year': [2023, 2024, 2025],
                'month': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
                
                # 示例3: 大范围年份
                # 'year': list(range(2020, 2026)),  # 2020-2025
                # 'month': list(range(1, 13))       # 全年
            }
        }
    }
    
    example_file = 'example_multi_year_config.yml'
    with open(example_file, 'w', encoding='utf-8') as f:
        f.write("# 多年份爬取配置示例\n")
        f.write("# Multiple years crawling configuration example\n\n")
        yaml.dump(example_config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"✅ Example configuration created: {example_file}")
    print("\nConfiguration examples:")
    print("1. Single year:     year: 2025")
    print("2. Multiple years:  year: [2023, 2024, 2025]")
    print("3. Year range:      year: list(range(2020, 2026))")
    print("4. Single month:    month: 1")
    print("5. Multiple months: month: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]")

if __name__ == "__main__":
    print("Testing Multiple Years Configuration Support")
    print("=" * 60)
    
    # 运行测试
    success1 = test_multiple_years_config()
    
    # 创建示例配置
    create_example_config()
    
    if success1:
        print("\n🎉 All tests passed! Multiple years configuration is working correctly.")
        print("\nYou can now configure multiple years in your config.yml like this:")
        print("```yaml")
        print("crawl:")
        print("  date_filter:")
        print("    year: [2023, 2024, 2025]  # Multiple years")
        print("    month: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]  # All months")
        print("```")
    else:
        print("\n❌ Some tests failed!")