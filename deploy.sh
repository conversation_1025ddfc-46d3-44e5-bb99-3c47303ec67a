#!/bin/bash

# 动漫网站生产环境部署脚本
# 使用方法: ./deploy.sh [选项]
# 选项:
#   --build-only    只构建，不启动
#   --no-cache      不使用缓存构建
#   --backup        部署前备份数据库

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查配置文件
check_config() {
    log_info "检查配置文件..."
    
    if [ ! -f "frontend/.env.production" ]; then
        log_error "前端生产环境配置文件不存在: frontend/.env.production"
        exit 1
    fi
    
    if [ ! -f "backend/.env.production" ]; then
        log_error "后端生产环境配置文件不存在: backend/.env.production"
        exit 1
    fi
    
    if [ ! -f "docker-compose.production.yml" ]; then
        log_error "Docker Compose 生产环境配置文件不存在: docker-compose.production.yml"
        exit 1
    fi
    
    log_success "配置文件检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p backend/uploads
    mkdir -p backend/logs
    mkdir -p backend/backups
    mkdir -p nginx/ssl
    
    log_success "目录创建完成"
}

# 备份数据库
backup_database() {
    if [ "$1" = "--backup" ]; then
        log_info "备份数据库..."
        
        # 检查是否有运行中的数据库容器
        if docker-compose -f docker-compose.production.yml ps postgres | grep -q "Up"; then
            BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
            docker-compose -f docker-compose.production.yml exec -T postgres pg_dump -U anime_user anime_website > "backend/backups/$BACKUP_FILE"
            log_success "数据库备份完成: $BACKUP_FILE"
        else
            log_warning "数据库容器未运行，跳过备份"
        fi
    fi
}

# 构建镜像
build_images() {
    local build_args=""
    
    if [ "$1" = "--no-cache" ]; then
        build_args="--no-cache"
        log_info "构建镜像（不使用缓存）..."
    else
        log_info "构建镜像..."
    fi
    
    docker-compose -f docker-compose.production.yml build $build_args
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 停止现有服务
    docker-compose -f docker-compose.production.yml down
    
    # 启动服务
    docker-compose -f docker-compose.production.yml up -d
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待数据库
    log_info "等待数据库启动..."
    until docker-compose -f docker-compose.production.yml exec postgres pg_isready -U anime_user; do
        sleep 2
    done
    
    # 等待后端API
    log_info "等待后端API启动..."
    until curl -f http://localhost:8000/health &> /dev/null; do
        sleep 2
    done
    
    # 等待前端
    log_info "等待前端启动..."
    until curl -f http://localhost:3000 &> /dev/null; do
        sleep 2
    done
    
    log_success "所有服务已就绪"
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    docker-compose -f docker-compose.production.yml exec backend python -m alembic upgrade head
    
    log_success "数据库迁移完成"
}

# 显示服务状态
show_status() {
    log_info "服务状态:"
    docker-compose -f docker-compose.production.yml ps
    
    echo ""
    log_info "服务访问地址:"
    echo "  前端: http://localhost:3000"
    echo "  后端API: http://localhost:8000"
    echo "  API文档: http://localhost:8000/docs"
    echo ""
    
    log_success "部署完成！"
}

# 主函数
main() {
    log_info "开始部署动漫网站..."
    
    # 解析参数
    BUILD_ONLY=false
    NO_CACHE=false
    BACKUP=false
    
    for arg in "$@"; do
        case $arg in
            --build-only)
                BUILD_ONLY=true
                ;;
            --no-cache)
                NO_CACHE=true
                ;;
            --backup)
                BACKUP=true
                ;;
            *)
                log_error "未知参数: $arg"
                echo "使用方法: $0 [--build-only] [--no-cache] [--backup]"
                exit 1
                ;;
        esac
    done
    
    # 执行部署步骤
    check_dependencies
    check_config
    create_directories
    
    if [ "$BACKUP" = true ]; then
        backup_database --backup
    fi
    
    if [ "$NO_CACHE" = true ]; then
        build_images --no-cache
    else
        build_images
    fi
    
    if [ "$BUILD_ONLY" = false ]; then
        start_services
        wait_for_services
        run_migrations
        show_status
    else
        log_success "构建完成（仅构建模式）"
    fi
}

# 运行主函数
main "$@"
