"""
Redis配置管理API
"""
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.deps import get_current_admin_user
from app.core.redis_config import RedisConfigManager
from app.core.redis_client import redis_client
from app.models import User
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

# ==================== 请求/响应模型 ====================

class RedisConfigResponse(BaseModel):
    """Redis配置响应模型"""
    redis_enabled: bool = Field(description="是否启用Redis")
    redis_host: str = Field(description="Redis主机地址")
    redis_port: int = Field(description="Redis端口")
    redis_db: int = Field(description="Redis数据库编号")
    redis_password: str = Field(description="Redis密码")
    redis_max_memory: int = Field(description="最大内存限制(MB，0为无限制)")
    redis_expire_time: int = Field(description="默认过期时间(秒)")
    redis_max_connections: int = Field(description="最大连接数")

class RedisConfigUpdate(BaseModel):
    """Redis配置更新模型"""
    redis_enabled: Optional[bool] = Field(None, description="是否启用Redis")
    redis_host: Optional[str] = Field(None, description="Redis主机地址")
    redis_port: Optional[int] = Field(None, description="Redis端口", ge=1, le=65535)
    redis_db: Optional[int] = Field(None, description="Redis数据库编号", ge=0, le=15)
    redis_password: Optional[str] = Field(None, description="Redis密码")
    redis_max_memory: Optional[int] = Field(None, description="最大内存限制(MB)", ge=0)
    redis_expire_time: Optional[int] = Field(None, description="默认过期时间(秒)", ge=0)
    redis_max_connections: Optional[int] = Field(None, description="最大连接数", ge=1)

class RedisStatusResponse(BaseModel):
    """Redis状态响应模型"""
    connected: bool = Field(description="是否已连接")
    enabled: bool = Field(description="是否启用")
    host: Optional[str] = Field(None, description="当前连接的主机")
    port: Optional[int] = Field(None, description="当前连接的端口")
    db: Optional[int] = Field(None, description="当前数据库编号")
    memory_info: Optional[Dict[str, Any]] = Field(None, description="内存使用信息")
    error: Optional[str] = Field(None, description="错误信息")

class RedisTestConnectionRequest(BaseModel):
    """测试Redis连接请求"""
    host: str = Field(description="Redis主机地址")
    port: int = Field(description="Redis端口", ge=1, le=65535)
    db: int = Field(default=0, description="Redis数据库编号", ge=0, le=15)
    password: Optional[str] = Field(None, description="Redis密码")

# ==================== API端点 ====================

@router.get("/config", response_model=RedisConfigResponse, summary="获取Redis配置")
async def get_redis_config(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """获取当前Redis配置"""
    try:
        config = RedisConfigManager.get_all_config(db)
        return RedisConfigResponse(**config)
    except Exception as e:
        logger.error(f"获取Redis配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")

@router.put("/config", response_model=Dict[str, str], summary="更新Redis配置")
async def update_redis_config(
    config_update: RedisConfigUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """更新Redis配置并重新连接"""
    try:
        # 准备更新的配置
        updates = {}
        if config_update.redis_enabled is not None:
            updates['redis_enabled'] = config_update.redis_enabled
        if config_update.redis_host is not None:
            updates['redis_host'] = config_update.redis_host
        if config_update.redis_port is not None:
            updates['redis_port'] = config_update.redis_port
        if config_update.redis_db is not None:
            updates['redis_db'] = config_update.redis_db
        if config_update.redis_password is not None:
            updates['redis_password'] = config_update.redis_password
        if config_update.redis_max_memory is not None:
            updates['redis_max_memory'] = config_update.redis_max_memory
        if config_update.redis_expire_time is not None:
            updates['redis_expire_time'] = config_update.redis_expire_time
        if config_update.redis_max_connections is not None:
            updates['redis_max_connections'] = config_update.redis_max_connections
        
        # 验证配置
        is_valid, message = RedisConfigManager.validate_config(updates)
        if not is_valid:
            raise HTTPException(status_code=400, detail=message)
        
        # 更新配置到数据库
        success = RedisConfigManager.update_configs(db, updates)
        if not success:
            raise HTTPException(status_code=500, detail="配置更新失败")
        
        # 重新初始化Redis连接
        await redis_client.init_redis(force_reconnect=True)
        
        return {"message": "Redis配置更新成功", "status": "success"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新Redis配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"配置更新失败: {str(e)}")

@router.get("/status", response_model=RedisStatusResponse, summary="获取Redis状态")
async def get_redis_status(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """获取Redis连接状态和内存使用信息"""
    try:
        config = RedisConfigManager.get_all_config(db)
        
        # 基本状态
        status = {
            "enabled": config.get('redis_enabled', False),
            "connected": redis_client.is_available,
            "host": config.get('redis_host'),
            "port": config.get('redis_port'),
            "db": config.get('redis_db')
        }
        
        # 如果连接正常，获取内存信息
        if redis_client.is_available:
            memory_info = await redis_client.get_memory_info()
            status["memory_info"] = memory_info
        else:
            status["error"] = "Redis未连接或连接失败"
        
        return RedisStatusResponse(**status)
        
    except Exception as e:
        logger.error(f"获取Redis状态失败: {e}")
        return RedisStatusResponse(
            connected=False,
            enabled=False,
            error=str(e)
        )

@router.post("/test-connection", response_model=Dict[str, Any], summary="测试Redis连接")
async def test_redis_connection(
    test_config: RedisTestConnectionRequest,
    current_user: User = Depends(get_current_admin_user)
):
    """测试Redis连接是否正常"""
    import redis.asyncio as redis
    
    try:
        # 构建连接URL
        if test_config.password:
            redis_url = f"redis://:{test_config.password}@{test_config.host}:{test_config.port}/{test_config.db}"
        else:
            redis_url = f"redis://{test_config.host}:{test_config.port}/{test_config.db}"
        
        # 测试连接
        test_client = redis.from_url(redis_url, decode_responses=True)
        await test_client.ping()
        
        # 获取服务器信息
        info = await test_client.info('server')
        
        # 关闭测试连接
        await test_client.close()
        
        return {
            "success": True,
            "message": "连接成功",
            "server_info": {
                "redis_version": info.get('redis_version', 'unknown'),
                "redis_mode": info.get('redis_mode', 'standalone'),
                "os": info.get('os', 'unknown'),
                "tcp_port": info.get('tcp_port', test_config.port)
            }
        }
        
    except Exception as e:
        logger.error(f"测试Redis连接失败: {e}")
        return {
            "success": False,
            "message": f"连接失败: {str(e)}",
            "error": str(e)
        }

@router.post("/flush", response_model=Dict[str, str], summary="清空Redis缓存")
async def flush_redis_cache(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """清空Redis当前数据库的所有缓存"""
    try:
        if not redis_client.is_available:
            raise HTTPException(status_code=400, detail="Redis未连接")
        
        success = await redis_client.flush_db()
        if success:
            return {"message": "Redis缓存已清空", "status": "success"}
        else:
            raise HTTPException(status_code=500, detail="清空缓存失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清空Redis缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"操作失败: {str(e)}")

@router.post("/clear-pattern", response_model=Dict[str, Any], summary="按模式清除缓存")
async def clear_cache_pattern(
    pattern: str,
    current_user: User = Depends(get_current_admin_user)
):
    """按模式清除特定的缓存键"""
    try:
        if not redis_client.is_available:
            raise HTTPException(status_code=400, detail="Redis未连接")
        
        deleted_count = await redis_client.clear_pattern(pattern)
        return {
            "message": f"已清除 {deleted_count} 个缓存键",
            "deleted_count": deleted_count,
            "pattern": pattern,
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"清除缓存模式失败: {e}")
        raise HTTPException(status_code=500, detail=f"操作失败: {str(e)}")

@router.post("/initialize", response_model=Dict[str, str], summary="初始化Redis默认配置")
async def initialize_redis_config(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """初始化Redis默认配置到数据库"""
    try:
        RedisConfigManager.initialize_default_config(db)
        return {"message": "Redis默认配置已初始化", "status": "success"}
    except Exception as e:
        logger.error(f"初始化Redis配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"初始化失败: {str(e)}")