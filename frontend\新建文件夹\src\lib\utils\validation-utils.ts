import { VALIDATION } from '../constants';

/**
 * 验证用户名
 */
export function validateUsername(username: string): { isValid: boolean; error?: string } {
  if (!username) {
    return { isValid: false, error: '用户名不能为空' };
  }
  
  if (username.length < VALIDATION.USERNAME.MIN_LENGTH) {
    return { isValid: false, error: `用户名至少需要${VALIDATION.USERNAME.MIN_LENGTH}个字符` };
  }
  
  if (username.length > VALIDATION.USERNAME.MAX_LENGTH) {
    return { isValid: false, error: `用户名不能超过${VALIDATION.USERNAME.MAX_LENGTH}个字符` };
  }
  
  if (!VALIDATION.USERNAME.PATTERN.test(username)) {
    return { isValid: false, error: '用户名只能包含字母、数字和下划线' };
  }
  
  return { isValid: true };
}

/**
 * 验证密码
 */
export function validatePassword(password: string): { isValid: boolean; error?: string } {
  if (!password) {
    return { isValid: false, error: '密码不能为空' };
  }
  
  if (password.length < VALIDATION.PASSWORD.MIN_LENGTH) {
    return { isValid: false, error: `密码至少需要${VALIDATION.PASSWORD.MIN_LENGTH}个字符` };
  }
  
  if (password.length > VALIDATION.PASSWORD.MAX_LENGTH) {
    return { isValid: false, error: `密码不能超过${VALIDATION.PASSWORD.MAX_LENGTH}个字符` };
  }
  
  return { isValid: true };
}

/**
 * 验证邮箱
 */
export function validateEmail(email: string): { isValid: boolean; error?: string } {
  if (!email) {
    return { isValid: false, error: '邮箱不能为空' };
  }
  
  if (!VALIDATION.EMAIL.PATTERN.test(email)) {
    return { isValid: false, error: '请输入有效的邮箱地址' };
  }
  
  return { isValid: true };
}

/**
 * 验证确认密码
 */
export function validateConfirmPassword(
  password: string,
  confirmPassword: string
): { isValid: boolean; error?: string } {
  if (!confirmPassword) {
    return { isValid: false, error: '请确认密码' };
  }
  
  if (password !== confirmPassword) {
    return { isValid: false, error: '两次输入的密码不一致' };
  }
  
  return { isValid: true };
}

/**
 * 验证URL
 */
export function validateUrl(url: string): { isValid: boolean; error?: string } {
  if (!url) {
    return { isValid: false, error: 'URL不能为空' };
  }
  
  try {
    new URL(url);
    return { isValid: true };
  } catch {
    return { isValid: false, error: '请输入有效的URL' };
  }
}

/**
 * 验证文件类型
 */
export function validateFileType(
  file: File,
  allowedTypes: string[]
): { isValid: boolean; error?: string } {
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `不支持的文件类型。支持的类型：${allowedTypes.join(', ')}`,
    };
  }
  
  return { isValid: true };
}

/**
 * 验证文件大小
 */
export function validateFileSize(
  file: File,
  maxSize: number
): { isValid: boolean; error?: string } {
  if (file.size > maxSize) {
    const maxSizeMB = Math.round(maxSize / (1024 * 1024));
    return {
      isValid: false,
      error: `文件大小不能超过${maxSizeMB}MB`,
    };
  }
  
  return { isValid: true };
}

/**
 * 验证数字范围
 */
export function validateNumberRange(
  value: number,
  min?: number,
  max?: number
): { isValid: boolean; error?: string } {
  if (isNaN(value)) {
    return { isValid: false, error: '请输入有效的数字' };
  }
  
  if (min !== undefined && value < min) {
    return { isValid: false, error: `数值不能小于${min}` };
  }
  
  if (max !== undefined && value > max) {
    return { isValid: false, error: `数值不能大于${max}` };
  }
  
  return { isValid: true };
}

/**
 * 验证字符串长度
 */
export function validateStringLength(
  value: string,
  min?: number,
  max?: number
): { isValid: boolean; error?: string } {
  if (min !== undefined && value.length < min) {
    return { isValid: false, error: `长度不能少于${min}个字符` };
  }
  
  if (max !== undefined && value.length > max) {
    return { isValid: false, error: `长度不能超过${max}个字符` };
  }
  
  return { isValid: true };
}

/**
 * 验证必填字段
 */
export function validateRequired(value: any): { isValid: boolean; error?: string } {
  if (value === null || value === undefined || value === '') {
    return { isValid: false, error: '此字段为必填项' };
  }
  
  if (Array.isArray(value) && value.length === 0) {
    return { isValid: false, error: '请至少选择一项' };
  }
  
  return { isValid: true };
}

/**
 * 批量验证
 */
export function validateFields(
  fields: Record<string, any>,
  validators: Record<string, (value: any) => { isValid: boolean; error?: string }>
): { isValid: boolean; errors: Record<string, string> } {
  const errors: Record<string, string> = {};
  let isValid = true;
  
  Object.entries(validators).forEach(([fieldName, validator]) => {
    const result = validator(fields[fieldName]);
    if (!result.isValid) {
      errors[fieldName] = result.error || '验证失败';
      isValid = false;
    }
  });
  
  return { isValid, errors };
}
