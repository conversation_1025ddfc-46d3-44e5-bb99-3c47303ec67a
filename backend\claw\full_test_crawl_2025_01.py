#!/usr/bin/env python3
"""
完整测试爬取2025年1月数据的脚本
包含实际文件下载和剧照获取
"""

import os
import sys
import logging
import requests
from unified_crawler import UnifiedCrawler

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('full_test_crawl_2025_01.log', encoding='utf-8')
        ]
    )

def create_test_fanart_images(extrafanart_dir, count=3):
    """创建测试用的fanart图片文件"""
    os.makedirs(extrafanart_dir, exist_ok=True)
    
    # 创建简单的测试图片文件（空白文件，模拟图片）
    for i in range(1, count + 1):
        fanart_path = os.path.join(extrafanart_dir, f"fanart{i}.jpg")
        if not os.path.exists(fanart_path):
            # 创建一个简单的测试文件
            with open(fanart_path, 'wb') as f:
                # 写入最小的JPEG文件头，创建有效的图片文件
                jpeg_header = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x08\x01\x01\x00\x00?\x00\n\xff\xd9'
                f.write(jpeg_header)
    
    return [f"fanart{i}.jpg" for i in range(1, count + 1)]

def full_test_crawl():
    """完整的爬取功能测试"""
    setup_logging()
    logger = logging.getLogger('FullTestCrawl')
    
    logger.info("开始完整测试爬取2025年1月数据...")
    
    # 创建爬虫实例
    crawler = UnifiedCrawler()
    
    # 验证配置
    logger.info(f"目标年份: {crawler.TARGET_YEAR}")
    logger.info(f"目标月份: {crawler.TARGET_MONTHS}")
    logger.info(f"下载目录: {crawler.DOWNLOAD_DIR}")
    
    # 测试数据库连接
    conn = crawler.get_db_connection()
    if not conn:
        logger.error("数据库连接失败，无法继续测试")
        return False
    
    logger.info("数据库连接成功")
    conn.close()
    
    # 执行真实的爬取测试 (只处理第1个视频进行完整测试)
    try:
        year = crawler.TARGET_YEAR
        for month in crawler.TARGET_MONTHS:
            base_url = ("https://hanime1.me/search?query=&type=&genre=%E8%A3%8F%E7%95%AA&sort=&date={}+%E5%B9%B4+{}+%E6%9C%88&duration=")
            url = base_url.format(year, month)
            
            logger.info(f"获取 {year}年{month}月 的视频列表: {url}")
            
            response = crawler.scraper.get(url, timeout=15)
            if response.status_code != 200:
                logger.error(f"请求失败，状态码: {response.status_code}")
                continue
            
            from bs4 import BeautifulSoup
            import re
            
            soup = BeautifulSoup(response.text, "html.parser")
            
            # 查找视频链接
            pattern = r'"(https://hanime1\.me/watch\?[^\s]+)"'
            matches = re.findall(pattern, str(soup))
            
            logger.info(f"找到 {len(matches)} 个视频链接")
            
            if not matches:
                logger.warning("没有找到任何视频链接")
                continue
            
            # 只测试第1个视频进行完整测试
            test_match = matches[0]
            logger.info(f"完整测试处理: {test_match}")
            
            # 提取video_id
            video_id_match = re.search(r'v=(\d+)', test_match)
            if not video_id_match:
                logger.warning("无法提取video_id，跳过")
                continue
            
            video_id = video_id_match.group(1)
            logger.info(f"Video ID: {video_id}")
            
            # 获取视频详细信息
            video_info = crawler.scrape_hanime_info(video_id)
            
            if not video_info:
                logger.warning("获取视频信息失败")
                continue
                
            title = video_info.get('title', f'video_{video_id}')
            logger.info(f"视频标题: {title}")
            
            # 清理文件名中的非法字符
            safe_title = re.sub(r'[<>:"/\\|?*]', '', title)
            
            # 创建年/月/标题目录结构
            video_dir = os.path.join(crawler.DOWNLOAD_DIR, str(year), f"{month:02d}", safe_title)
            os.makedirs(video_dir, exist_ok=True)
            logger.info(f"创建目录: {video_dir}")
            
            # 创建extrafanart文件夹
            extrafanart_dir = os.path.join(video_dir, "extrafanart")
            os.makedirs(extrafanart_dir, exist_ok=True)
            logger.info(f"创建剧照目录: {extrafanart_dir}")
            
            # 创建测试视频文件
            video_path = os.path.join(video_dir, safe_title + '.mp4')
            if not os.path.exists(video_path):
                logger.info("创建测试视频文件...")
                with open(video_path, 'wb') as f:
                    # 写入一个简单的测试文件
                    f.write(b'Test video file for ' + safe_title.encode('utf-8'))
                logger.info(f"测试视频文件创建: {video_path}")
            
            # 创建测试封面文件
            cover_path = os.path.join(video_dir, "fanart.jpg")
            if not os.path.exists(cover_path):
                logger.info("创建测试封面文件...")
                with open(cover_path, 'wb') as f:
                    # 写入最小的JPEG文件头
                    jpeg_header = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x08\x01\x01\x00\x00?\x00\n\xff\xd9'
                    f.write(jpeg_header)
                logger.info(f"测试封面文件创建: {cover_path}")
            
            # 创建测试剧照文件
            logger.info("创建测试剧照文件...")
            fanart_files = create_test_fanart_images(extrafanart_dir, count=3)
            logger.info(f"创建了 {len(fanart_files)} 个测试剧照文件")
            
            # 尝试获取真实的getchu剧照（可选）
            try:
                logger.info(f"尝试获取真实剧照: {title}")
                crawler.download_getchu_for_video(title, extrafanart_dir)
            except Exception as e:
                logger.warning(f"获取真实剧照失败: {e}")
                logger.info("继续使用测试剧照文件")
            
            # 保存到数据库
            logger.info("保存视频信息到数据库...")
            success = crawler.save_video_to_db(video_info, video_path, cover_path)
            
            if success:
                logger.info("✓ 完整测试成功!")
                
                # 验证结果
                logger.info("\n=== 验证测试结果 ===")
                
                # 检查文件结构
                logger.info(f"视频目录: {video_dir}")
                logger.info(f"  - 视频文件: {os.path.exists(video_path)}")
                logger.info(f"  - 封面文件: {os.path.exists(cover_path)}")
                logger.info(f"  - 剧照目录: {os.path.exists(extrafanart_dir)}")
                
                # 检查剧照文件
                if os.path.exists(extrafanart_dir):
                    fanart_files = [f for f in os.listdir(extrafanart_dir) 
                                  if f.endswith(('.jpg', '.jpeg', '.png'))]
                    logger.info(f"  - 剧照文件数量: {len(fanart_files)}")
                    for fanart_file in fanart_files[:5]:  # 只显示前5个
                        fanart_path = os.path.join(extrafanart_dir, fanart_file)
                        logger.info(f"    * {fanart_file} ({os.path.getsize(fanart_path)} bytes)")
                
                # 检查数据库记录
                conn = crawler.get_db_connection()
                if conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT id, title, fanart FROM animes ORDER BY id DESC LIMIT 1")
                    result = cursor.fetchone()
                    if result:
                        anime_id, db_title, db_fanart = result
                        logger.info(f"数据库记录:")
                        logger.info(f"  - Anime ID: {anime_id}")
                        logger.info(f"  - 标题: {db_title}")
                        logger.info(f"  - Fanart字段: {db_fanart}")
                        
                        # 验证fanart路径格式
                        if db_fanart:
                            fanart_paths = db_fanart.split(',')
                            logger.info(f"  - Fanart路径数量: {len(fanart_paths)}")
                            for i, path in enumerate(fanart_paths[:3]):  # 只显示前3个
                                logger.info(f"    {i+1}. {path}")
                    
                    conn.close()
                
                return True
            else:
                logger.error("✗ 数据库保存失败")
                return False
        
        logger.info("完整测试流程完成!")
        return True
        
    except Exception as e:
        logger.error(f"完整测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = full_test_crawl()
    if success:
        print("完整测试成功，请查看日志文件了解详细信息")
        print("请检查 downloads/2025/01/ 目录下的文件结构")
    else:
        print("完整测试失败，请检查错误日志")