#!/usr/bin/env python3
"""
数据库连接检查和修复工具
"""

import pymysql
import sys
import os
from urllib.parse import urlparse

# 从config.py获取数据库URL
DATABASE_URL = "mysql+pymysql://sql23721_hentai:507877550%40lihao@************:3306/sql23721_hentai?charset=utf8mb4"

def parse_database_url(url):
    """解析数据库URL"""
    # 移除SQLAlchemy前缀
    if url.startswith('mysql+pymysql://'):
        url = url.replace('mysql+pymysql://', 'mysql://')
    
    parsed = urlparse(url)
    
    return {
        'host': parsed.hostname,
        'port': parsed.port or 3306,
        'user': parsed.username,
        'password': parsed.password.replace('%40', '@') if parsed.password else None,
        'database': parsed.path.lstrip('/').split('?')[0]
    }

def test_connection(config):
    """测试数据库连接"""
    try:
        print(f"尝试连接数据库: {config['host']}:{config['port']}")
        connection = pymysql.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password=config['password'],
            database=config['database'],
            charset='utf8mb4',
            connect_timeout=10
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✅ 数据库连接成功! MySQL版本: {version[0]}")
            
            # 测试animes表是否存在
            cursor.execute("SHOW TABLES LIKE 'animes'")
            table_exists = cursor.fetchone()
            if table_exists:
                cursor.execute("SELECT COUNT(*) FROM animes")
                count = cursor.fetchone()
                print(f"✅ animes表存在，包含 {count[0]} 条记录")
            else:
                print("⚠️  animes表不存在")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def create_local_config():
    """创建本地SQLite配置"""
    local_config = """import os
from typing import List, Union
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # 数据库配置 - 临时使用SQLite
    DATABASE_URL: str = "sqlite:///./anime_local.db"
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 应用配置
    DEBUG: bool = True
    CORS_ORIGINS: str = "http://************:3000,http://localhost:3000,http://127.0.0.1:3000"
    
    @property
    def cors_origins_list(self) -> List[str]:
        if self.CORS_ORIGINS == "*":
            return ["*"]
        return [origin.strip() for origin in self.CORS_ORIGINS.split(",")]
    
    class Config:
        env_file = ".env"

settings = Settings()"""
    
    backup_path = "app/core/config_backup.py"
    local_path = "app/core/config_local.py"
    
    # 备份原配置
    if os.path.exists("app/core/config.py"):
        with open("app/core/config.py", "r", encoding="utf-8") as f:
            original_config = f.read()
        with open(backup_path, "w", encoding="utf-8") as f:
            f.write(original_config)
        print(f"✅ 原配置已备份到: {backup_path}")
    
    # 创建本地配置
    with open(local_path, "w", encoding="utf-8") as f:
        f.write(local_config)
    print(f"✅ 本地配置已创建: {local_path}")
    
    print("""
要使用本地SQLite数据库，请执行以下步骤:
1. 将 app/core/config.py 重命名为 app/core/config_mysql.py
2. 将 app/core/config_local.py 重命名为 app/core/config.py
3. 运行: python -c "from app.core.database import create_tables; create_tables()"
4. 重启后端服务器

要恢复MySQL配置:
1. 将当前的 app/core/config.py 删除
2. 将 app/core/config_backup.py 重命名为 app/core/config.py
""")

def main():
    print("数据库连接诊断工具")
    print("=" * 40)
    
    # 解析数据库URL
    config = parse_database_url(DATABASE_URL)
    print(f"数据库配置:")
    print(f"  主机: {config['host']}")
    print(f"  端口: {config['port']}")
    print(f"  用户: {config['user']}")
    print(f"  数据库: {config['database']}")
    print()
    
    # 测试连接
    if test_connection(config):
        print("\n✅ 数据库连接正常，搜索功能修复应该没问题")
        print("请重启后端服务器并测试搜索功能")
    else:
        print("\n❌ 数据库连接失败")
        print("\n可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 联系数据库管理员")
        print("3. 使用本地SQLite数据库进行开发")
        
        choice = input("\n是否创建本地SQLite配置? (y/n): ").lower().strip()
        if choice == 'y':
            create_local_config()

if __name__ == "__main__":
    main()