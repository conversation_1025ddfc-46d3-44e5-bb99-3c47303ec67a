@echo off
REM 生产环境文件清理脚本 - Windows 版本
REM 清理多余的开发文件和测试文件，优化部署包大小

echo 🧹 开始清理动漫网站生产环境不需要的文件...
echo =========================================
echo.

REM 1. 删除测试文件
echo 🗑️ 删除测试文件...
for /r %%i in (*.test.js, *.test.ts, *.spec.js, *.spec.ts) do @del "%%i" 2>nul
for /r %%i in (*test*.py) do @del "%%i" 2>nul

REM 删除测试目录和配置文件
rmdir /s /q frontend\tests 2>nul
rmdir /s /q frontend\__tests__ 2>nul  
rmdir /s /q backend\tests 2>nul
del frontend\jest.config.js 2>nul
del frontend\jest.setup.js 2>nul
del frontend\playwright.config.ts 2>nul
del frontend\.eslintrc.json 2>nul
del backend\pytest.ini 2>nul
del backend\.coverage 2>nul

echo ✅ 测试文件清理完成

REM 2. 删除开发文件和工具
echo 🗑️ 删除开发工具文件...
rmdir /s /q .vscode 2>nul
rmdir /s /q .idea 2>nul
del .DS_Store 2>nul
for /r %%i in (.DS_Store, Thumbs.db) do @del "%%i" 2>nul
del *.log 2>nul
rmdir /s /q logs 2>nul

echo ✅ 开发工具文件清理完成

REM 3. 删除 Docker 文件（用户要求不使用 Docker）
echo 🗑️ 删除 Docker 相关文件...
del docker-compose.yml 2>nul
del docker-compose.prod.yml 2>nul
del Dockerfile 2>nul
del frontend\Dockerfile* 2>nul
del backend\Dockerfile* 2>nul
del .dockerignore 2>nul

echo ✅ Docker 文件清理完成

REM 4. 删除不必要的配置和文档文件
echo 🗑️ 删除不必要的配置文件...
del .gitignore 2>nul
del README.md 2>nul

echo 📄 保留关键部署文档 (PRODUCTION-DEPLOYMENT.md)
echo ✅ 配置文件清理完成

REM 5. 清理 Node.js 缓存和临时文件
echo 🗑️ 清理 Node.js 缓存...
cd frontend 2>nul && (
    rmdir /s /q node_modules\.cache 2>nul
    rmdir /s /q .next\cache 2>nul
    rmdir /s /q .next\static\chunks 2>nul
    npm cache clean --force 2>nul
    cd ..
)

echo ✅ Node.js 缓存清理完成

REM 6. 清理 Python 缓存
echo 🗑️ 清理 Python 缓存...
for /r %%i in (*.pyc, *.pyo) do @del "%%i" 2>nul
for /d /r %%i in (__pycache__) do @rmdir /s /q "%%i" 2>nul

echo ✅ Python 缓存清理完成

REM 7. 删除开发用的示例数据和脚本
echo 🗑️ 删除开发脚本和示例数据...
rmdir /s /q backend\scripts 2>nul
del backend\create_sample_data.py 2>nul
del backend\recreate_db.py 2>nul
del backend\test_*.py 2>nul
del backend\debug_*.py 2>nul

echo ✅ 开发脚本清理完成

REM 8. 清理前端构建产物中的开发文件
echo 🗑️ 清理前端开发构建文件...
cd frontend 2>nul && (
    rmdir /s /q .next\trace 2>nul
    rmdir /s /q .next\static\development 2>nul
    cd ..
)

echo ✅ 前端开发文件清理完成

echo.
echo 🎉 生产环境文件清理完成！
echo =========================================
echo 📋 清理总结：
echo ✅ 删除了测试文件和测试配置
echo ✅ 删除了开发工具配置文件
echo ✅ 删除了 Docker 相关文件
echo ✅ 删除了缓存和临时文件
echo ✅ 删除了开发脚本和示例数据
echo.
echo 🚀 现在可以进行生产环境部署了！
echo 📖 请参考 PRODUCTION-DEPLOYMENT.md 进行部署
echo.
pause