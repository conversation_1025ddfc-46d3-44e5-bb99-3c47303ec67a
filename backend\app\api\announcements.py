from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.deps import get_current_admin_user
from app.models import User, Announcement
from pydantic import BaseModel
from datetime import datetime

router = APIRouter()

# Schema定义
class AnnouncementCreate(BaseModel):
    title: str
    content: str
    type: str
    target: str
    is_active: bool = True
    sent_count: Optional[int] = 0
    display_frequency: str = "once"  # once, daily, weekly, always
    auto_dismiss: bool = True
    auto_dismiss_seconds: int = 5

class AnnouncementResponse(BaseModel):
    id: int
    title: str
    content: str
    type: str
    target: str
    is_active: bool
    sent_count: int
    display_frequency: str
    auto_dismiss: bool
    auto_dismiss_seconds: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

@router.get("/", response_model=List[AnnouncementResponse], summary="获取公告列表")
def get_announcements(
    filter: str = Query("all", description="筛选类型: all, active, inactive"),
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    query = db.query(Announcement)
    
    if filter == "active":
        query = query.filter(Announcement.is_active == True)
    elif filter == "inactive":
        query = query.filter(Announcement.is_active == False)
    
    announcements = query.order_by(Announcement.created_at.desc()).all()
    return announcements

@router.get("/{announcement_id}", response_model=AnnouncementResponse, summary="获取公告详情")
def get_announcement(
    announcement_id: int,
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    announcement = db.query(Announcement).filter(Announcement.id == announcement_id).first()
    if not announcement:
        raise HTTPException(status_code=404, detail="公告不存在")
    return announcement

@router.post("/", response_model=AnnouncementResponse, summary="创建公告")
def create_announcement(
    announcement: AnnouncementCreate,
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    db_announcement = Announcement(
        title=announcement.title,
        content=announcement.content,
        type=announcement.type,
        target=announcement.target,
        is_active=announcement.is_active,
        sent_count=announcement.sent_count or 0,
        display_frequency=announcement.display_frequency,
        auto_dismiss=announcement.auto_dismiss,
        auto_dismiss_seconds=announcement.auto_dismiss_seconds
    )
    db.add(db_announcement)
    db.commit()
    db.refresh(db_announcement)
    return db_announcement

@router.put("/{announcement_id}", response_model=AnnouncementResponse, summary="更新公告")
def update_announcement(
    announcement_id: int,
    announcement: AnnouncementCreate,
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    db_announcement = db.query(Announcement).filter(Announcement.id == announcement_id).first()
    if not db_announcement:
        raise HTTPException(status_code=404, detail="公告不存在")
    
    db_announcement.title = announcement.title
    db_announcement.content = announcement.content
    db_announcement.type = announcement.type
    db_announcement.target = announcement.target
    db_announcement.is_active = announcement.is_active
    db_announcement.display_frequency = announcement.display_frequency
    db_announcement.auto_dismiss = announcement.auto_dismiss
    db_announcement.auto_dismiss_seconds = announcement.auto_dismiss_seconds
    if announcement.sent_count is not None:
        db_announcement.sent_count = announcement.sent_count
    
    db.commit()
    db.refresh(db_announcement)
    return db_announcement

@router.delete("/{announcement_id}", summary="删除公告")
def delete_announcement(
    announcement_id: int,
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    db_announcement = db.query(Announcement).filter(Announcement.id == announcement_id).first()
    if not db_announcement:
        raise HTTPException(status_code=404, detail="公告不存在")
    
    db.delete(db_announcement)
    db.commit()
    return {"message": "公告已删除"}

@router.post("/{announcement_id}/toggle", response_model=AnnouncementResponse, summary="切换公告状态")
def toggle_announcement(
    announcement_id: int,
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    db_announcement = db.query(Announcement).filter(Announcement.id == announcement_id).first()
    if not db_announcement:
        raise HTTPException(status_code=404, detail="公告不存在")
    
    db_announcement.is_active = not db_announcement.is_active
    db.commit()
    db.refresh(db_announcement)
    return db_announcement

# 公开API - 获取活动的弹窗公告
@router.get("/popup/active", response_model=Optional[AnnouncementResponse], summary="获取活动弹窗公告")
def get_active_popup_announcement(
    db: Session = Depends(get_db)
):
    """获取当前活动的弹窗公告（供前端展示）"""
    # 获取所有活动的弹窗公告
    announcements = db.query(Announcement).filter(
        Announcement.type == "popup",
        Announcement.is_active == True
    ).order_by(Announcement.created_at.desc()).all()
    
    # 返回第一个适用的公告
    # 优先级：all > guests > logged_in
    for target in ['all', 'guests', 'logged_in']:
        for announcement in announcements:
            if announcement.target == target:
                return announcement
    
    return None