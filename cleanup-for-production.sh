#!/bin/bash
# 生产环境文件清理脚本 - Linux/Mac 版本
# 清理多余的开发文件和测试文件，优化部署包大小

echo "🧹 开始清理动漫网站生产环境不需要的文件..."
echo "========================================="

# 记录清理前的目录大小
echo "📊 清理前项目大小:"
du -sh . 2>/dev/null || echo "无法计算目录大小"
echo ""

# 1. 删除测试文件
echo "🗑️  删除测试文件..."
find . -name "*.test.js" -type f -delete 2>/dev/null
find . -name "*.test.ts" -type f -delete 2>/dev/null
find . -name "*.spec.js" -type f -delete 2>/dev/null
find . -name "*.spec.ts" -type f -delete 2>/dev/null
find . -name "*test*.py" -type f -delete 2>/dev/null
find . -path "*/tests/*" -type f -delete 2>/dev/null
find . -path "*/__tests__/*" -type f -delete 2>/dev/null

# 删除测试配置文件
rm -f frontend/jest.config.js
rm -f frontend/jest.setup.js  
rm -f frontend/playwright.config.ts
rm -f frontend/.eslintrc.json
rm -rf frontend/tests/
rm -rf frontend/__tests__/
rm -rf backend/tests/
rm -f backend/pytest.ini
rm -f backend/.coverage

echo "✅ 测试文件清理完成"

# 2. 删除开发文件和工具
echo "🗑️  删除开发工具文件..."
rm -rf .vscode/
rm -rf .idea/
rm -f .DS_Store
find . -name ".DS_Store" -delete 2>/dev/null
find . -name "Thumbs.db" -delete 2>/dev/null
rm -f *.log
rm -rf logs/

echo "✅ 开发工具文件清理完成"

# 3. 删除 Docker 文件（用户要求不使用 Docker）
echo "🗑️  删除 Docker 相关文件..."
rm -f docker-compose.yml
rm -f docker-compose.prod.yml
rm -f Dockerfile
rm -f frontend/Dockerfile*
rm -f backend/Dockerfile*
rm -f .dockerignore

echo "✅ Docker 文件清理完成"

# 4. 删除不必要的配置和文档文件
echo "🗑️  删除不必要的配置文件..."
rm -f .gitignore  # 生产环境不需要
rm -f README.md   # 可选删除
rm -f *.md        # 删除其他 markdown 文档（保留关键文档）

# 保留重要文档
echo "📄 保留关键部署文档..."
# 这些文档在清理时会被保留
# - PRODUCTION-DEPLOYMENT.md (部署文档)

echo "✅ 配置文件清理完成"

# 5. 清理 Node.js 缓存和临时文件
echo "🗑️  清理 Node.js 缓存..."
cd frontend 2>/dev/null && {
    rm -rf node_modules/.cache/
    rm -rf .next/cache/
    rm -rf .next/static/chunks/
    npm cache clean --force 2>/dev/null || echo "npm 缓存清理跳过"
    cd ..
}

echo "✅ Node.js 缓存清理完成"

# 6. 清理 Python 缓存
echo "🗑️  清理 Python 缓存..."
find . -name "*.pyc" -delete 2>/dev/null
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null
find . -name "*.pyo" -delete 2>/dev/null

echo "✅ Python 缓存清理完成"

# 7. 删除开发用的示例数据和脚本
echo "🗑️  删除开发脚本和示例数据..."
rm -rf backend/scripts/ 2>/dev/null
rm -f backend/create_sample_data.py 2>/dev/null
rm -f backend/recreate_db.py 2>/dev/null
rm -f backend/test_*.py 2>/dev/null
rm -f backend/debug_*.py 2>/dev/null

echo "✅ 开发脚本清理完成"

# 8. 清理前端构建产物中的开发文件
echo "🗑️  清理前端开发构建文件..."
cd frontend 2>/dev/null && {
    rm -rf .next/trace/ 2>/dev/null
    rm -rf .next/static/development/ 2>/dev/null
    cd ..
}

echo "✅ 前端开发文件清理完成"

# 记录清理后的目录大小
echo ""
echo "📊 清理后项目大小:"
du -sh . 2>/dev/null || echo "无法计算目录大小"

echo ""
echo "🎉 生产环境文件清理完成！"
echo "========================================="
echo "📋 清理总结："
echo "✅ 删除了测试文件和测试配置"
echo "✅ 删除了开发工具配置文件" 
echo "✅ 删除了 Docker 相关文件"
echo "✅ 删除了缓存和临时文件"
echo "✅ 删除了开发脚本和示例数据"
echo ""
echo "🚀 现在可以进行生产环境部署了！"
echo "📖 请参考 PRODUCTION-DEPLOYMENT.md 进行部署"