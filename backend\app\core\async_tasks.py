"""
异步任务处理器 - 用于处理非关键性任务，如浏览次数更新
"""
import asyncio
import threading
import time
from queue import Queue
from typing import Callable, Any
from sqlalchemy.orm import sessionmaker
from app.core.database import engine

class AsyncTaskQueue:
    def __init__(self):
        self.task_queue = Queue()
        self.worker_thread = None
        self.should_stop = False
        self._start_worker()
    
    def _start_worker(self):
        """启动后台工作线程"""
        self.worker_thread = threading.Thread(target=self._worker, daemon=True)
        self.worker_thread.start()
    
    def _worker(self):
        """后台工作线程主循环"""
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        while not self.should_stop:
            try:
                if not self.task_queue.empty():
                    task = self.task_queue.get(timeout=1)
                    db = SessionLocal()
                    try:
                        task['func'](db, *task['args'], **task['kwargs'])
                    except Exception as e:
                        # 记录错误但不抛出，避免影响其他任务
                        print(f"异步任务执行失败: {e}")
                    finally:
                        db.close()
                        self.task_queue.task_done()
                else:
                    time.sleep(0.1)  # 避免CPU占用过高
            except Exception as e:
                print(f"异步任务队列错误: {e}")
                time.sleep(1)
    
    def add_task(self, func: Callable, *args, **kwargs):
        """添加任务到队列"""
        task = {
            'func': func,
            'args': args,
            'kwargs': kwargs,
            'timestamp': time.time()
        }
        self.task_queue.put(task)
    
    def stop(self):
        """停止任务队列"""
        self.should_stop = True
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
    
    def get_queue_size(self) -> int:
        """获取队列大小"""
        return self.task_queue.qsize()

# 全局异步任务队列
async_task_queue = AsyncTaskQueue()

def async_increment_view_count(db, manga_id: int):
    """异步增加浏览次数"""
    from app.crud.manga import MangaCRUD
    try:
        MangaCRUD.increment_view_count(db, manga_id)
    except Exception as e:
        print(f"异步更新浏览次数失败 (manga_id: {manga_id}): {e}")

def queue_view_count_update(manga_id: int):
    """队列浏览次数更新任务"""
    async_task_queue.add_task(async_increment_view_count, manga_id)