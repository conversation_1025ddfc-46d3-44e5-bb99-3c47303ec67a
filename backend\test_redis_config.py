"""
测试Redis动态配置功能
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import SessionLocal
from app.core.redis_config import RedisConfigManager
from app.core.redis_client import redis_client
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_redis_config():
    """测试Redis配置管理功能"""
    
    print("\n" + "="*50)
    print("Redis动态配置测试")
    print("="*50 + "\n")
    
    db = SessionLocal()
    
    try:
        # 1. 初始化默认配置
        print("1. 初始化默认配置...")
        RedisConfigManager.initialize_default_config(db)
        print("   [OK] 默认配置已初始化")
        
        # 2. 获取当前配置
        print("\n2. 获取当前配置...")
        config = RedisConfigManager.get_all_config(db)
        print(f"   启用状态: {config['redis_enabled']}")
        print(f"   服务器: {config['redis_host']}:{config['redis_port']}")
        print(f"   数据库: {config['redis_db']}")
        print(f"   最大内存: {config['redis_max_memory']} MB")
        print(f"   默认过期时间: {config['redis_expire_time']} 秒")
        
        # 3. 测试启用Redis
        print("\n3. 启用Redis缓存...")
        RedisConfigManager.update_configs(db, {
            'redis_enabled': True,
            'redis_host': '***********',
            'redis_port': 6379,
            'redis_db': 0,
            'redis_max_memory': 100  # 100MB限制
        })
        print("   [OK] 配置已更新")
        
        # 4. 初始化Redis连接
        print("\n4. 初始化Redis连接...")
        await redis_client.init_redis(force_reconnect=True)
        if redis_client.is_available:
            print("   [OK] Redis连接成功")
        else:
            print("   [FAIL] Redis连接失败")
        
        # 5. 测试基本操作
        if redis_client.is_available:
            print("\n5. 测试基本缓存操作...")
            
            # 设置缓存
            test_key = "test:dynamic:config"
            test_value = {"message": "Redis动态配置测试成功", "timestamp": "2025-09-08"}
            
            success = await redis_client.set(test_key, test_value, 60)
            if success:
                print(f"   [OK] 设置缓存成功: {test_key}")
            else:
                print(f"   [FAIL] 设置缓存失败")
            
            # 获取缓存
            cached_value = await redis_client.get(test_key)
            if cached_value:
                print(f"   [OK] 获取缓存成功: {cached_value}")
            else:
                print(f"   [FAIL] 获取缓存失败")
            
            # 获取内存信息
            memory_info = await redis_client.get_memory_info()
            if memory_info:
                print(f"\n6. Redis内存状态:")
                print(f"   已用内存: {memory_info['used_memory_human']}")
                print(f"   内存限制: {memory_info['maxmemory_human']}")
                print(f"   淘汰策略: {memory_info['maxmemory_policy']}")
            
            # 删除测试键
            await redis_client.delete(test_key)
            print(f"\n   [OK] 清理测试数据")
        
        # 7. 测试动态修改配置
        print("\n7. 测试动态修改配置...")
        print("   修改内存限制为200MB...")
        RedisConfigManager.update_configs(db, {
            'redis_max_memory': 200
        })
        
        # 重新初始化连接以应用新配置
        await redis_client.init_redis(force_reconnect=False)
        
        # 验证新配置
        memory_info = await redis_client.get_memory_info()
        if memory_info:
            print(f"   新的内存限制: {memory_info['maxmemory_human']}")
        
        # 8. 测试禁用Redis
        print("\n8. 测试禁用Redis...")
        RedisConfigManager.update_configs(db, {
            'redis_enabled': False
        })
        await redis_client.init_redis(force_reconnect=True)
        
        if not redis_client.is_available:
            print("   [OK] Redis已成功禁用")
        else:
            print("   [FAIL] Redis禁用失败")
        
        print("\n" + "="*50)
        print("测试完成！Redis动态配置功能正常")
        print("="*50 + "\n")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        print(f"\n[ERROR] 测试失败: {e}")
    finally:
        db.close()
        await redis_client.close_redis()

if __name__ == "__main__":
    asyncio.run(test_redis_config())