# 配置指南 (Configuration Guide)

## 配置文件详解

### config.yml 完整配置

```yaml
# ========== 全局配置 ==========
app:
  name: "Hanime1.me 视频抓取系统"
  version: "2.0.0"
  author: "AI Assistant"

# ========== 网络配置 ==========
network:
  # 代理配置 (可选)
  proxy:
    enabled: false  # 是否启用代理
    http: "http://proxy:port"
    https: "http://proxy:port"
    socks5: "socks5://proxy:port"
  
  # 请求配置
  timeout: 30           # 请求超时时间(秒)
  max_retries: 3        # 最大重试次数
  retry_delay: 1        # 重试延迟(秒)
  
  # 用户代理
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

# ========== 爬取配置 ==========
crawl:
  # 目标网站
  base_url: "https://hanime1.me"
  
  # 日期过滤 - 指定要爬取的年月
  date_filter:
    year: 2025    # 目标年份
    month: 1      # 目标月份 (1-12)
  
  # 搜索配置
  search:
    genre: "%E8%A3%8F%E7%95%AA"  # 里番类型URL编码
    sort: ""                       # 排序方式
    type: ""                       # 视频类型
  
  # 质量优先级 (从高到低尝试)
  quality_priority:
    - "1080"  # 1080p优先
    - "720"   # 720p其次
    - "480"   # 480p最后
  
  # 跳过关键词 - 包含这些词的视频将被跳过
  skip_keywords:
    - "中字後補"
    - "简中补字"
    - "Chinese Sub"  
    - "中文字幕後補"

# ========== 下载配置 ==========
download:
  # 目录配置
  download_dir: "downloads"       # 下载根目录
  temp_dir: "temp"               # 临时文件目录
  organize_by_date: true         # 按日期组织 (年/月/标题)
  
  # 下载开关
  enable_video: true             # 是否下载视频
  enable_cover: true             # 是否下载封面
  enable_fanart: true            # 是否下载剧照
  
  # 性能配置
  chunk_size: 8192              # 下载块大小(字节)
  max_concurrent: 3             # 最大并发下载数
  clean_filename: true          # 清理文件名非法字符

# ========== Selenium配置 ==========
selenium:
  # Chrome选项
  headless: true                # 无头模式
  no_sandbox: true              # 禁用沙箱
  disable_dev_shm: true         # 禁用/dev/shm使用
  
  # 等待时间
  implicit_wait: 5              # 隐式等待(秒)
  page_load_timeout: 30         # 页面加载超时(秒)

# ========== 刮削配置 ==========
scrape:
  # 刮削模式
  mode: "online"                # online: 在线搜索
  
  # 搜索站点
  search_sites:
    hanime1: true               # 启用hanime1.me搜索
  
  # NFO文件
  nfo:
    enabled: true               # 生成NFO文件
    encoding: "utf-8"           # NFO编码
  
  # 图片下载
  images:
    fanart: true                # 下载fanart图片
    poster: true                # 下载poster图片
    thumb: false                # 下载缩略图
  
  # 合集检测
  collection:
    enabled: true               # 启用合集检测

# ========== 重命名配置 ==========
rename:
  enabled: true                 # 启用重命名
  template: "{title}"           # 重命名模板
  remove_brackets: true         # 移除方括号内容
  clean_special_chars: true     # 清理特殊字符

# ========== 日志配置 ==========
logging:
  level: "INFO"                 # 日志级别
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "scraper.log"           # 日志文件
  max_size: "10MB"              # 最大大小
  backup_count: 5               # 备份数量

# ========== 性能配置 ==========
performance:
  http2: true                   # 启用HTTP/2
  connection_pool_size: 10      # 连接池大小
  max_redirects: 10             # 最大重定向次数
  gc_threshold: 100             # 垃圾回收阈值

# ========== 数据库配置 ==========
database:
  host: "************:3306"      # MariaDB数据库主机:端口
  user: "sql23721_hentai"        # 数据库用户名
  password: "507877550@lihao"    # 数据库密码
  database: "sql23721_hentai"    # 数据库名
  charset: "utf8mb4"             # 字符集

# ========== Web访问配置 ==========
web_access:
  domain_prefix: 'https://static.denlu.top'  # 域名前缀
  base_path: '/downloads'                     # 基础路径
```

## 配置说明

### 📅 日期筛选

```yaml
date_filter:
  year: 2025    # 爬取2025年的视频
  month: 1      # 爬取1月份的视频
  # month: null # 设为null表示爬取整年
```

### 🎯 质量优先级

系统会按照配置的优先级顺序查找视频源：

```yaml
quality_priority:
  - "1080"  # 首先尝试1080p
  - "720"   # 没有1080p则尝试720p
  - "480"   # 最后尝试480p
```

### 🚫 跳过规则

包含指定关键词的视频会被自动跳过：

```yaml
skip_keywords:
  - "中字後補"    # 跳过补字版本
  - "简中补字"    # 跳过简体中文补字
  - "Chinese Sub" # 跳过中文字幕版
```

### 🌐 URL生成

Web访问配置决定了数据库中存储的完整URL格式：

```yaml
web_access:
  domain_prefix: 'https://static.denlu.top'
  base_path: '/downloads'
```

生成URL格式：
- 视频: `{domain_prefix}{base_path}/{year}/{month:02d}/{title}/{title}.mp4`
- 剧照: `{domain_prefix}{base_path}/{year}/{month:02d}/{title}/extrafanart/fanart{n}.jpg`

### 🗄️ 数据库连接

配置MariaDB数据库连接信息：

```yaml
database:
  host: "************:3306"      # MariaDB服务器:端口
  user: "sql23721_hentai"        # 数据库用户名  
  password: "507877550@lihao"    # 数据库密码
  database: "sql23721_hentai"    # 数据库名称
  charset: "utf8mb4"             # 字符集(支持emoji)
```

## 环境变量支持

可以通过环境变量覆盖配置：

```bash
# 数据库配置
export DB_HOST="************:3306"
export DB_USER="sql23721_hentai"
export DB_PASSWORD="507877550@lihao"
export DB_DATABASE="sql23721_hentai"

# 下载目录
export DOWNLOAD_DIR=/path/to/downloads

# 目标日期
export TARGET_YEAR=2025
export TARGET_MONTH=1
```

## 配置验证

运行配置验证脚本：

```python
from unified_crawler import UnifiedCrawler

# 加载并验证配置
try:
    crawler = UnifiedCrawler("config.yml")
    print("✅ 配置文件加载成功")
    print(f"目标日期: {crawler.TARGET_YEAR}/{crawler.TARGET_MONTHS[0]:02d}")
    print(f"下载目录: {crawler.DOWNLOAD_DIR}")
    print(f"数据库: {crawler.db_config['host']}:{crawler.db_config['port']}/{crawler.db_config['database']}")
except Exception as e:
    print(f"❌ 配置错误: {e}")
```

## 常用配置场景

### 场景1: 开发测试
```yaml
crawl:
  date_filter:
    year: 2025
    month: 1
download:
  download_dir: "test_downloads"
logging:
  level: "DEBUG"
```

### 场景2: 生产环境
```yaml
crawl:
  date_filter:
    year: 2025
    month: null  # 爬取整年
download:
  max_concurrent: 5
  download_dir: "/var/www/static/downloads"
logging:
  level: "INFO"  
  file: "/var/log/crawler.log"
```

### 场景3: 代理环境
```yaml
network:
  proxy:
    enabled: true
    http: "http://proxy.company.com:8080"
    https: "http://proxy.company.com:8080"
  timeout: 60
```

---

**注意**: 修改配置后需要重启爬虫程序使配置生效。