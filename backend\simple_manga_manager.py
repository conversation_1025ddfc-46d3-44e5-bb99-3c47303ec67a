#!/usr/bin/env python3
"""
漫画数据管理工具 - 修正版
基于实际数据库结构，避免模型字段不匹配问题
"""

import sys
import os
from sqlalchemy import create_engine, text
from datetime import datetime
import argparse

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings

class SimpleMangaManager:
    def __init__(self):
        self.engine = create_engine(settings.DATABASE_URL, pool_pre_ping=True, echo=False)
    
    def search_manga(self, keyword):
        """搜索漫画"""
        with self.engine.connect() as conn:
            result = conn.execute(
                text("""
                    SELECT id, title, author, status 
                    FROM mangas 
                    WHERE title LIKE :keyword OR author LIKE :keyword
                    ORDER BY id
                """),
                {"keyword": f"%{keyword}%"}
            )
            
            mangas = result.fetchall()
            if mangas:
                print(f"找到 {len(mangas)} 部匹配的漫画:")
                for manga in mangas:
                    print(f"  ID:{manga.id} - {manga.title} (作者: {manga.author or '未知'}, 状态: {manga.status})")
            else:
                print("未找到匹配的漫画")
            return mangas
    
    def get_manga_stats(self, manga_id):
        """获取漫画统计信息"""
        with self.engine.connect() as conn:
            # 基本信息
            manga_result = conn.execute(
                text("SELECT id, title, author, status, chapter_count FROM mangas WHERE id = :id"),
                {"id": manga_id}
            )
            manga = manga_result.fetchone()
            
            if not manga:
                return None
            
            # 章节数
            chapters_result = conn.execute(
                text("SELECT COUNT(*) FROM manga_chapters WHERE manga_id = :manga_id"),
                {"manga_id": manga_id}
            )
            chapters_count = chapters_result.scalar()
            
            # 页面数
            pages_result = conn.execute(
                text("""
                    SELECT COUNT(*) FROM manga_pages mp
                    JOIN manga_chapters mc ON mp.chapter_id = mc.id
                    WHERE mc.manga_id = :manga_id
                """),
                {"manga_id": manga_id}
            )
            pages_count = pages_result.scalar()
            
            # 收藏数
            favorites_result = conn.execute(
                text("SELECT COUNT(*) FROM favorites WHERE content_type = 'manga' AND manga_id = :manga_id"),
                {"manga_id": manga_id}
            )
            favorites_count = favorites_result.scalar()
            
            # 评论数
            comments_result = conn.execute(
                text("SELECT COUNT(*) FROM comments WHERE content_type = 'manga' AND manga_id = :manga_id"),
                {"manga_id": manga_id}
            )
            comments_count = comments_result.scalar()
            
            # 阅读进度数
            progress_result = conn.execute(
                text("SELECT COUNT(*) FROM manga_reading_progress WHERE manga_id = :manga_id"),
                {"manga_id": manga_id}
            )
            progress_count = progress_result.scalar()
            
            # 标签数
            tags_result = conn.execute(
                text("SELECT COUNT(*) FROM manga_tags WHERE manga_id = :manga_id"),
                {"manga_id": manga_id}
            )
            tags_count = tags_result.scalar()
            
            return {
                'manga': manga,
                'chapters': chapters_count,
                'pages': pages_count,
                'favorites': favorites_count,
                'comments': comments_count,
                'reading_progress': progress_count,
                'tags': tags_count
            }
    
    def show_manga_details(self, manga_id):
        """显示漫画详情"""
        stats = self.get_manga_stats(manga_id)
        if not stats:
            print(f"未找到ID为 {manga_id} 的漫画")
            return False
        
        manga = stats['manga']
        print(f"\n漫画详情:")
        print(f"  ID: {manga.id}")
        print(f"  标题: {manga.title}")
        print(f"  作者: {manga.author or '未知'}")
        print(f"  状态: {manga.status}")
        print(f"  记录的章节数: {manga.chapter_count}")
        
        print(f"\n相关数据统计:")
        print(f"  实际章节数: {stats['chapters']}")
        print(f"  页面总数: {stats['pages']}")
        print(f"  标签关联: {stats['tags']}")
        print(f"  收藏人数: {stats['favorites']}")
        print(f"  评论数量: {stats['comments']}")
        print(f"  阅读记录: {stats['reading_progress']}")
        
        return True
    
    def delete_manga_safe(self, manga_id, confirm=True):
        """安全删除漫画（使用原生SQL避免模型问题）"""
        if not self.show_manga_details(manga_id):
            return False
        
        if confirm:
            response = input("\n确认删除这部漫画及其所有相关数据吗？(输入 'YES' 确认): ")
            if response != 'YES':
                print("取消删除操作")
                return False
        
        print(f"\n开始删除漫画 ID:{manga_id} 的所有相关数据...")
        
        try:
            with self.engine.begin() as conn:  # 使用事务
                delete_counts = {}
                
                # 1. 删除页面
                result = conn.execute(
                    text("""
                        DELETE mp FROM manga_pages mp
                        JOIN manga_chapters mc ON mp.chapter_id = mc.id
                        WHERE mc.manga_id = :manga_id
                    """),
                    {"manga_id": manga_id}
                )
                delete_counts['pages'] = result.rowcount
                
                # 2. 删除章节
                result = conn.execute(
                    text("DELETE FROM manga_chapters WHERE manga_id = :manga_id"),
                    {"manga_id": manga_id}
                )
                delete_counts['chapters'] = result.rowcount
                
                # 3. 删除标签关联
                result = conn.execute(
                    text("DELETE FROM manga_tags WHERE manga_id = :manga_id"),
                    {"manga_id": manga_id}
                )
                delete_counts['tags'] = result.rowcount
                
                # 4. 删除阅读进度
                result = conn.execute(
                    text("DELETE FROM manga_reading_progress WHERE manga_id = :manga_id"),
                    {"manga_id": manga_id}
                )
                delete_counts['reading_progress'] = result.rowcount
                
                # 5. 删除收藏
                result = conn.execute(
                    text("DELETE FROM favorites WHERE content_type = 'manga' AND manga_id = :manga_id"),
                    {"manga_id": manga_id}
                )
                delete_counts['favorites'] = result.rowcount
                
                # 6. 删除评论
                result = conn.execute(
                    text("DELETE FROM comments WHERE content_type = 'manga' AND manga_id = :manga_id"),
                    {"manga_id": manga_id}
                )
                delete_counts['comments'] = result.rowcount
                
                # 7. 最后删除漫画本身
                result = conn.execute(
                    text("DELETE FROM mangas WHERE id = :manga_id"),
                    {"manga_id": manga_id}
                )
                delete_counts['manga'] = result.rowcount
                
                print(f"\n删除完成！统计:")
                print(f"  漫画记录: {delete_counts['manga']} 条")
                print(f"  章节记录: {delete_counts['chapters']} 条") 
                print(f"  页面记录: {delete_counts['pages']} 条")
                print(f"  标签关联: {delete_counts['tags']} 条")
                print(f"  阅读进度: {delete_counts['reading_progress']} 条")
                print(f"  收藏记录: {delete_counts['favorites']} 条")
                print(f"  评论记录: {delete_counts['comments']} 条")
                
                total = sum(delete_counts.values())
                print(f"\n总计删除: {total} 条记录")
                
                return True
                
        except Exception as e:
            print(f"删除失败: {e}")
            return False
    
    def interactive_menu(self):
        """交互式菜单"""
        while True:
            print("\n=== 漫画数据管理工具 ===")
            print("1. 搜索漫画")
            print("2. 查看漫画详情")
            print("3. 删除漫画")
            print("4. 退出")
            
            choice = input("\n请选择操作 (1-4): ").strip()
            
            if choice == '1':
                keyword = input("输入搜索关键词: ").strip()
                if keyword:
                    self.search_manga(keyword)
            elif choice == '2':
                try:
                    manga_id = int(input("输入漫画ID: ").strip())
                    self.show_manga_details(manga_id)
                except ValueError:
                    print("请输入有效的数字ID")
            elif choice == '3':
                try:
                    manga_id = int(input("输入要删除的漫画ID: ").strip())
                    self.delete_manga_safe(manga_id)
                except ValueError:
                    print("请输入有效的数字ID")
            elif choice == '4':
                print("退出程序")
                break
            else:
                print("无效选择，请重新输入")

def main():
    parser = argparse.ArgumentParser(description='漫画数据管理工具')
    parser.add_argument('--search', '-s', help='搜索漫画')
    parser.add_argument('--details', '-d', type=int, help='查看漫画详情')
    parser.add_argument('--delete', type=int, help='删除漫画')
    parser.add_argument('--no-confirm', action='store_true', help='删除时跳过确认')
    
    args = parser.parse_args()
    manager = SimpleMangaManager()
    
    if args.search:
        manager.search_manga(args.search)
    elif args.details:
        manager.show_manga_details(args.details)
    elif args.delete:
        manager.delete_manga_safe(args.delete, confirm=not args.no_confirm)
    else:
        manager.interactive_menu()

if __name__ == "__main__":
    main()