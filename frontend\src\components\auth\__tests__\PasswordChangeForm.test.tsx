/**
 * @jest-environment jsdom
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { apiClient } from '@/lib/api'
import PasswordChangeForm from '../PasswordChangeForm'

// Mock the API client
jest.mock('@/lib/api')
const mockApiClient = apiClient as jest.Mocked<typeof apiClient>

// Mock toast notifications
const mockSetToastMessage = jest.fn()

describe('PasswordChangeForm', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockApiClient.changePassword.mockClear()
  })

  const defaultProps = {
    passwordForm: {
      old_password: '',
      new_password: '',
      confirm_password: '',
    },
    setPasswordForm: jest.fn(),
    showPasswords: {
      old: false,
      new: false,
      confirm: false,
    },
    setShowPasswords: jest.fn(),
    passwordLoading: false,
    setToastMessage: mockSetToastMessage,
  }

  it('renders password change form with all fields', () => {
    render(<PasswordChangeForm {...defaultProps} />)

    expect(screen.getByLabelText('当前密码')).toBeInTheDocument()
    expect(screen.getByLabelText('新密码')).toBeInTheDocument()
    expect(screen.getByLabelText('确认新密码')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /修改密码/ })).toBeInTheDocument()
  })

  it('displays password requirements', () => {
    render(<PasswordChangeForm {...defaultProps} />)

    expect(screen.getByText('密码必须至少包含6个字符，包括至少一个字母和一个数字')).toBeInTheDocument()
  })

  it('handles input changes correctly', async () => {
    const mockSetPasswordForm = jest.fn()
    const props = {
      ...defaultProps,
      setPasswordForm: mockSetPasswordForm,
    }

    render(<PasswordChangeForm {...props} />)

    const oldPasswordInput = screen.getByLabelText('当前密码')
    await userEvent.type(oldPasswordInput, 'oldpass123')

    expect(mockSetPasswordForm).toHaveBeenCalledWith(
      expect.objectContaining({
        old_password: 'oldpass123',
      })
    )
  })

  it('toggles password visibility', async () => {
    const mockSetShowPasswords = jest.fn()
    const props = {
      ...defaultProps,
      setShowPasswords: mockSetShowPasswords,
    }

    render(<PasswordChangeForm {...props} />)

    const toggleButtons = screen.getAllByRole('button', { name: '' }) // Eye icons have no text
    await userEvent.click(toggleButtons[0]) // Click first toggle (old password)

    expect(mockSetShowPasswords).toHaveBeenCalledWith(
      expect.objectContaining({
        old: true,
      })
    )
  })

  it('shows validation error for empty fields', async () => {
    render(<PasswordChangeForm {...defaultProps} />)

    const submitButton = screen.getByRole('button', { name: /修改密码/ })
    await userEvent.click(submitButton)

    expect(mockSetToastMessage).toHaveBeenCalledWith({
      message: '请填写所有密码字段',
      type: 'error',
    })
  })

  it('shows validation error for short password', async () => {
    const props = {
      ...defaultProps,
      passwordForm: {
        old_password: 'oldpass123',
        new_password: '123',
        confirm_password: '123',
      },
    }

    render(<PasswordChangeForm {...props} />)

    const submitButton = screen.getByRole('button', { name: /修改密码/ })
    await userEvent.click(submitButton)

    expect(mockSetToastMessage).toHaveBeenCalledWith({
      message: '新密码至少需要6个字符',
      type: 'error',
    })
  })

  it('shows validation error for password without letter', async () => {
    const props = {
      ...defaultProps,
      passwordForm: {
        old_password: 'oldpass123',
        new_password: '123456',
        confirm_password: '123456',
      },
    }

    render(<PasswordChangeForm {...props} />)

    const submitButton = screen.getByRole('button', { name: /修改密码/ })
    await userEvent.click(submitButton)

    expect(mockSetToastMessage).toHaveBeenCalledWith({
      message: '密码必须包含至少一个字母和一个数字',
      type: 'error',
    })
  })

  it('shows validation error for password without digit', async () => {
    const props = {
      ...defaultProps,
      passwordForm: {
        old_password: 'oldpass123',
        new_password: 'abcdef',
        confirm_password: 'abcdef',
      },
    }

    render(<PasswordChangeForm {...props} />)

    const submitButton = screen.getByRole('button', { name: /修改密码/ })
    await userEvent.click(submitButton)

    expect(mockSetToastMessage).toHaveBeenCalledWith({
      message: '密码必须包含至少一个字母和一个数字',
      type: 'error',
    })
  })

  it('shows validation error for password mismatch', async () => {
    const props = {
      ...defaultProps,
      passwordForm: {
        old_password: 'oldpass123',
        new_password: 'newpass123',
        confirm_password: 'different456',
      },
    }

    render(<PasswordChangeForm {...props} />)

    const submitButton = screen.getByRole('button', { name: /修改密码/ })
    await userEvent.click(submitButton)

    expect(mockSetToastMessage).toHaveBeenCalledWith({
      message: '新密码和确认密码不匹配',
      type: 'error',
    })
  })

  it('submits form successfully with valid data', async () => {
    mockApiClient.changePassword.mockResolvedValue({ message: '密码修改成功' })

    const mockSetPasswordForm = jest.fn()
    const props = {
      ...defaultProps,
      passwordForm: {
        old_password: 'oldpass123',
        new_password: 'newpass456',
        confirm_password: 'newpass456',
      },
      setPasswordForm: mockSetPasswordForm,
    }

    render(<PasswordChangeForm {...props} />)

    const submitButton = screen.getByRole('button', { name: /修改密码/ })
    await userEvent.click(submitButton)

    await waitFor(() => {
      expect(mockApiClient.changePassword).toHaveBeenCalledWith({
        old_password: 'oldpass123',
        new_password: 'newpass456',
        confirm_password: 'newpass456',
      })
    })

    expect(mockSetPasswordForm).toHaveBeenCalledWith({
      old_password: '',
      new_password: '',
      confirm_password: '',
    })

    expect(mockSetToastMessage).toHaveBeenCalledWith({
      message: '密码修改成功',
      type: 'success',
    })
  })

  it('handles API error correctly', async () => {
    const errorMessage = '原密码错误'
    mockApiClient.changePassword.mockRejectedValue(new Error(errorMessage))

    const props = {
      ...defaultProps,
      passwordForm: {
        old_password: 'wrongpass',
        new_password: 'newpass456',
        confirm_password: 'newpass456',
      },
    }

    render(<PasswordChangeForm {...props} />)

    const submitButton = screen.getByRole('button', { name: /修改密码/ })
    await userEvent.click(submitButton)

    await waitFor(() => {
      expect(mockSetToastMessage).toHaveBeenCalledWith({
        message: errorMessage,
        type: 'error',
      })
    })
  })

  it('shows loading state during submission', async () => {
    const props = {
      ...defaultProps,
      passwordLoading: true,
    }

    render(<PasswordChangeForm {...props} />)

    expect(screen.getByText('修改中...')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /修改中.../ })).toBeDisabled()
  })

  it('disables form during loading', async () => {
    const props = {
      ...defaultProps,
      passwordLoading: true,
    }

    render(<PasswordChangeForm {...props} />)

    const oldPasswordInput = screen.getByLabelText('当前密码')
    const newPasswordInput = screen.getByLabelText('新密码')
    const confirmPasswordInput = screen.getByLabelText('确认新密码')
    const submitButton = screen.getByRole('button', { name: /修改中.../ })

    expect(oldPasswordInput).toBeDisabled()
    expect(newPasswordInput).toBeDisabled()
    expect(confirmPasswordInput).toBeDisabled()
    expect(submitButton).toBeDisabled()
  })

  it('validates complex passwords correctly', async () => {
    mockApiClient.changePassword.mockResolvedValue({ message: '密码修改成功' })

    const props = {
      ...defaultProps,
      passwordForm: {
        old_password: 'OldComplex123!',
        new_password: 'NewComplex456@',
        confirm_password: 'NewComplex456@',
      },
    }

    render(<PasswordChangeForm {...props} />)

    const submitButton = screen.getByRole('button', { name: /修改密码/ })
    await userEvent.click(submitButton)

    await waitFor(() => {
      expect(mockApiClient.changePassword).toHaveBeenCalled()
    })

    expect(mockSetToastMessage).toHaveBeenCalledWith({
      message: '密码修改成功',
      type: 'success',
    })
  })

  it('handles unicode passwords correctly', async () => {
    mockApiClient.changePassword.mockResolvedValue({ message: '密码修改成功' })

    const props = {
      ...defaultProps,
      passwordForm: {
        old_password: '老密码123',
        new_password: '新密码456',
        confirm_password: '新密码456',
      },
    }

    render(<PasswordChangeForm {...props} />)

    const submitButton = screen.getByRole('button', { name: /修改密码/ })
    await userEvent.click(submitButton)

    await waitFor(() => {
      expect(mockApiClient.changePassword).toHaveBeenCalledWith({
        old_password: '老密码123',
        new_password: '新密码456',
        confirm_password: '新密码456',
      })
    })
  })

  it('validates boundary length passwords', async () => {
    mockApiClient.changePassword.mockResolvedValue({ message: '密码修改成功' })

    // Test minimum length (6 characters)
    const minPasswordProps = {
      ...defaultProps,
      passwordForm: {
        old_password: 'old123',
        new_password: 'new123',
        confirm_password: 'new123',
      },
    }

    const { rerender } = render(<PasswordChangeForm {...minPasswordProps} />)

    let submitButton = screen.getByRole('button', { name: /修改密码/ })
    await userEvent.click(submitButton)

    await waitFor(() => {
      expect(mockApiClient.changePassword).toHaveBeenCalled()
    })

    // Test maximum length (128 characters)
    const maxPassword = 'a'.repeat(127) + '1' // 128 characters
    const maxPasswordProps = {
      ...defaultProps,
      passwordForm: {
        old_password: 'old123',
        new_password: maxPassword,
        confirm_password: maxPassword,
      },
    }

    rerender(<PasswordChangeForm {...maxPasswordProps} />)

    submitButton = screen.getByRole('button', { name: /修改密码/ })
    await userEvent.click(submitButton)

    await waitFor(() => {
      expect(mockApiClient.changePassword).toHaveBeenCalledWith({
        old_password: 'old123',
        new_password: maxPassword,
        confirm_password: maxPassword,
      })
    })
  })
})