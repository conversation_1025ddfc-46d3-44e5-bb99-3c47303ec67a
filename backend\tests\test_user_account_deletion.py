#!/usr/bin/env python3
"""
Comprehensive unit tests for user account deletion endpoints.

Tests cover:
- Account deletion with cascade operations
- Database transaction handling
- Error scenarios and rollback
- Security validation
- Edge cases
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, MagicMock, call
from fastapi import HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import text
from app.api.user import delete_account
from app.models import User
from app.schemas.user import AccountDeleteRequest, AccountDeleteResponse

class TestAccountDeletionEndpoint:
    """Test suite for account deletion functionality"""

    def setup_method(self):
        """Set up test fixtures before each test"""
        self.mock_db = Mock(spec=Session)
        self.mock_user = Mock(spec=User)
        self.mock_user.id = 1
        self.mock_user.username = "testuser"
        self.mock_user.email = "<EMAIL>"

    def test_account_deletion_success(self):
        """Test successful account deletion with all cascade operations"""
        # Arrange
        account_data = AccountDeleteRequest(username_confirmation="testuser")
        
        # Mock database operations
        self.mock_db.execute = Mock()
        self.mock_db.delete = Mock()
        self.mock_db.commit = Mock()
        
        # Act
        result = delete_account(account_data, self.mock_user, self.mock_db)
        
        # Assert
        assert isinstance(result, AccountDeleteResponse)
        assert result.success == True
        assert result.message == "账户已成功删除"
        
        # Verify all cascade deletion operations were called in correct order
        expected_calls = [
            call(text("DELETE FROM favorites WHERE user_id = :user_id"), {"user_id": 1}),
            call(text("DELETE FROM comment_likes WHERE user_id = :user_id"), {"user_id": 1}),
            call(text("DELETE FROM comments WHERE user_id = :user_id"), {"user_id": 1}),
            call(text("DELETE FROM manga_reading_progress WHERE user_id = :user_id"), {"user_id": 1})
        ]
        
        assert self.mock_db.execute.call_count == 4
        self.mock_db.execute.assert_has_calls(expected_calls, any_order=False)
        self.mock_db.delete.assert_called_once_with(self.mock_user)
        self.mock_db.commit.assert_called_once()

    def test_account_deletion_username_mismatch(self):
        """Test account deletion with incorrect username confirmation"""
        # Arrange
        account_data = AccountDeleteRequest(username_confirmation="wrongusername")
        
        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            delete_account(account_data, self.mock_user, self.mock_db)
        
        assert exc_info.value.status_code == 400
        assert exc_info.value.detail == "用户名确认不匹配"
        
        # Verify no database operations were performed
        self.mock_db.execute.assert_not_called()
        self.mock_db.delete.assert_not_called()
        self.mock_db.commit.assert_not_called()

    def test_account_deletion_empty_username_confirmation(self):
        """Test account deletion with empty username confirmation"""
        # Arrange
        account_data = AccountDeleteRequest(username_confirmation="")
        
        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            delete_account(account_data, self.mock_user, self.mock_db)
        
        assert exc_info.value.status_code == 400
        assert exc_info.value.detail == "用户名确认不匹配"

    def test_account_deletion_whitespace_username_confirmation(self):
        """Test account deletion with whitespace-only username confirmation"""
        # Arrange
        account_data = AccountDeleteRequest(username_confirmation="   ")
        
        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            delete_account(account_data, self.mock_user, self.mock_db)
        
        assert exc_info.value.status_code == 400
        assert exc_info.value.detail == "用户名确认不匹配"

    def test_account_deletion_database_error_rollback(self):
        """Test account deletion with database error triggers rollback"""
        # Arrange
        account_data = AccountDeleteRequest(username_confirmation="testuser")
        
        # Mock database error during favorites deletion
        self.mock_db.execute.side_effect = Exception("Database connection error")
        self.mock_db.rollback = Mock()
        
        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            delete_account(account_data, self.mock_user, self.mock_db)
        
        assert exc_info.value.status_code == 500
        assert exc_info.value.detail == "删除账户时发生错误，请稍后重试"
        
        # Verify rollback was called
        self.mock_db.rollback.assert_called_once()
        self.mock_db.commit.assert_not_called()

    def test_account_deletion_partial_failure_rollback(self):
        """Test account deletion with partial failure rolls back all changes"""
        # Arrange
        account_data = AccountDeleteRequest(username_confirmation="testuser")
        
        # Mock success for first two operations, then failure
        def side_effect(*args, **kwargs):
            if self.mock_db.execute.call_count <= 2:
                return None
            else:
                raise Exception("Failed to delete comments")
        
        self.mock_db.execute.side_effect = side_effect
        self.mock_db.rollback = Mock()
        
        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            delete_account(account_data, self.mock_user, self.mock_db)
        
        assert exc_info.value.status_code == 500
        assert exc_info.value.detail == "删除账户时发生错误，请稍后重试"
        
        # Verify rollback was called and commit was not
        self.mock_db.rollback.assert_called_once()
        self.mock_db.commit.assert_not_called()

class TestAccountDeletionCascadeOperations:
    """Test suite for cascade deletion operations"""

    def setup_method(self):
        """Set up test fixtures before each test"""
        self.mock_db = Mock(spec=Session)
        self.mock_user = Mock(spec=User)
        self.mock_user.id = 42
        self.mock_user.username = "cascadeuser"

    def test_cascade_deletion_order_is_correct(self):
        """Test that cascade deletion operations occur in the correct order"""
        # Arrange
        account_data = AccountDeleteRequest(username_confirmation="cascadeuser")
        
        self.mock_db.execute = Mock()
        self.mock_db.delete = Mock()
        self.mock_db.commit = Mock()
        
        # Act
        delete_account(account_data, self.mock_user, self.mock_db)
        
        # Assert - verify the exact order of operations
        expected_calls = [
            # 1. Delete favorites first (foreign key references)
            call(text("DELETE FROM favorites WHERE user_id = :user_id"), {"user_id": 42}),
            # 2. Delete comment likes (foreign key references)
            call(text("DELETE FROM comment_likes WHERE user_id = :user_id"), {"user_id": 42}),
            # 3. Delete comments (foreign key references)
            call(text("DELETE FROM comments WHERE user_id = :user_id"), {"user_id": 42}),
            # 4. Delete reading progress (foreign key references)
            call(text("DELETE FROM manga_reading_progress WHERE user_id = :user_id"), {"user_id": 42})
        ]
        
        self.mock_db.execute.assert_has_calls(expected_calls)
        # 5. Finally delete the user record itself
        self.mock_db.delete.assert_called_once_with(self.mock_user)

    def test_cascade_deletion_with_specific_user_id(self):
        """Test that cascade deletion uses correct user ID in all operations"""
        # Arrange
        specific_user = Mock(spec=User)
        specific_user.id = 999
        specific_user.username = "specificuser"
        
        account_data = AccountDeleteRequest(username_confirmation="specificuser")
        
        self.mock_db.execute = Mock()
        self.mock_db.delete = Mock()
        self.mock_db.commit = Mock()
        
        # Act
        delete_account(account_data, specific_user, self.mock_db)
        
        # Assert - verify all SQL operations use the correct user_id
        for call_args in self.mock_db.execute.call_args_list:
            args, kwargs = call_args
            if len(args) > 1:  # Has parameters
                assert args[1]["user_id"] == 999

class TestAccountDeletionSecurity:
    """Test suite for security aspects of account deletion"""

    def setup_method(self):
        """Set up test fixtures before each test"""
        self.mock_db = Mock(spec=Session)
        self.mock_user = Mock(spec=User)
        self.mock_user.id = 1
        self.mock_user.username = "secureuser"

    def test_account_deletion_case_sensitive_username(self):
        """Test that username confirmation is case sensitive"""
        # Arrange
        account_data = AccountDeleteRequest(username_confirmation="SECUREUSER")  # Wrong case
        
        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            delete_account(account_data, self.mock_user, self.mock_db)
        
        assert exc_info.value.status_code == 400
        assert exc_info.value.detail == "用户名确认不匹配"

    def test_account_deletion_prevents_sql_injection(self):
        """Test that malicious username confirmation doesn't cause SQL injection"""
        # Arrange
        malicious_username = "testuser'; DROP TABLE users; --"
        self.mock_user.username = "testuser"
        account_data = AccountDeleteRequest(username_confirmation=malicious_username)
        
        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            delete_account(account_data, self.mock_user, self.mock_db)
        
        assert exc_info.value.status_code == 400
        assert exc_info.value.detail == "用户名确认不匹配"

    def test_account_deletion_unicode_username_handling(self):
        """Test that unicode usernames are handled correctly"""
        # Arrange
        unicode_user = Mock(spec=User)
        unicode_user.id = 1
        unicode_user.username = "用户名测试"
        
        account_data = AccountDeleteRequest(username_confirmation="用户名测试")
        
        self.mock_db.execute = Mock()
        self.mock_db.delete = Mock()
        self.mock_db.commit = Mock()
        
        # Act
        result = delete_account(account_data, unicode_user, self.mock_db)
        
        # Assert
        assert result.success == True

class TestAccountDeletionErrorHandling:
    """Test suite for error handling scenarios"""

    def setup_method(self):
        """Set up test fixtures before each test"""
        self.mock_db = Mock(spec=Session)
        self.mock_user = Mock(spec=User)
        self.mock_user.id = 1
        self.mock_user.username = "erroruser"

    def test_database_connection_lost_during_deletion(self):
        """Test handling of database connection loss during deletion"""
        # Arrange
        account_data = AccountDeleteRequest(username_confirmation="erroruser")
        
        self.mock_db.execute.side_effect = ConnectionError("Connection lost")
        self.mock_db.rollback = Mock()
        
        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            delete_account(account_data, self.mock_user, self.mock_db)
        
        assert exc_info.value.status_code == 500
        self.mock_db.rollback.assert_called_once()

    def test_timeout_during_deletion(self):
        """Test handling of timeout during deletion operations"""
        # Arrange
        account_data = AccountDeleteRequest(username_confirmation="erroruser")
        
        self.mock_db.execute.side_effect = TimeoutError("Operation timeout")
        self.mock_db.rollback = Mock()
        
        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            delete_account(account_data, self.mock_user, self.mock_db)
        
        assert exc_info.value.status_code == 500
        self.mock_db.rollback.assert_called_once()

    def test_foreign_key_constraint_error(self):
        """Test handling of foreign key constraint errors"""
        # Arrange
        account_data = AccountDeleteRequest(username_confirmation="erroruser")
        
        # Mock a foreign key constraint error
        from sqlalchemy.exc import IntegrityError
        integrity_error = IntegrityError("statement", "params", "orig")
        self.mock_db.delete.side_effect = integrity_error
        self.mock_db.rollback = Mock()
        
        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            delete_account(account_data, self.mock_user, self.mock_db)
        
        assert exc_info.value.status_code == 500
        self.mock_db.rollback.assert_called_once()

class TestAccountDeletionSchemaValidation:
    """Test suite for request schema validation"""

    def test_valid_account_delete_request(self):
        """Test valid account delete request creation"""
        # Arrange & Act
        request = AccountDeleteRequest(username_confirmation="validuser")
        
        # Assert
        assert request.username_confirmation == "validuser"

    def test_account_delete_request_with_special_characters(self):
        """Test account delete request with special characters in username"""
        # Arrange & Act
        request = AccountDeleteRequest(username_confirmation="<EMAIL>")
        
        # Assert
        assert request.username_confirmation == "<EMAIL>"

    def test_account_delete_response_structure(self):
        """Test account delete response structure"""
        # Arrange & Act
        response = AccountDeleteResponse(success=True, message="Test message")
        
        # Assert
        assert response.success == True
        assert response.message == "Test message"

if __name__ == "__main__":
    pytest.main([__file__])