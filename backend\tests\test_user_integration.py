#!/usr/bin/env python3
"""
Integration tests for user management endpoints with real database.

Uses testcontainers to spin up a MySQL instance for testing.
Tests the complete flow from API endpoint to database.
"""

import pytest
import asyncio
import os
from typing import Generator
from testcontainers.mysql import MySqlContainer
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from fastapi.testclient import TestClient
from app.main import app
from app.core.database import get_db
from app.models import Base, User
from app.core.security import get_password_hash
import json

class TestUserManagementIntegration:
    """Integration tests for user management with real database"""

    @classmethod
    def setup_class(cls):
        """Set up test database container before all tests"""
        # Start MySQL container
        cls.mysql_container = MySqlContainer("mysql:8.0")
        cls.mysql_container.start()
        
        # Create database engine
        connection_url = cls.mysql_container.get_connection_url()
        cls.engine = create_engine(connection_url)
        
        # Create tables
        Base.metadata.create_all(bind=cls.engine)
        
        # Create session factory
        cls.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=cls.engine)
        
        # Override dependency
        def override_get_db():
            try:
                db = cls.SessionLocal()
                yield db
            finally:
                db.close()
        
        app.dependency_overrides[get_db] = override_get_db
        
        # Create test client
        cls.client = TestClient(app)

    @classmethod
    def teardown_class(cls):
        """Clean up after all tests"""
        cls.mysql_container.stop()

    def setup_method(self):
        """Set up before each test"""
        # Clear all data
        self.db = self.SessionLocal()
        
        # Delete in order to respect foreign key constraints
        self.db.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
        for table in reversed(Base.metadata.sorted_tables):
            self.db.execute(text(f"DELETE FROM {table.name}"))
        self.db.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
        self.db.commit()

    def teardown_method(self):
        """Clean up after each test"""
        self.db.close()

    def create_test_user(self, username="testuser", password="password123") -> User:
        """Create a test user in the database"""
        user = User(
            username=username,
            email=f"{username}@test.com",
            password_hash=get_password_hash(password),
            is_active=True,
            is_admin=False
        )
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        return user

    def get_auth_token(self, username="testuser", password="password123") -> str:
        """Get authentication token for test user"""
        # Create user if not exists
        existing_user = self.db.query(User).filter(User.username == username).first()
        if not existing_user:
            self.create_test_user(username, password)
        
        # Login to get token
        response = self.client.post(
            "/api/v1/auth/login",
            data={"username": username, "password": password}
        )
        assert response.status_code == 200
        return response.json()["access_token"]

    def test_password_change_integration_success(self):
        """Test complete password change flow with database"""
        # Arrange
        user = self.create_test_user("passuser", "oldpass123")
        token = self.get_auth_token("passuser", "oldpass123")
        
        headers = {"Authorization": f"Bearer {token}"}
        password_data = {
            "old_password": "oldpass123",
            "new_password": "newpass456",
            "confirm_password": "newpass456"
        }
        
        # Act
        response = self.client.put(
            "/api/v1/user/password",
            json=password_data,
            headers=headers
        )
        
        # Assert
        assert response.status_code == 200
        result = response.json()
        assert result["success"] == True
        assert result["message"] == "密码修改成功"
        
        # Verify password was actually changed in database
        self.db.refresh(user)
        from app.core.security import verify_password
        assert verify_password("newpass456", user.password_hash)
        assert not verify_password("oldpass123", user.password_hash)

    def test_password_change_integration_wrong_old_password(self):
        """Test password change with wrong old password"""
        # Arrange
        self.create_test_user("passuser2", "correctpass123")
        token = self.get_auth_token("passuser2", "correctpass123")
        
        headers = {"Authorization": f"Bearer {token}"}
        password_data = {
            "old_password": "wrongpass123",
            "new_password": "newpass456",
            "confirm_password": "newpass456"
        }
        
        # Act
        response = self.client.put(
            "/api/v1/user/password",
            json=password_data,
            headers=headers
        )
        
        # Assert
        assert response.status_code == 400
        result = response.json()
        assert result["detail"] == "原密码错误"

    def test_password_change_integration_validation_error(self):
        """Test password change with validation errors"""
        # Arrange
        self.create_test_user("passuser3", "oldpass123")
        token = self.get_auth_token("passuser3", "oldpass123")
        
        headers = {"Authorization": f"Bearer {token}"}
        password_data = {
            "old_password": "oldpass123",
            "new_password": "123",  # Too short
            "confirm_password": "123"
        }
        
        # Act
        response = self.client.put(
            "/api/v1/user/password",
            json=password_data,
            headers=headers
        )
        
        # Assert
        assert response.status_code == 422  # Validation error

    def test_account_deletion_integration_success(self):
        """Test complete account deletion flow with database"""
        # Arrange
        user = self.create_test_user("deleteuser", "password123")
        token = self.get_auth_token("deleteuser", "password123")
        
        # Create some related data
        self.db.execute(text("INSERT INTO favorites (user_id, content_type, anime_id) VALUES (:user_id, 'anime', 1)"), 
                       {"user_id": user.id})
        self.db.execute(text("INSERT INTO comments (user_id, content_type, anime_id, content) VALUES (:user_id, 'anime', 1, 'test comment')"), 
                       {"user_id": user.id})
        self.db.commit()
        
        headers = {"Authorization": f"Bearer {token}"}
        delete_data = {"username_confirmation": "deleteuser"}
        
        # Act
        response = self.client.delete(
            "/api/v1/user/account",
            json=delete_data,
            headers=headers
        )
        
        # Assert
        assert response.status_code == 200
        result = response.json()
        assert result["success"] == True
        assert result["message"] == "账户已成功删除"
        
        # Verify user and related data were deleted
        deleted_user = self.db.query(User).filter(User.id == user.id).first()
        assert deleted_user is None
        
        # Check that cascade deletion worked
        favorites_count = self.db.execute(text("SELECT COUNT(*) FROM favorites WHERE user_id = :user_id"), 
                                        {"user_id": user.id}).scalar()
        assert favorites_count == 0
        
        comments_count = self.db.execute(text("SELECT COUNT(*) FROM comments WHERE user_id = :user_id"), 
                                       {"user_id": user.id}).scalar()
        assert comments_count == 0

    def test_account_deletion_integration_wrong_username(self):
        """Test account deletion with wrong username confirmation"""
        # Arrange
        self.create_test_user("deleteuser2", "password123")
        token = self.get_auth_token("deleteuser2", "password123")
        
        headers = {"Authorization": f"Bearer {token}"}
        delete_data = {"username_confirmation": "wrongusername"}
        
        # Act
        response = self.client.delete(
            "/api/v1/user/account",
            json=delete_data,
            headers=headers
        )
        
        # Assert
        assert response.status_code == 400
        result = response.json()
        assert result["detail"] == "用户名确认不匹配"

    def test_account_deletion_with_complex_relationships(self):
        """Test account deletion with complex relationship data"""
        # Arrange
        user = self.create_test_user("complexuser", "password123")
        token = self.get_auth_token("complexuser", "password123")
        
        # Create complex relationship data
        # Add multiple favorites
        for i in range(1, 4):
            self.db.execute(text("INSERT INTO favorites (user_id, content_type, anime_id) VALUES (:user_id, 'anime', :anime_id)"), 
                           {"user_id": user.id, "anime_id": i})
        
        # Add comments
        comment_id = None
        for i in range(1, 3):
            result = self.db.execute(text("INSERT INTO comments (user_id, content_type, anime_id, content) VALUES (:user_id, 'anime', 1, :content)"), 
                                   {"user_id": user.id, "content": f"comment {i}"})
            if i == 1:
                comment_id = result.lastrowid
        
        # Add comment likes
        if comment_id:
            self.db.execute(text("INSERT INTO comment_likes (comment_id, user_id, is_like) VALUES (:comment_id, :user_id, true)"), 
                           {"comment_id": comment_id, "user_id": user.id})
        
        self.db.commit()
        
        headers = {"Authorization": f"Bearer {token}"}
        delete_data = {"username_confirmation": "complexuser"}
        
        # Act
        response = self.client.delete(
            "/api/v1/user/account",
            json=delete_data,
            headers=headers
        )
        
        # Assert
        assert response.status_code == 200
        result = response.json()
        assert result["success"] == True
        
        # Verify all data was properly deleted
        user_exists = self.db.query(User).filter(User.id == user.id).first()
        assert user_exists is None
        
        # Check all related data was cascade deleted
        for table in ["favorites", "comments", "comment_likes"]:
            count = self.db.execute(text(f"SELECT COUNT(*) FROM {table} WHERE user_id = :user_id"), 
                                  {"user_id": user.id}).scalar()
            assert count == 0

    def test_password_change_concurrent_access(self):
        """Test password change under concurrent access"""
        # Arrange
        user = self.create_test_user("concurrentuser", "password123")
        token1 = self.get_auth_token("concurrentuser", "password123")
        
        headers = {"Authorization": f"Bearer {token1}"}
        password_data = {
            "old_password": "password123",
            "new_password": "newpass456",
            "confirm_password": "newpass456"
        }
        
        # Act - Simulate concurrent password changes
        response1 = self.client.put(
            "/api/v1/user/password",
            json=password_data,
            headers=headers
        )
        
        # Second request should fail because password has changed
        response2 = self.client.put(
            "/api/v1/user/password",
            json=password_data,
            headers=headers
        )
        
        # Assert
        assert response1.status_code == 200
        assert response2.status_code == 400  # Old password no longer valid

    def test_authentication_required_for_user_endpoints(self):
        """Test that authentication is required for user management endpoints"""
        # Arrange
        password_data = {
            "old_password": "password123",
            "new_password": "newpass456",
            "confirm_password": "newpass456"
        }
        delete_data = {"username_confirmation": "testuser"}
        
        # Act & Assert - Password change without auth
        response = self.client.put("/api/v1/user/password", json=password_data)
        assert response.status_code == 401
        
        # Act & Assert - Account deletion without auth
        response = self.client.delete("/api/v1/user/account", json=delete_data)
        assert response.status_code == 401

if __name__ == "__main__":
    pytest.main([__file__])