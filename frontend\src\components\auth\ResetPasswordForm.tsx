'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { apiClient } from '@/lib/api';
import { ArrowLeft, Mail, CheckCircle, AlertCircle } from 'lucide-react';

interface ResetPasswordFormProps {
  onBackToLogin: () => void;
}

export function ResetPasswordForm({ onBackToLogin }: ResetPasswordFormProps) {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [step, setStep] = useState<'request' | 'sent'>('request');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) {
      setError('请输入邮箱地址');
      return;
    }

    setLoading(true);
    setError('');
    setMessage('');

    try {
      await apiClient.requestPasswordReset(email);
      setStep('sent');
      setMessage('重置密码邮件已发送，请检查您的邮箱');
    } catch (error: any) {
      console.error('Password reset request failed:', error);

      // 处理不同类型的错误
      if (error.message && error.message.includes('尚未注册')) {
        setError('该邮箱地址尚未注册，请检查邮箱地址或先注册账户');
      } else if (error.message && error.message.includes('404')) {
        setError('该邮箱地址尚未注册，请检查邮箱地址或先注册账户');
      } else {
        setError(error.message || '发送重置邮件失败，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  };

  if (step === 'sent') {
    return (
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
            <CheckCircle className="h-6 w-6 text-green-600" />
          </div>
          <CardTitle>邮件已发送</CardTitle>
          <CardDescription>
            我们已向 {email} 发送了重置密码的邮件
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <Mail className="h-4 w-4" />
            <AlertDescription>
              请检查您的邮箱（包括垃圾邮件文件夹），点击邮件中的链接来重置密码。
              如果几分钟内没有收到邮件，请检查邮箱地址是否正确。
            </AlertDescription>
          </Alert>
          
          <div className="space-y-2">
            <Button 
              variant="outline" 
              className="w-full" 
              onClick={() => setStep('request')}
            >
              重新发送邮件
            </Button>
            <Button 
              variant="ghost" 
              className="w-full" 
              onClick={onBackToLogin}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回登录
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="text-center">
        <CardTitle>重置密码</CardTitle>
        <CardDescription>
          输入您的邮箱地址，我们将发送重置密码的链接
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">邮箱地址</Label>
            <Input
              id="email"
              type="email"
              placeholder="请输入您的邮箱地址"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {message && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>{message}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? '发送中...' : '发送重置邮件'}
            </Button>
            <Button 
              type="button" 
              variant="ghost" 
              className="w-full" 
              onClick={onBackToLogin}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回登录
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
