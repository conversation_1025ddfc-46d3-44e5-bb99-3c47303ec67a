#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试脚本 - 验证ikanmh.top页面数据获取能力
不进行下载和数据库操作，只测试页面解析功能
"""

import requests
import json
from lxml import etree
from urllib.parse import urljoin

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"配置文件加载失败: {e}")
        return None

def make_request(url, headers, retries=3):
    """发起HTTP请求"""
    for attempt in range(retries):
        try:
            response = requests.get(url, headers=headers, timeout=30)
            if response.status_code == 200:
                response.encoding = 'utf-8'
                return response
            else:
                print(f"请求失败，状态码：{response.status_code}")
        except Exception as e:
            print(f"请求异常: {e}")
        
        if attempt < retries - 1:
            print(f"重试第 {attempt + 1} 次...")
    
    return None

def test_manga_list_page(config, page_num=1):
    """测试漫画列表页解析"""
    print(f"\n=== 测试第{page_num}页漫画列表 ===")
    
    url = config['crawler']['booklist_url'].format(page_num)
    print(f"测试URL: {url}")
    
    response = make_request(url, config['crawler']['headers'])
    if not response:
        print("❌ 请求失败")
        return []
    
    print(f"✅ 请求成功，状态码: {response.status_code}")
    print(f"📄 响应长度: {len(response.text)} 字符")
    
    try:
        soup = etree.HTML(response.text)
        
        # 测试原始XPath
        print("\n--- 测试原始XPath ---")
        manga_items = soup.xpath('//div[@class="mh-item"]/div[@class="mh-item-detali"]/h2/a/@href')
        print(f"原始XPath找到: {len(manga_items)} 个链接")
        
        # 测试更通用的XPath
        print("\n--- 测试通用XPath ---")
        all_book_links = soup.xpath('//a[contains(@href, "/book/")]/@href')
        print(f"通用XPath找到: {len(all_book_links)} 个 /book/ 链接")
        
        # 测试最宽泛的方法
        print("\n--- 测试所有链接过滤 ---")
        all_links = soup.xpath('//a/@href')
        book_links = [link for link in all_links if link and '/book/' in link]
        print(f"所有链接数量: {len(all_links)}")
        print(f"包含/book/的链接: {len(book_links)}")
        
        # 显示找到的链接示例
        if book_links:
            print("\n--- 找到的链接示例 ---")
            for i, link in enumerate(book_links[:10]):  # 只显示前10个
                full_url = urljoin(config['crawler']['base_url'], link)
                print(f"{i+1}. {link} -> {full_url}")
        
        # 保存页面HTML片段用于调试
        if len(response.text) > 1000:
            with open(f'debug_page_{page_num}.html', 'w', encoding='utf-8') as f:
                f.write(response.text[:5000])  # 只保存前5000字符
            print(f"\n📁 页面HTML片段已保存到 debug_page_{page_num}.html")
        
        return book_links if book_links else all_book_links if all_book_links else manga_items
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        return []

def test_manga_detail_page(config, manga_url):
    """测试漫画详情页解析"""
    print(f"\n=== 测试漫画详情页 ===")
    print(f"测试URL: {manga_url}")
    
    response = make_request(manga_url, config['crawler']['headers'])
    if not response:
        print("❌ 请求失败")
        return None
    
    print(f"✅ 请求成功，状态码: {response.status_code}")
    
    try:
        soup = etree.HTML(response.text)
        
        # 测试标题提取
        title_elements = soup.xpath('//div[@class="comic-view"]/div[@class="comic-content"]/div[@class="comic-title"]/h1/text()')
        title = title_elements[0].strip() if title_elements else "未找到标题"
        print(f"标题: {title}")
        
        # 测试作者提取
        author_elements = soup.xpath('//div[@class="comic-view"]//span[contains(text(), "作者")]/following-sibling::text()')
        author = author_elements[0].strip() if author_elements else "未找到作者"
        print(f"作者: {author}")
        
        # 测试章节列表
        chapter_elements = soup.xpath('//div[@id="chapterlistload"]//li/a')
        print(f"章节数量: {len(chapter_elements)}")
        
        if chapter_elements:
            print("\n--- 章节示例 ---")
            for i, chapter in enumerate(chapter_elements[:5]):  # 只显示前5个
                chapter_title = chapter.text or chapter.get('title', f'第{i+1}话')
                chapter_href = chapter.get('href', '')
                print(f"{i+1}. {chapter_title} -> {chapter_href}")
        
        # 测试封面
        cover_elements = soup.xpath('//div[@class="comic-view"]//div[@class="comic-cover"]/img/@src')
        cover = cover_elements[0] if cover_elements else "未找到封面"
        print(f"封面: {cover}")
        
        # 测试标签提取
        tag_elements = []
        # 方式1：从标签链接中提取
        tag_links = soup.xpath('//a[contains(@href, "/tag/")]/text()')
        if tag_links:
            tag_elements.extend(tag_links)
        
        # 方式2：从漫画信息区域提取
        info_tags = soup.xpath('//div[@class="comic-view"]//div[@class="comic-intro"]//a/text()')
        if info_tags:
            tag_elements.extend(info_tags)
        
        # 清理和去重标签
        tags = list(set([tag.strip() for tag in tag_elements if tag.strip() and len(tag.strip()) > 0]))
        print(f"标签: {tags}")
        print(f"标签数量: {len(tags)}")
        
        return {
            'title': title,
            'author': author,
            'chapter_count': len(chapter_elements),
            'cover': cover
        }
        
    except Exception as e:
        print(f"❌ 详情页解析失败: {e}")
        return None

def test_chapter_page(config, chapter_url):
    """测试章节页面解析"""
    print(f"\n=== 测试章节页面 ===")
    print(f"测试URL: {chapter_url}")
    
    response = make_request(chapter_url, config['crawler']['headers'])
    if not response:
        print("❌ 请求失败")
        return None
    
    print(f"✅ 请求成功，状态码: {response.status_code}")
    
    try:
        soup = etree.HTML(response.text)
        
        # 测试图片提取
        img_elements = soup.xpath('//div[@class="comicpage"]/div/img/@data-original | //div[@class="comicpage"]/div/img/@src')
        print(f"找到图片数量: {len(img_elements)}")
        
        if img_elements:
            print("\n--- 图片链接示例 ---")
            for i, img_src in enumerate(img_elements[:3]):  # 只显示前3个
                full_img_url = urljoin(config['crawler']['base_url'], img_src) if not img_src.startswith('http') else img_src
                print(f"{i+1}. {img_src}")
                print(f"   完整URL: {full_img_url}")
        
        return len(img_elements)
        
    except Exception as e:
        print(f"❌ 章节页解析失败: {e}")
        return 0

def main():
    """主测试函数"""
    print("开始测试 ikanmh.top 数据获取能力")
    
    # 加载配置
    config = load_config()
    if not config:
        print("❌ 无法继续测试")
        return
    
    print(f"基础URL: {config['crawler']['base_url']}")
    print(f"列表URL模板: {config['crawler']['booklist_url']}")
    
    # 测试列表页
    manga_links = test_manga_list_page(config, page_num=1)
    
    if not manga_links:
        print("\n❌ 无法获取漫画链接，测试结束")
        return
    
    print(f"\n✅ 成功获取 {len(manga_links)} 个漫画链接")
    
    # 测试详情页（测试第一个漫画）
    if manga_links:
        first_manga_url = urljoin(config['crawler']['base_url'], manga_links[0])
        manga_detail = test_manga_detail_page(config, first_manga_url)
        
        if manga_detail and manga_detail['chapter_count'] > 0:
            print(f"\n✅ 详情页解析成功，找到 {manga_detail['chapter_count']} 个章节")
            
            # 测试章节页（测试第一个章节）
            # 这里需要重新获取章节链接
            response = make_request(first_manga_url, config['crawler']['headers'])
            if response:
                soup = etree.HTML(response.text)
                chapter_elements = soup.xpath('//div[@id="chapterlistload"]//li/a')
                if chapter_elements:
                    first_chapter_href = chapter_elements[0].get('href')
                    if first_chapter_href:
                        chapter_url = urljoin(config['crawler']['base_url'], first_chapter_href)
                        image_count = test_chapter_page(config, chapter_url)
                        if image_count > 0:
                            print(f"\n✅ 章节页解析成功，找到 {image_count} 张图片")
                        else:
                            print(f"\n❌ 章节页无法获取图片")
    
    print(f"\n🎉 测试完成！")

if __name__ == "__main__":
    main()