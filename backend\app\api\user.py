from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.deps import get_current_active_user
from app.core.security import get_password_hash, verify_password
from app.models import User
from app.schemas.user import PasswordChangeRequest, PasswordChangeResponse, AccountDeleteRequest, AccountDeleteResponse
from app.crud.manga import MangaReadingProgressCRUD
from app.crud.anime import FavoriteCRUD
from sqlalchemy import text

router = APIRouter()

# ==================== 个人信息管理 ====================

@router.get("/profile", summary="获取用户个人信息")
def get_user_profile(
    current_user: User = Depends(get_current_active_user)
):
    """获取当前用户的个人信息"""
    return {
        "id": current_user.id,
        "username": current_user.username,
        "email": current_user.email,
        "avatar_url": current_user.avatar_url,
        "is_admin": current_user.is_admin,
        "created_at": current_user.created_at,
        "updated_at": current_user.updated_at
    }

@router.put("/profile", summary="更新用户个人信息")
def update_user_profile(
    username: Optional[str] = None,
    avatar_url: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新用户个人信息"""
    # 检查用户名是否已被使用（如果提供了新用户名）
    if username and username != current_user.username:
        existing_user = db.query(User).filter(User.username == username).first()
        if existing_user:
            raise HTTPException(status_code=400, detail="用户名已被使用")
        current_user.username = username
    
    # 更新头像URL
    if avatar_url is not None:
        current_user.avatar_url = avatar_url
    
    db.commit()
    db.refresh(current_user)
    
    return {
        "message": "个人信息更新成功",
        "user": {
            "id": current_user.id,
            "username": current_user.username,
            "email": current_user.email,
            "avatar_url": current_user.avatar_url,
            "is_admin": current_user.is_admin,
            "created_at": current_user.created_at,
            "updated_at": current_user.updated_at
        }
    }

# ==================== 密码管理 ====================

@router.put("/password", response_model=PasswordChangeResponse, summary="修改密码")
def change_password(
    password_data: PasswordChangeRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """修改用户密码"""
    # 验证原密码是否正确
    if not verify_password(password_data.old_password, current_user.password_hash):
        raise HTTPException(
            status_code=400,
            detail="原密码错误"
        )
    
    # 加密新密码
    new_password_hash = get_password_hash(password_data.new_password)
    
    # 更新数据库中的密码
    current_user.password_hash = new_password_hash
    db.commit()
    db.refresh(current_user)
    
    return PasswordChangeResponse(
        success=True,
        message="密码修改成功"
    )

@router.delete("/account", response_model=AccountDeleteResponse, summary="删除账户")
def delete_account(
    account_data: AccountDeleteRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除用户账户及所有关联数据"""
    # 验证用户名确认
    if account_data.username_confirmation != current_user.username:
        raise HTTPException(
            status_code=400,
            detail="用户名确认不匹配"
        )
    
    try:
        # 开始数据库事务
        # 1. 删除收藏记录
        db.execute(text("DELETE FROM favorites WHERE user_id = :user_id"), {"user_id": current_user.id})
        
        # 2. 删除评论点赞记录
        db.execute(text("DELETE FROM comment_likes WHERE user_id = :user_id"), {"user_id": current_user.id})
        
        # 3. 删除评论记录（包括回复）
        db.execute(text("DELETE FROM comments WHERE user_id = :user_id"), {"user_id": current_user.id})
        
        # 4. 删除阅读进度记录（通过cascade自动处理，但手动删除确保安全）
        db.execute(text("DELETE FROM manga_reading_progress WHERE user_id = :user_id"), {"user_id": current_user.id})
        
        # 5. 最后删除用户记录
        db.delete(current_user)
        
        # 提交事务
        db.commit()
        
        return AccountDeleteResponse(
            success=True,
            message="账户已成功删除"
        )
        
    except Exception as e:
        # 回滚事务
        db.rollback()
        print(f"删除账户错误: {e}")
        raise HTTPException(
            status_code=500,
            detail="删除账户时发生错误，请稍后重试"
        )

# ==================== 收藏管理 ====================

@router.get("/favorites/summary", summary="获取收藏统计")
def get_favorites_summary(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户收藏统计信息"""
    # 获取动漫收藏数量
    anime_favorites = FavoriteCRUD.get_user_favorites_by_type(
        db, user_id=current_user.id, content_type="anime", skip=0, limit=1000
    )
    anime_count = len(anime_favorites)
    
    # 获取漫画收藏数量
    manga_favorites = FavoriteCRUD.get_user_favorites_by_type(
        db, user_id=current_user.id, content_type="manga", skip=0, limit=1000
    )
    manga_count = len(manga_favorites)
    
    return {
        "total": anime_count + manga_count,
        "anime_count": anime_count,
        "manga_count": manga_count
    }

@router.get("/favorites", summary="获取用户收藏列表")
def get_user_favorites(
    content_type: Optional[str] = Query(None, description="内容类型: anime, manga 或 all"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户收藏列表（支持分类筛选）"""
    try:
        # 构建SQL查询 - 同时LEFT JOIN animes和mangas表
        sql = '''
            SELECT f.id, f.user_id, f.content_type, f.anime_id, f.manga_id, f.created_at,
                   m.title as manga_title, m.cover as manga_cover,
                   a.title as anime_title, a.cover as anime_cover
            FROM favorites f
            LEFT JOIN mangas m ON f.manga_id = m.id
            LEFT JOIN animes a ON f.anime_id = a.id
            WHERE f.user_id = :user_id
        '''
        
        params = {'user_id': current_user.id, 'limit': limit, 'skip': skip}
        
        if content_type == "anime":
            sql += " AND f.content_type = 'anime'"
        elif content_type == "manga":
            sql += " AND f.content_type = 'manga'"
        
        sql += " ORDER BY f.created_at DESC LIMIT :limit OFFSET :skip"
        
        # 使用SQLAlchemy执行原生SQL
        from sqlalchemy import text
        result_proxy = db.execute(text(sql), params)
        rows = result_proxy.fetchall()
        
        # 构建响应
        result = []
        for row in rows:
            fav_dict = {
                'id': row.id,
                'user_id': row.user_id,
                'content_type': row.content_type,
                'anime_id': row.anime_id,
                'manga_id': row.manga_id,
                'created_at': row.created_at.isoformat() if row.created_at else None,
                'anime': None,
                'manga': None
            }
            
            # 添加动漫信息
            if row.anime_id and row.anime_title:
                fav_dict['anime'] = {
                    'id': row.anime_id,
                    'title': row.anime_title,
                    'cover': row.anime_cover
                }
            
            # 添加漫画信息
            if row.manga_id and row.manga_title:
                fav_dict['manga'] = {
                    'id': row.manga_id,
                    'title': row.manga_title,
                    'cover': row.manga_cover
                }
            
            result.append(fav_dict)
        
        return result
        
    except Exception as e:
        print(f"获取收藏错误: {e}")
        import traceback
        traceback.print_exc()
        return []

# ==================== 阅读历史管理 ====================

@router.get("/reading-history", summary="获取阅读历史")
def get_reading_history(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=50),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户阅读历史 - 简化版本避免数据库模式不匹配"""
    try:
        # 使用原生SQL查询避免模式问题
        from sqlalchemy import text
        sql = '''
            SELECT mrp.id, mrp.user_id, mrp.manga_id, mrp.chapter_id, 
                   mrp.page_number, mrp.total_pages, mrp.progress_percentage,
                   mrp.last_read_at, mrp.created_at,
                   m.title as manga_title, m.cover as manga_cover,
                   mc.chapter_number, mc.title as chapter_title
            FROM manga_reading_progress mrp
            LEFT JOIN mangas m ON mrp.manga_id = m.id
            LEFT JOIN manga_chapters mc ON mrp.chapter_id = mc.id
            WHERE mrp.user_id = :user_id
            ORDER BY mrp.last_read_at DESC
            LIMIT :limit OFFSET :skip
        '''
        
        result = db.execute(text(sql), {
            'user_id': current_user.id,
            'limit': limit,
            'skip': skip
        })
        rows = result.fetchall()
        
        # 构建响应
        reading_history = []
        for row in rows:
            item = {
                'id': row.id,
                'user_id': row.user_id,
                'manga_id': row.manga_id,
                'chapter_id': row.chapter_id,
                'page_number': row.page_number or 1,
                'total_pages': row.total_pages or 0,
                'progress_percentage': float(row.progress_percentage) if row.progress_percentage else 0,
                'last_read_at': row.last_read_at.isoformat() if row.last_read_at else None,
                'manga': {
                    'id': row.manga_id,
                    'title': row.manga_title or '未知漫画',
                    'cover': row.manga_cover
                } if row.manga_title else None,
                'chapter': {
                    'id': row.chapter_id,
                    'chapter_number': row.chapter_number or 1,
                    'title': row.chapter_title
                } if row.chapter_id else None
            }
            reading_history.append(item)
            
        return reading_history
        
    except Exception as e:
        print(f"获取阅读历史错误: {e}")
        import traceback
        traceback.print_exc()
        return []

@router.delete("/reading-history/{manga_id}", summary="删除阅读历史")
def delete_reading_history(
    manga_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除特定漫画的阅读历史"""
    success = MangaReadingProgressCRUD.delete_reading_progress(
        db, user_id=current_user.id, manga_id=manga_id
    )
    
    if not success:
        raise HTTPException(status_code=404, detail="阅读历史不存在")
    
    return {"message": "阅读历史删除成功"}

@router.delete("/reading-history", summary="清空阅读历史")
def clear_reading_history(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """清空用户所有阅读历史"""
    # 获取所有阅读记录
    all_progress, _ = MangaReadingProgressCRUD.get_user_reading_history(
        db, user_id=current_user.id, skip=0, limit=1000
    )
    
    count = 0
    for progress in all_progress:
        if MangaReadingProgressCRUD.delete_reading_progress(
            db, user_id=current_user.id, manga_id=progress.manga_id
        ):
            count += 1
    
    return {"message": f"成功清空 {count} 条阅读历史"}

# ==================== 用户统计信息 ====================

@router.get("/stats", summary="获取用户统计信息")
def get_user_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户的统计信息 - 简化版本"""
    try:
        # 直接从数据库查询收藏统计
        from sqlalchemy import text
        
        anime_count = db.execute(text(
            "SELECT COUNT(*) FROM favorites WHERE user_id = :user_id AND content_type = 'anime'"
        ), {"user_id": current_user.id}).scalar() or 0
        
        manga_count = db.execute(text(
            "SELECT COUNT(*) FROM favorites WHERE user_id = :user_id AND content_type = 'manga'"
        ), {"user_id": current_user.id}).scalar() or 0
        
        reading_count = db.execute(text(
            "SELECT COUNT(*) FROM manga_reading_progress WHERE user_id = :user_id"
        ), {"user_id": current_user.id}).scalar() or 0
        
        return {
            "user_info": {
                "username": current_user.username,
                "join_date": current_user.created_at,
                "is_admin": current_user.is_admin or False
            },
            "favorites": {
                "total": anime_count + manga_count,
                "anime_count": anime_count,
                "manga_count": manga_count
            },
            "reading": {
                "manga_reading_count": reading_count
            }
        }
    except Exception as e:
        print(f"获取用户统计错误: {e}")
        # 返回默认值
        return {
            "user_info": {
                "username": current_user.username,
                "join_date": current_user.created_at,
                "is_admin": False
            },
            "favorites": {
                "total": 0,
                "anime_count": 0,
                "manga_count": 0
            },
            "reading": {
                "manga_reading_count": 0
            }
        }