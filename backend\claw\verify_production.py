#!/usr/bin/env python3
"""
简化的生产环境配置验证
"""

import yaml
import os

def verify_production_config():
    """验证生产环境配置"""
    print("Verifying production configuration...")
    
    # 检查文件存在
    files_to_check = ['production_config.yml', 'production_crawler.py']
    
    for filename in files_to_check:
        if os.path.exists(filename):
            print(f"OK: {filename} exists")
        else:
            print(f"ERROR: {filename} missing")
            return False
    
    # 检查配置文件内容
    try:
        with open('production_config.yml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        date_filter = config.get('crawl', {}).get('date_filter', {})
        year_config = date_filter.get('year')
        month_config = date_filter.get('month')
        
        print(f"Year config: {year_config}")
        print(f"Month config: {month_config}")
        
        # 测试多年份逻辑
        if isinstance(year_config, list):
            years = year_config
        else:
            years = [year_config]
        
        if isinstance(month_config, list):
            months = month_config
        else:
            months = [month_config]
        
        total_combinations = len(years) * len(months)
        print(f"Total combinations: {len(years)} years × {len(months)} months = {total_combinations}")
        
        return True
        
    except Exception as e:
        print(f"Configuration error: {e}")
        return False

def show_usage_examples():
    """显示使用示例"""
    print("\nUsage Examples:")
    print("=" * 50)
    
    print("\n1. Single year, all months:")
    print("   year: 2003")
    print("   month: [1,2,3,4,5,6,7,8,9,10,11,12]")
    
    print("\n2. Multiple years, selected months:")
    print("   year: [2003, 2004, 2005]")
    print("   month: [6,7,8]")
    
    print("\n3. Large range (use with caution):")
    print("   year: [2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010]")
    print("   month: [1,2,3,4,5,6,7,8,9,10,11,12]")
    print("   Total: 11 × 12 = 132 combinations")

if __name__ == "__main__":
    print("Production Multi-Year Configuration Verification")
    print("=" * 60)
    
    success = verify_production_config()
    
    if success:
        print("\nSUCCESS: Production configuration is ready for multi-year crawling!")
        
        show_usage_examples()
        
        print("\nTo use multi-year crawling:")
        print("1. Edit production_config.yml")
        print("2. Update date_filter section:")
        print("   year: [2003, 2004, 2005]")
        print("   month: [1,2,3,4,5,6,7,8,9,10,11,12]")
        print("3. Run: python production_crawler.py")
    else:
        print("\nERROR: Production configuration has issues!")
    
    print("\n" + "=" * 60)