# 动漫网站生产环境部署指南

本指南将帮助您在生产环境中部署动漫网站，采用前后端分离架构。

## 📋 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上（推荐8GB）
- **存储**: 50GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **域名**: 已解析到服务器IP的域名（可选）

## 🚀 快速部署

### 1. 克隆项目
```bash
git clone <your-repo-url>
cd anime-website
```

### 2. 配置环境变量

#### 前端配置 (`frontend/.env.production`)
```bash
# 修改API服务器地址
NEXT_PUBLIC_API_BASE_URL=https://your-api-domain.com/api

# 修改网站信息
NEXT_PUBLIC_SITE_NAME=你的网站名称
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

#### 后端配置 (`backend/.env.production`)
```bash
# 修改数据库连接
DATABASE_URL=postgresql://username:password@localhost:5432/anime_website

# 修改JWT密钥（重要！）
SECRET_KEY=your-super-secret-key-change-this-in-production

# 修改CORS设置
ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# 配置邮件服务
SMTP_HOST=smtp.gmail.com
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
```

#### Docker Compose配置 (`docker-compose.production.yml`)
```bash
# 修改数据库密码
POSTGRES_PASSWORD: your_secure_password_here

# 修改Redis密码
redis-server --requirepass your_redis_password_here
```

### 3. SSL证书配置（HTTPS）

#### 使用Let's Encrypt（推荐）
```bash
# 安装certbot
sudo apt install certbot

# 获取SSL证书
sudo certbot certonly --standalone -d your-domain.com -d www.your-domain.com

# 复制证书到nginx目录
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem nginx/ssl/key.pem
```

#### 使用自签名证书（测试用）
```bash
mkdir -p nginx/ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout nginx/ssl/key.pem \
    -out nginx/ssl/cert.pem \
    -subj "/C=CN/ST=State/L=City/O=Organization/CN=your-domain.com"
```

### 4. 修改Nginx配置
编辑 `nginx/nginx.conf`，将 `your-domain.com` 替换为您的实际域名。

### 5. 运行部署脚本
```bash
# 给脚本执行权限
chmod +x deploy.sh

# 执行部署
./deploy.sh
```

## 🔧 高级配置

### 自定义部署选项
```bash
# 仅构建，不启动
./deploy.sh --build-only

# 不使用缓存构建
./deploy.sh --no-cache

# 部署前备份数据库
./deploy.sh --backup

# 组合使用
./deploy.sh --backup --no-cache
```

### 手动部署步骤
如果不使用部署脚本，可以手动执行以下步骤：

```bash
# 1. 构建镜像
docker-compose -f docker-compose.production.yml build

# 2. 启动服务
docker-compose -f docker-compose.production.yml up -d

# 3. 运行数据库迁移
docker-compose -f docker-compose.production.yml exec backend python -m alembic upgrade head

# 4. 查看服务状态
docker-compose -f docker-compose.production.yml ps
```

## 📊 监控和维护

### 查看日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.production.yml logs

# 查看特定服务日志
docker-compose -f docker-compose.production.yml logs frontend
docker-compose -f docker-compose.production.yml logs backend
docker-compose -f docker-compose.production.yml logs postgres
```

### 备份数据库
```bash
# 手动备份
docker-compose -f docker-compose.production.yml exec postgres pg_dump -U anime_user anime_website > backup.sql

# 恢复备份
docker-compose -f docker-compose.production.yml exec -T postgres psql -U anime_user anime_website < backup.sql
```

### 更新应用
```bash
# 拉取最新代码
git pull

# 重新构建和部署
./deploy.sh --no-cache
```

### 扩展服务
```bash
# 扩展后端服务实例
docker-compose -f docker-compose.production.yml up -d --scale backend=3
```

## 🔒 安全配置

### 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 定期安全更新
```bash
# 设置自动更新（Ubuntu）
sudo apt install unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades
```

## 🚨 故障排除

### 常见问题

#### 1. 服务无法启动
```bash
# 检查端口占用
sudo netstat -tlnp | grep :8000
sudo netstat -tlnp | grep :3000

# 检查Docker状态
sudo systemctl status docker
```

#### 2. 数据库连接失败
```bash
# 检查数据库容器状态
docker-compose -f docker-compose.production.yml ps postgres

# 检查数据库日志
docker-compose -f docker-compose.production.yml logs postgres
```

#### 3. SSL证书问题
```bash
# 检查证书文件
ls -la nginx/ssl/

# 测试SSL配置
openssl s_client -connect your-domain.com:443
```

### 性能优化

#### 1. 数据库优化
```sql
-- 在PostgreSQL中创建索引
CREATE INDEX idx_animes_title ON animes(title);
CREATE INDEX idx_animes_created_at ON animes(created_at);
CREATE INDEX idx_favorites_user_id ON favorites(user_id);
```

#### 2. Redis缓存配置
```bash
# 修改Redis配置以提高性能
echo "maxmemory 1gb" >> redis.conf
echo "maxmemory-policy allkeys-lru" >> redis.conf
```

## 📈 监控和分析

### 健康检查
```bash
# 检查服务健康状态
curl http://localhost:8000/health
curl http://localhost:3000
```

### 日志分析
```bash
# 分析访问日志
docker-compose -f docker-compose.production.yml exec nginx tail -f /var/log/nginx/access.log

# 分析错误日志
docker-compose -f docker-compose.production.yml exec nginx tail -f /var/log/nginx/error.log
```

## 📞 支持

如果在部署过程中遇到问题，请：

1. 检查日志文件
2. 确认配置文件正确
3. 验证网络连接
4. 查看Docker容器状态

## 🔄 自动化部署（可选）

### 使用GitHub Actions
创建 `.github/workflows/deploy.yml` 文件实现自动部署：

```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Deploy to server
      run: |
        # 添加部署脚本
        ./deploy.sh
```

---

**注意**: 请确保在生产环境中使用强密码，定期更新系统和依赖，并监控服务状态。
