                                <!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
<title>小巷裡的秘密,韩漫小巷裡的秘密最终话,小巷裡的秘密漫画在线观看,小巷裡的秘密韩国漫画百度云网盘下载,小巷裡的秘密韩漫完整版免费在线观看,18韩漫无羞遮漫画,漫小肆韓漫</title>
<meta name="keywords" content="小巷裡的秘密,韩漫小巷裡的秘密最终话,小巷裡的秘密漫画在线观看,小巷裡的秘密最新一话,小巷裡的秘密韩漫完整版免费在线观看,18韩漫无羞遮漫画,漫小肆韓漫"/>
<meta name="description" content="提供小巷裡的秘密最新话更新,小巷里的秘密是一部韩国，类型为都市|青春|女学生|性感|长腿|多人|御姐的高人气漫画，漫画简介：一起在小巷裡長大的6個青春男女，所有人都沉睡的那一夜，允豪嘗到了打破禁忌的美妙滋味。十年後，同樣的場景，同樣的時間，允豪再次收到邀約並來到了姐姐的房間&hellip;「這次不吃泡麵，改吃姐姐吧!」">

    <link rel="shortcut icon" href="/static/images/favicon.ico">
    <link href="/static/css/style.css" rel="stylesheet" type="text/css">
    <link href="/static/css/userinfo-vendor.css" rel="stylesheet" type="text/css">
    <link href="/static/css/dm5_style.css" rel="stylesheet" type="text/css">
    
<link rel="stylesheet" href="/static/css/jquery-ui-1.7.3.custom.css">

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
    <script src="/static/js/index.js"></script>
</head>

<body class="white">
<!-- 页面头部 -->
<header class="header container-fluid " style="left: 0px;">
    <div class="container">
        <!-- 左侧logo -->
        <a href="/">
            <img class="header-logo" src="/static/images/header-logo.png">
        </a>
        <!-- 左侧菜单标题 -->
        <ul class="header-title">
            <li><a href="/">首页</a></li>
            <li><a href="/booklist?end=0">连载</a></li>
            <li><a href="/booklist?end=1">完结</a></li>
            <li><a href="/update">更新</a></li>
            <li><a href="/rank">排行</a></li>
            <li><a href="/booklist"><i class="icon icon-cat" style="font-size:19px;vertical-align: sub;"></i></a></li>
        </ul>
        <script>
            var url = location.href;
            if (url.indexOf('booklist') > -1) {
                if (url.indexOf('end=0') > -1) {
                    $('.header-title li a').eq(1).addClass('active');
                } else if (url.indexOf('end=1') > -1) {
                    $('.header-title li a').eq(2).addClass('active');
                } else {
                    $('.header-title li a').eq(5).addClass('active');
                }
            }  else if (url.indexOf('rank') > -1) {
                $('.header-title li a').eq(4).addClass('active');
            } else if (url.indexOf('update') > -1) {
                $('.header-title li a').eq(3).addClass('active');
            } else {
                $('.header-title li a').eq(0).addClass('active');
            }
        </script>
        <!-- 搜索栏 -->
        <div class="header-search">
            <input id="txtKeywords" type="text" value="">
            <a id="btnSearch">搜索</a>
            <script>
                $('#btnSearch').click(function () {
                    var keyword = $('#txtKeywords').val();
                    window.location.href = '/search?keyword=' + keyword;
                })
            </script>
        </div>
        <ul class="header-bar">

            <li class="hover">
                <a href="/history" target="_blank">
                    <i class="icon icon-clock"></i>
                    <p>历史</p>
                </a>
            </li>
            <li class="hover">
                <a href="/bookshelf" data-isload="0" target="_blank">
                    <i class="icon icon-fav"></i>
                    <p>收藏</p>
                    <span class="red-sign"></span>
                </a>
            </li>
        </ul>
        <div class="header_login hover">
                        <a href="/login" class="js_header_login_btn">
                            <img data-isload="0" class="header-avatar" src="/static/images/mrtx.gif">
            </a>
        </div>
    </div>
</header>

<div class="">
    <section class="banner_detail">
        <div class="banner_border_bg">
            <img class="banner_detail_bg" src="https://www.wzdhm2.cc/static/upload/book/642/cover.jpg" alt="小巷裡的秘密" style="width: 100%;height:auto;filter: progid:DXImageTransform.Microsoft.Blur(PixelRadius=60, MakeShadow=false);">
        </div>
        <div class="banner_detail_form">
            <div class="cover">
                <img src="https://www.wzdhm2.cc/static/upload/book/642/cover.jpg" alt="小巷裡的秘密">
                <div class="sr-qrcode">
                    <img src="/static/images/wxqrcode.jpg" alt="小巷裡的秘密">
                </div>
            </div>
            <div class="info">
                <h1>小巷裡的秘密</h1>
                <p class="subtitle">别名：小巷里的秘密</p>
                <p class="subtitle">作者：Gyou&amp;鋼鐵王</p>
                <p class="tip">
                    <span class="block">状态：<span>连载中</span></span>
                    <span class="block ticai">地区：<a href="/booklist/?area=1" target="_blank">韩国</a></span>
                    <span class="block">更新时间：2023-09-13</span>
                    <span class="block">点击：1210768</span>
                </p>
                <p class="tip" style="margin-top:10px;">
                    <span class="block">标签：
                                                <a href="/booklist/?tag=都市" target="_blank">都市</a>
                                                <a href="/booklist/?tag=青春" target="_blank">青春</a>
                                                <a href="/booklist/?tag=女学生" target="_blank">女学生</a>
                                                <a href="/booklist/?tag=性感" target="_blank">性感</a>
                                                <a href="/booklist/?tag=长腿" target="_blank">长腿</a>
                                                <a href="/booklist/?tag=多人" target="_blank">多人</a>
                                                <a href="/booklist/?tag=御姐" target="_blank">御姐</a>
                                            </span>
                </p>
                <p class="content" style="position: relative;overflow : hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-box-orient: vertical;">
                    一起在小巷裡長大的6個青春男女，所有人都沉睡的那一夜，允豪嘗到了打破禁忌的美妙滋味。十年後，同樣的場景，同樣的時間，允豪再次收到邀約並來到了姐姐的房間&hellip;「這次不吃泡麵，改吃姐姐吧!」                </p>
                <div class="bottom" style="width: 850px;bottom: 30px;">
                                        <a href="/login" class="btn-1 btn_collection"><i class="icon icon-fav2"></i>收藏</a>
                                            <a href="/chapter/27015" class="btn-2">开始阅读</a>
                                                            </div>
            </div>
            <script>
                $('#addfavor').click(function () {
                    var val = $(this).attr('data-val');
                    $.post({
                        url:`/addfavor`,
                        data:{'book_id' : 642, 'val' : val },
                        success: function (res) {
                            if (res.err == 1){
                                ShowDialog(res.msg);
                            } else {
                                if (res.isfavor == 1){ //表示收藏了，将控件状态改为已收藏
                                    $('a.btn-1').html('<i class="icon icon-fav2-full"></i>已收藏</a>').attr('data-val',1).addClass('active');
                                    ShowDialog('收藏成功');
                                } else { //更改为未收藏
                                    $('a.btn-1').html('<i class="icon icon-fav2"></i>收藏</a>').attr('data-val',0).removeClass('active');
                                    ShowDialog('取消收藏');
                                }
                            }
                        },
                    })
                })

            </script>
            <div style="clear: both;"></div>
        </div>
    </section>
</div>
<div class="view-comment" style="position:relative;">
    <div class="container">
        <div class="view-comment-sub">
            <div class="sub-manga">
                <h2 class="top">阅读排行</h2>
                <ul class="list">
                                        <li>
                        <div class="cover">
                            <a href="/book/418" title="秘密教學">
                                                                <img src="https://www.wzdhm2.cc/static/upload/book/418/cover.jpg" alt="秘密教學">
                                                            </a>
                        </div>
                        <div class="info">
                            <p class="title">
                                <a href="/book/418" title="秘密教學">秘密教學</a>
                            </p>
                            <p class="subtitle-new">13歲那年成為孤兒的子豪，被爸</p>
                            <p class="tip"><span>评分:</span><span class="mh-star-line star-5"></span></p>
                        </div>
                    </li>
                                        <li>
                        <div class="cover">
                            <a href="/book/499" title="社團學姊">
                                                                <img src="https://www.wzdhm2.cc/static/upload/book/499/cover.jpg" alt="社團學姊">
                                                            </a>
                        </div>
                        <div class="info">
                            <p class="title">
                                <a href="/book/499" title="社團學姊">社團學姊</a>
                            </p>
                            <p class="subtitle-new">「你是我的菜!」大一新生李毅傑</p>
                            <p class="tip"><span>评分:</span><span class="mh-star-line star-5"></span></p>
                        </div>
                    </li>
                                        <li>
                        <div class="cover">
                            <a href="/book/634" title="洞洞雜貨店">
                                                                <img src="https://www.wzdhm2.cc/static/upload/book/634/cover.jpg" alt="洞洞雜貨店">
                                                            </a>
                        </div>
                        <div class="info">
         