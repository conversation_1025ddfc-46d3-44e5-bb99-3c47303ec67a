#!/usr/bin/env python3
"""
后端服务器启动脚本
"""
import uvicorn
from app.main import app

if __name__ == "__main__":
    print("正在启动动漫平台后端服务...")
    print("API 文档地址: http://localhost:8001/docs")
    print("API 基础地址: http://localhost:8001/api/v1")
    print("按 Ctrl+C 停止服务")
    print("-" * 50)
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        reload_dirs=["app"],
        log_level="info"
    )
