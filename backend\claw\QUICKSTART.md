# 快速开始指南 (Quick Start Guide)

## 🚀 5分钟快速部署

### 第一步: 环境准备

```bash
# 1. 安装Python依赖
pip install requests beautifulsoup4 lxml selenium cloudscraper pymysql pyyaml

# 2. 安装Chrome浏览器 (如未安装)
# Ubuntu/Debian:
sudo apt-get install google-chrome-stable

# CentOS/RHEL:
sudo yum install google-chrome-stable

# 3. 下载ChromeDriver
# 访问 https://chromedriver.chromium.org/downloads
# 下载与Chrome版本匹配的ChromeDriver并添加到PATH
```

### 第二步: 数据库准备

```sql
-- 1. 创建数据库
CREATE DATABASE sql23721_hentai CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 2. 使用数据库
USE sql23721_hentai;

-- 3. 创建数据表
CREATE TABLE animes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) COMMENT '中文标题',
    title_english VARCHAR(255) COMMENT '英文标题',
    title_japanese VARCHAR(255) COMMENT '日文标题',
    description TEXT COMMENT '简介',
    cover VARCHAR(500) COMMENT '封面URL',
    fanart TEXT COMMENT '剧照URLs',
    video_url VARCHAR(500) COMMENT '视频URL',
    release_year INT COMMENT '发布年份',
    release_date DATE COMMENT '发布日期',
    view_count INT DEFAULT 0 COMMENT '观看次数',
    favorite_count INT DEFAULT 0 COMMENT '收藏次数',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
    category_id INT COMMENT '分类ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE tags (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) UNIQUE COMMENT '标签名称'
);

CREATE TABLE anime_tags (
    anime_id INT,
    tag_id INT,
    PRIMARY KEY (anime_id, tag_id)
);

CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) COMMENT '分类名称',
    num INT DEFAULT 0 COMMENT '数量'
);

-- 4. 插入默认数据
INSERT INTO categories (name, num) VALUES ('里番', 1);
```

### 第三步: 配置文件

编辑 `config.yml`:

```yaml
# 爬取配置 - 修改为你要的年月
crawl:
  date_filter:
    year: 2025    # 目标年份
    month: 1      # 目标月份

# 数据库配置 - 修改为你的MariaDB信息
database:
  host: "************:3306"
  user: "sql23721_hentai"
  password: "507877550@lihao"
  database: "sql23721_hentai"
  charset: "utf8mb4"

# Web访问配置 - 修改为你的域名
web_access:
  domain_prefix: 'https://static.denlu.top'
  base_path: '/downloads'
```

### 第四步: 运行测试

```bash
# 1. 进入项目目录
cd /path/to/anime-website/backend/claw/

# 2. 测试运行 (爬取少量数据)
python test_crawler_jan2025.py

# 3. 验证结果
python verify_db.py
```

### 第五步: 生产运行

```bash
# 运行完整爬虫
python -c "
from unified_crawler import UnifiedCrawler
crawler = UnifiedCrawler('config.yml')
crawler.run_full_crawl()
"
```

## 📁 文件结构说明

运行后会生成以下目录结构：

```
claw/
├── unified_crawler.py          # 主爬虫程序
├── config.yml                  # 配置文件
├── test_crawler_jan2025.py     # 测试脚本
├── verify_db.py               # 数据库验证脚本
├── scraper.log                # 运行日志
├── downloads/                 # 下载目录
│   └── 2025/                 # 年份目录
│       └── 01/               # 月份目录
│           └── 视频标题/      # 视频目录
│               ├── 视频标题.mp4
│               ├── fanart.jpg
│               ├── poster.jpg
│               ├── 视频标题.nfo
│               └── extrafanart/
│                   ├── fanart1.jpg
│                   ├── fanart2.jpg
│                   └── ...
└── README.md                  # 完整文档
```

## 🔧 常用命令

### 爬取特定月份数据

```python
# 修改配置后运行
from unified_crawler import UnifiedCrawler

# 爬取2025年2月数据
crawler = UnifiedCrawler()
crawler.TARGET_YEAR = 2025
crawler.TARGET_MONTHS = [2]
crawler.run_full_crawl()
```

### 按标题爬取单个视频

```python
from unified_crawler import UnifiedCrawler

crawler = UnifiedCrawler()
crawler.crawl_by_title("某个具体的视频标题")
```

### 批量爬取多个月份

```python
from unified_crawler import UnifiedCrawler

crawler = UnifiedCrawler()
crawler.TARGET_YEAR = 2025
crawler.TARGET_MONTHS = [1, 2, 3]  # 爬取1-3月
crawler.run_full_crawl()
```

## 📊 监控和维护

### 检查运行状态

```bash
# 查看日志
tail -f scraper.log

# 统计处理结果
grep "视频信息已保存到数据库" scraper.log | wc -l  # 成功数量
grep "ERROR" scraper.log | wc -l                   # 错误数量
```

### 数据库查询

```sql
-- 查看最新爬取的视频
SELECT id, title, view_count, created_at 
FROM animes 
ORDER BY id DESC 
LIMIT 10;

-- 统计爬取数据
SELECT 
    release_year,
    COUNT(*) as total_videos,
    AVG(view_count) as avg_views
FROM animes 
GROUP BY release_year
ORDER BY release_year DESC;

-- 检查标签分布
SELECT t.name, COUNT(*) as usage_count
FROM tags t
JOIN anime_tags at ON t.id = at.tag_id
GROUP BY t.id
ORDER BY usage_count DESC
LIMIT 20;
```

### 清理和重置

```bash
# 清理下载目录
rm -rf downloads/2025/01/

# 清理数据库记录 (谨慎操作)
```sql
DELETE FROM anime_tags WHERE anime_id IN (
    SELECT id FROM animes WHERE release_year = 2025
);
DELETE FROM animes WHERE release_year = 2025;
```

## ⚡ 性能调优

### 高性能配置

```yaml
# config.yml 优化配置
download:
  max_concurrent: 5        # 增加并发数
  chunk_size: 16384       # 增大块大小

network:
  timeout: 60             # 增加超时时间
  max_retries: 5          # 增加重试次数

performance:
  connection_pool_size: 20  # 增大连接池
```

### 定时任务

```bash
# 添加crontab定时任务
crontab -e

# 每天凌晨2点运行爬虫
0 2 * * * cd /path/to/claw && python -c "from unified_crawler import UnifiedCrawler; UnifiedCrawler().run_full_crawl()" >> /var/log/crawler_cron.log 2>&1
```

### Docker部署 (可选)

```dockerfile
# Dockerfile
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# 安装Chrome
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable

# 安装ChromeDriver
RUN CHROME_VERSION=$(google-chrome --product-version | cut -d. -f1-3) \
    && CHROMEDRIVER_VERSION=$(curl -sS chromedriver.storage.googleapis.com/LATEST_RELEASE_$CHROME_VERSION) \
    && wget -O /tmp/chromedriver.zip https://chromedriver.storage.googleapis.com/$CHROMEDRIVER_VERSION/chromedriver_linux64.zip \
    && unzip /tmp/chromedriver.zip -d /usr/local/bin/ \
    && chmod +x /usr/local/bin/chromedriver

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "unified_crawler.py"]
```

## 🆘 快速故障排除

### 常见错误快速修复

1. **ChromeDriver版本不匹配**:
   ```bash
   # 自动安装匹配版本
   pip install webdriver-manager
   ```

2. **数据库连接失败**:
   ```bash
   # 测试连接
   mysql -h192.168.1.7 -uroot -p123456 -e "SELECT 1"
   ```

3. **网站访问失败**:
   ```bash
   # 检查网站状态
   curl -I https://hanime1.me
   ```

4. **权限不足**:
   ```bash
   # 修复权限
   chmod -R 755 downloads/
   ```

## 📞 获取支持

- 📖 完整文档: 查看 `README.md`
- 🔧 配置说明: 查看 `CONFIGURATION.md`  
- 🐛 故障排除: 查看 `TROUBLESHOOTING.md`
- 💬 问题反馈: 提交 GitHub Issue

---

**恭喜! 🎉 您已成功配置统一爬虫系统。开始享受自动化的视频抓取体验吧！**