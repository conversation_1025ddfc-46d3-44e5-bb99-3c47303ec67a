import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from app.main import app
from app.core.database import get_db
from app.models import Manga, MangaChapter, MangaPage
from tests.conftest import TestingSessionLocal, engine
import json

client = TestClient(app)

# Test data
MANGA_DATA = {
    "title": "Test Manga",
    "description": "A manga for testing infinite scroll",
    "status": "ongoing",
    "manga_type": "serial",
    "is_active": True
}

CHAPTER_1_DATA = {
    "manga_id": 1,
    "chapter_number": 1,
    "title": "Chapter 1",
    "page_count": 10,
    "is_free": True,
    "price": 0,
    "status": "published"
}

CHAPTER_2_DATA = {
    "manga_id": 1,
    "chapter_number": 2,
    "title": "Chapter 2", 
    "page_count": 8,
    "is_free": True,
    "price": 0,
    "status": "published"
}

def create_test_pages(db: Session, chapter_id: int, page_count: int, start_global_seq: int):
    """Helper to create test pages with global sequence numbers"""
    pages = []
    for i in range(1, page_count + 1):
        page_data = {
            "chapter_id": chapter_id,
            "page_number": i,
            "image_url": f"https://example.com/chapter_{chapter_id}_page_{i}.jpg",
            "width": 800,
            "height": 1200
        }
        page = MangaPage(**page_data)
        db.add(page)
        pages.append(page)
    
    db.commit()
    
    # Update with global sequence numbers
    for idx, page in enumerate(pages):
        page.global_sequence_number = start_global_seq + idx
    
    db.commit()
    return pages

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

class TestInfiniteScrollAPI:
    @pytest.fixture(autouse=True)
    def setup_database(self):
        # Create test data
        db = TestingSessionLocal()
        
        # Create manga
        manga = Manga(**MANGA_DATA)
        db.add(manga)
        db.commit()
        db.refresh(manga)
        
        # Create chapters
        chapter1_data = {**CHAPTER_1_DATA, "manga_id": manga.id}
        chapter2_data = {**CHAPTER_2_DATA, "manga_id": manga.id}
        
        chapter1 = MangaChapter(**chapter1_data)
        chapter2 = MangaChapter(**chapter2_data)
        
        db.add(chapter1)
        db.add(chapter2)
        db.commit()
        db.refresh(chapter1)
        db.refresh(chapter2)
        
        # Create pages with global sequence numbers
        chapter1_pages = create_test_pages(db, chapter1.id, 10, 1)
        chapter2_pages = create_test_pages(db, chapter2.id, 8, 11)
        
        # Update chapter global sequence numbers
        chapter1.global_sequence_number = 1
        chapter2.global_sequence_number = 11
        
        db.commit()
        db.close()
        
        yield
        
        # Cleanup
        db = TestingSessionLocal()
        db.query(MangaPage).delete()
        db.query(MangaChapter).delete()
        db.query(Manga).delete()
        db.commit()
        db.close()

    def test_get_infinite_pages_initial_load(self):
        """Test initial infinite scroll page load"""
        response = client.get(
            "/api/v1/manga/1/infinite-pages",
            params={
                "current_chapter_id": 1,
                "current_page": 1,
                "pages_ahead": 5,
                "pages_behind": 2
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Check response structure
        assert "pages" in data
        assert "chapters" in data
        assert "total_pages" in data
        assert "current_global_position" in data
        
        # Check page data
        assert len(data["pages"]) <= 8  # 5 ahead + 2 behind + current = max 8
        assert data["total_pages"] == 18  # 10 + 8 pages total
        assert data["current_global_position"] == 1
        
        # Check first page data
        first_page = data["pages"][0]
        assert first_page["page_number"] == 1
        assert first_page["global_sequence_number"] == 1
        assert "chapter" in first_page
        assert first_page["chapter"]["chapter_number"] == 1

    def test_get_infinite_pages_with_chapter_transition(self):
        """Test loading pages across chapter boundaries"""
        response = client.get(
            "/api/v1/manga/1/infinite-pages",
            params={
                "current_chapter_id": 1,
                "current_page": 8,  # Near end of chapter 1
                "pages_ahead": 5,
                "pages_behind": 2
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Should include pages from both chapters
        page_chapter_ids = [page["chapter_id"] for page in data["pages"]]
        assert 1 in page_chapter_ids  # Chapter 1
        assert 2 in page_chapter_ids  # Chapter 2 (due to pages_ahead)
        
        # Should include chapter info for both chapters
        chapter_ids = [ch["id"] for ch in data["chapters"]]
        assert 1 in chapter_ids
        assert 2 in chapter_ids

    def test_get_infinite_pages_from_second_chapter(self):
        """Test loading from middle of second chapter"""
        response = client.get(
            "/api/v1/manga/1/infinite-pages",
            params={
                "current_chapter_id": 2,
                "current_page": 3,
                "pages_ahead": 3,
                "pages_behind": 3
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Current position should be in chapter 2
        assert data["current_global_position"] == 13  # 10 from ch1 + 3 from ch2
        
        # Should include pages from both chapters due to pages_behind
        page_global_seqs = [p["global_sequence_number"] for p in data["pages"]]
        assert min(page_global_seqs) <= 13  # Should go back into chapter 1
        assert max(page_global_seqs) >= 13  # Should go forward in chapter 2

    def test_get_infinite_pages_at_beginning(self):
        """Test loading at the very beginning of manga"""
        response = client.get(
            "/api/v1/manga/1/infinite-pages", 
            params={
                "current_chapter_id": 1,
                "current_page": 1,
                "pages_ahead": 5,
                "pages_behind": 5  # Should be limited to 0 at beginning
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Should start from page 1
        global_seqs = [p["global_sequence_number"] for p in data["pages"]]
        assert min(global_seqs) == 1
        
        # Should not go before first page
        assert all(seq >= 1 for seq in global_seqs)

    def test_get_infinite_pages_at_end(self):
        """Test loading at the very end of manga"""
        response = client.get(
            "/api/v1/manga/1/infinite-pages",
            params={
                "current_chapter_id": 2,
                "current_page": 8,  # Last page
                "pages_ahead": 5,  # Should be limited
                "pages_behind": 3
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Should end at last page
        global_seqs = [p["global_sequence_number"] for p in data["pages"]]
        assert max(global_seqs) == 18  # Total pages
        
        # Should not go beyond last page
        assert all(seq <= 18 for seq in global_seqs)

    def test_get_infinite_pages_with_default_parameters(self):
        """Test with default parameter values"""
        response = client.get(
            "/api/v1/manga/1/infinite-pages",
            params={
                "current_chapter_id": 1,
                "current_page": 5
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Should use default values (typically 10 ahead, 5 behind)
        assert len(data["pages"]) >= 1
        assert data["current_global_position"] == 5

    def test_get_infinite_pages_invalid_manga(self):
        """Test with non-existent manga"""
        response = client.get(
            "/api/v1/manga/999/infinite-pages",
            params={
                "current_chapter_id": 1,
                "current_page": 1
            }
        )
        
        assert response.status_code == 404

    def test_get_infinite_pages_invalid_chapter(self):
        """Test with non-existent chapter"""
        response = client.get(
            "/api/v1/manga/1/infinite-pages",
            params={
                "current_chapter_id": 999,
                "current_page": 1
            }
        )
        
        assert response.status_code == 404

    def test_get_infinite_pages_invalid_page_number(self):
        """Test with invalid page number"""
        response = client.get(
            "/api/v1/manga/1/infinite-pages",
            params={
                "current_chapter_id": 1,
                "current_page": 999  # Beyond chapter page count
            }
        )
        
        assert response.status_code == 400
        assert "page number" in response.json()["detail"].lower()

    def test_get_infinite_pages_zero_parameters(self):
        """Test with zero ahead/behind parameters"""
        response = client.get(
            "/api/v1/manga/1/infinite-pages",
            params={
                "current_chapter_id": 1,
                "current_page": 5,
                "pages_ahead": 0,
                "pages_behind": 0
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Should return at least current page
        assert len(data["pages"]) >= 1
        assert any(p["page_number"] == 5 and p["chapter_id"] == 1 for p in data["pages"])

    def test_get_infinite_pages_large_parameters(self):
        """Test with large ahead/behind parameters"""
        response = client.get(
            "/api/v1/manga/1/infinite-pages",
            params={
                "current_chapter_id": 1,
                "current_page": 5,
                "pages_ahead": 100,
                "pages_behind": 100
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Should return all available pages without error
        assert len(data["pages"]) == 18  # All pages in manga
        
        # Should include all chapters
        assert len(data["chapters"]) == 2

    def test_get_infinite_pages_response_structure(self):
        """Test complete response structure and data types"""
        response = client.get(
            "/api/v1/manga/1/infinite-pages",
            params={
                "current_chapter_id": 1,
                "current_page": 3,
                "pages_ahead": 3,
                "pages_behind": 2
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Check page structure
        for page in data["pages"]:
            assert "id" in page
            assert "chapter_id" in page
            assert "page_number" in page
            assert "image_url" in page
            assert "global_sequence_number" in page
            assert "chapter" in page
            
            # Check nested chapter structure
            chapter = page["chapter"]
            assert "id" in chapter
            assert "manga_id" in chapter
            assert "chapter_number" in chapter
            assert "page_count" in chapter
            assert "status" in chapter
            
        # Check chapters structure
        for chapter in data["chapters"]:
            assert "id" in chapter
            assert "manga_id" in chapter
            assert "chapter_number" in chapter
            assert "page_count" in chapter
            assert "global_sequence_number" in chapter

    def test_get_infinite_pages_ordering(self):
        """Test that pages are returned in correct order"""
        response = client.get(
            "/api/v1/manga/1/infinite-pages",
            params={
                "current_chapter_id": 1,
                "current_page": 5,
                "pages_ahead": 5,
                "pages_behind": 3
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Pages should be ordered by global sequence number
        global_seqs = [p["global_sequence_number"] for p in data["pages"]]
        assert global_seqs == sorted(global_seqs)
        
        # Chapters should be ordered by global sequence number
        chapter_seqs = [c["global_sequence_number"] for c in data["chapters"]]
        assert chapter_seqs == sorted(chapter_seqs)

    def test_get_infinite_pages_performance(self):
        """Test response time for infinite scroll endpoint"""
        import time
        
        start_time = time.time()
        
        response = client.get(
            "/api/v1/manga/1/infinite-pages",
            params={
                "current_chapter_id": 1,
                "current_page": 5,
                "pages_ahead": 10,
                "pages_behind": 5
            }
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        assert response.status_code == 200
        # Should respond within 1 second for small dataset
        assert response_time < 1.0

    def test_get_infinite_pages_caching_headers(self):
        """Test appropriate caching headers for infinite scroll"""
        response = client.get(
            "/api/v1/manga/1/infinite-pages",
            params={
                "current_chapter_id": 1,
                "current_page": 1
            }
        )
        
        assert response.status_code == 200
        
        # Should have appropriate cache headers for dynamic content
        # This depends on your caching strategy
        headers = response.headers
        # Example assertions (adjust based on your caching policy):
        # assert "Cache-Control" in headers
        # assert "no-cache" in headers.get("Cache-Control", "")

    def test_get_infinite_pages_concurrent_requests(self):
        """Test handling of concurrent requests"""
        import threading
        import time
        
        responses = []
        errors = []
        
        def make_request():
            try:
                response = client.get(
                    "/api/v1/manga/1/infinite-pages",
                    params={
                        "current_chapter_id": 1,
                        "current_page": 3,
                        "pages_ahead": 5,
                        "pages_behind": 2
                    }
                )
                responses.append(response)
            except Exception as e:
                errors.append(e)
        
        # Create 10 concurrent requests
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All requests should succeed
        assert len(errors) == 0
        assert len(responses) == 10
        
        # All responses should be successful and consistent
        for response in responses:
            assert response.status_code == 200
            data = response.json()
            assert data["current_global_position"] == 3
            assert len(data["pages"]) > 0

    def test_get_infinite_pages_edge_case_single_page_chapter(self):
        """Test with single page chapter"""
        # Create a single page chapter
        db = TestingSessionLocal()
        
        chapter3_data = {
            "manga_id": 1,
            "chapter_number": 3,
            "title": "Single Page Chapter",
            "page_count": 1,
            "is_free": True,
            "price": 0,
            "status": "published"
        }
        
        chapter3 = MangaChapter(**chapter3_data)
        db.add(chapter3)
        db.commit()
        db.refresh(chapter3)
        
        # Create single page
        page_data = {
            "chapter_id": chapter3.id,
            "page_number": 1,
            "image_url": "https://example.com/single_page.jpg",
            "global_sequence_number": 19
        }
        
        page = MangaPage(**page_data)
        db.add(page)
        
        chapter3.global_sequence_number = 19
        db.commit()
        db.close()
        
        # Test loading from single page chapter
        response = client.get(
            "/api/v1/manga/1/infinite-pages",
            params={
                "current_chapter_id": chapter3.id,
                "current_page": 1,
                "pages_ahead": 2,
                "pages_behind": 2
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Should handle single page chapter correctly
        assert data["current_global_position"] == 19
        assert any(p["chapter_id"] == chapter3.id for p in data["pages"])

    def test_get_infinite_pages_memory_efficiency(self):
        """Test memory efficiency with large parameters"""
        response = client.get(
            "/api/v1/manga/1/infinite-pages",
            params={
                "current_chapter_id": 1,
                "current_page": 1,
                "pages_ahead": 50,  # Large but reasonable
                "pages_behind": 50
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Should not return excessive data
        assert len(data["pages"]) <= 100  # Reasonable limit
        
        # Response should not be excessively large
        response_size = len(response.content)
        assert response_size < 1024 * 1024  # Less than 1MB