#!/usr/bin/env python3
"""
Comprehensive test runner for the anime website backend.

Runs different categories of tests with appropriate configurations.
"""

import os
import sys
import subprocess
import argparse
import time
from typing import List, Dict, Any

class TestRunner:
    """Test runner with different test categories"""

    def __init__(self):
        self.base_dir = os.path.dirname(os.path.abspath(__file__))
        self.results = {}

    def run_command(self, command: List[str], description: str) -> bool:
        """Run a command and return success status"""
        print(f"\n{'='*60}")
        print(f"Running: {description}")
        print(f"Command: {' '.join(command)}")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        try:
            result = subprocess.run(
                command,
                cwd=self.base_dir,
                capture_output=False,
                text=True,
                check=True
            )
            
            duration = time.time() - start_time
            print(f"\n✅ {description} completed successfully in {duration:.2f}s")
            self.results[description] = {
                "status": "PASSED",
                "duration": duration,
                "return_code": result.returncode
            }
            return True
            
        except subprocess.CalledProcessError as e:
            duration = time.time() - start_time
            print(f"\n❌ {description} failed in {duration:.2f}s")
            print(f"Return code: {e.returncode}")
            self.results[description] = {
                "status": "FAILED", 
                "duration": duration,
                "return_code": e.returncode
            }
            return False

    def run_unit_tests(self) -> bool:
        """Run unit tests"""
        command = [
            "python", "-m", "pytest", 
            "tests/test_user_password.py",
            "tests/test_user_account_deletion.py", 
            "-v", 
            "--tb=short",
            "-m", "not integration and not e2e and not slow"
        ]
        
        return self.run_command(command, "Unit Tests")

    def run_integration_tests(self) -> bool:
        """Run integration tests"""
        command = [
            "python", "-m", "pytest",
            "tests/test_user_integration.py",
            "-v",
            "--tb=short", 
            "-m", "integration"
        ]
        
        return self.run_command(command, "Integration Tests")

    def run_security_tests(self) -> bool:
        """Run security validation tests"""
        command = [
            "python", "-m", "pytest",
            "tests/test_security_validation.py",
            "-v",
            "--tb=short",
            "-m", "security"
        ]
        
        return self.run_command(command, "Security Tests")

    def run_all_backend_tests(self) -> bool:
        """Run all backend tests"""
        command = [
            "python", "-m", "pytest",
            "tests/",
            "-v",
            "--tb=short",
            "--cov=app",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov"
        ]
        
        return self.run_command(command, "All Backend Tests")

    def run_lint_and_format_check(self) -> bool:
        """Run code quality checks"""
        # Check if we have these tools installed
        checks = []
        
        # Try flake8 for linting
        try:
            subprocess.run(["flake8", "--version"], capture_output=True, check=True)
            checks.append((
                ["flake8", "app/", "tests/", "--max-line-length=100", "--ignore=E203,W503"],
                "Flake8 Linting"
            ))
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  flake8 not found, skipping linting")

        # Try black for format checking  
        try:
            subprocess.run(["black", "--version"], capture_output=True, check=True)
            checks.append((
                ["black", "--check", "app/", "tests/"],
                "Black Format Check"
            ))
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  black not found, skipping format check")

        # Try mypy for type checking
        try:
            subprocess.run(["mypy", "--version"], capture_output=True, check=True)
            checks.append((
                ["mypy", "app/", "--ignore-missing-imports"],
                "MyPy Type Check"
            ))
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  mypy not found, skipping type check")

        if not checks:
            print("⚠️  No code quality tools found, skipping quality checks")
            return True

        all_passed = True
        for command, description in checks:
            if not self.run_command(command, description):
                all_passed = False

        return all_passed

    def print_summary(self):
        """Print test summary"""
        print(f"\n{'='*60}")
        print("TEST SUMMARY")
        print(f"{'='*60}")
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results.values() if r["status"] == "PASSED")
        failed_tests = total_tests - passed_tests
        total_duration = sum(r["duration"] for r in self.results.values())
        
        print(f"Total test suites: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Total duration: {total_duration:.2f}s")
        print()
        
        for description, result in self.results.items():
            status_icon = "✅" if result["status"] == "PASSED" else "❌"
            print(f"{status_icon} {description:<40} {result['duration']:>8.2f}s")
        
        if failed_tests > 0:
            print(f"\n❌ {failed_tests} test suite(s) failed!")
            return False
        else:
            print(f"\n🎉 All {passed_tests} test suite(s) passed!")
            return True

def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description="Run tests for anime website backend")
    parser.add_argument(
        "--category",
        choices=["unit", "integration", "security", "quality", "all"],
        default="all",
        help="Category of tests to run"
    )
    parser.add_argument(
        "--fail-fast",
        action="store_true",
        help="Stop on first failure"
    )
    parser.add_argument(
        "--verbose",
        action="store_true", 
        help="Verbose output"
    )

    args = parser.parse_args()
    
    runner = TestRunner()
    
    print("🧪 Starting test execution for anime website backend")
    print(f"Category: {args.category}")
    print(f"Fail fast: {args.fail_fast}")
    
    success = True
    
    if args.category in ["unit", "all"]:
        if not runner.run_unit_tests() and args.fail_fast:
            success = False
        elif not runner.run_unit_tests():
            success = False

    if args.category in ["integration", "all"]:
        if not runner.run_integration_tests() and args.fail_fast:
            success = False
        elif not runner.run_integration_tests():
            success = False

    if args.category in ["security", "all"]:
        if not runner.run_security_tests() and args.fail_fast:
            success = False
        elif not runner.run_security_tests():
            success = False

    if args.category in ["quality", "all"]:
        if not runner.run_lint_and_format_check() and args.fail_fast:
            success = False
        elif not runner.run_lint_and_format_check():
            success = False

    if args.category == "all":
        # Run comprehensive test suite
        if not runner.run_all_backend_tests():
            success = False

    # Print summary
    overall_success = runner.print_summary()
    
    if success and overall_success:
        print("\n🎉 All tests completed successfully!")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()