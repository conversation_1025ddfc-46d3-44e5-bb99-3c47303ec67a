from sqlalchemy.orm import Session
from typing import List, Optional

from .anime import AnimeCRUD, FavoriteCRUD
from app.models import Comment, CommentLike


class CommentCRUD:
    @staticmethod
    def list_by_anime(db: Session, anime_id: int, skip: int = 0, limit: int = 50) -> List[Comment]:
        # 只获取顶级评论（没有父评论的评论），包含用户信息
        from sqlalchemy.orm import joinedload
        return db.query(Comment).options(
            joinedload(Comment.user),
            joinedload(Comment.reply_to_user)
        ).filter(
            Comment.anime_id == anime_id,
            Comment.is_deleted == False,
            Comment.parent_id.is_(None)  # 只获取顶级评论
        ).order_by(Comment.created_at.desc()).offset(skip).limit(limit).all()
    
    @staticmethod
    def list_by_manga(db: Session, manga_id: int, skip: int = 0, limit: int = 50) -> List[Comment]:
        # 只获取顶级评论（没有父评论的评论），包含用户信息
        from sqlalchemy.orm import joinedload
        return db.query(Comment).options(
            joinedload(Comment.user),
            joinedload(Comment.reply_to_user)
        ).filter(
            Comment.manga_id == manga_id,
            Comment.is_deleted == False,
            Comment.parent_id.is_(None)  # 只获取顶级评论
        ).order_by(Comment.created_at.desc()).offset(skip).limit(limit).all()
    
    @staticmethod
    def list_replies(db: Session, parent_id: int, skip: int = 0, limit: int = 50) -> List[Comment]:
        # 获取特定评论的回复，包含用户信息
        from sqlalchemy.orm import joinedload
        return db.query(Comment).options(
            joinedload(Comment.user),
            joinedload(Comment.reply_to_user)
        ).filter(
            Comment.parent_id == parent_id,
            Comment.is_deleted == False
        ).order_by(Comment.created_at.asc()).offset(skip).limit(limit).all()
    
    @staticmethod
    def get_comment_with_replies(db: Session, comment_id: int) -> Optional[Comment]:
        # 获取评论及其所有回复的递归结构
        comment = db.query(Comment).filter(
            Comment.id == comment_id,
            Comment.is_deleted == False
        ).first()
        
        if comment:
            # 递归获取回复
            comment.replies = CommentCRUD._get_replies_recursive(db, comment_id)
        
        return comment
    
    @staticmethod
    def _get_replies_recursive(db: Session, parent_id: int) -> List[Comment]:
        # 递归获取所有子评论，包含用户信息
        from sqlalchemy.orm import joinedload
        replies = db.query(Comment).options(
            joinedload(Comment.user),
            joinedload(Comment.reply_to_user)
        ).filter(
            Comment.parent_id == parent_id,
            Comment.is_deleted == False
        ).order_by(Comment.created_at.asc()).all()
        
        for reply in replies:
            reply.replies = CommentCRUD._get_replies_recursive(db, reply.id)
        
        return replies

    @staticmethod
    def create(db: Session, user_id: int, anime_id: int, content: str,
               attachments: Optional[List[str]] = None,
               parent_id: Optional[int] = None,
               reply_to_user_id: Optional[int] = None,
               quoted_comment_id: Optional[int] = None,
               quoted_content: Optional[str] = None) -> Comment:
        import json
        
        # 如果有引用评论ID但没有引用内容，自动获取
        if quoted_comment_id and not quoted_content:
            quoted_comment = db.query(Comment).filter(Comment.id == quoted_comment_id).first()
            if quoted_comment:
                quoted_content = quoted_comment.content
        
        comment = Comment(
            user_id=user_id,
            anime_id=anime_id,
            content=content,
            attachments=(None if not attachments else json.dumps(attachments)),
            parent_id=parent_id,
            reply_to_user_id=reply_to_user_id,
            quoted_comment_id=quoted_comment_id,
            quoted_content=quoted_content
        )
        db.add(comment)
        db.commit()
        db.refresh(comment)
        return comment

    @staticmethod
    def create_manga_comment(db: Session, comment, user_id: int) -> Comment:
        """创建漫画评论"""
        import json
        
        # 如果有引用评论ID但没有引用内容，自动获取
        quoted_content = comment.quoted_content
        if comment.quoted_comment_id and not quoted_content:
            quoted_comment = db.query(Comment).filter(Comment.id == comment.quoted_comment_id).first()
            if quoted_comment:
                quoted_content = quoted_comment.content
        
        db_comment = Comment(
            user_id=user_id,
            manga_id=comment.manga_id,  # 设置漫画ID而不是动漫ID
            content=comment.content,
            attachments=(None if not comment.attachments else json.dumps(comment.attachments)),
            parent_id=comment.parent_id,
            reply_to_user_id=comment.reply_to_user_id,
            quoted_comment_id=comment.quoted_comment_id,
            quoted_content=quoted_content
        )
        db.add(db_comment)
        db.commit()
        db.refresh(db_comment)
        return db_comment

    @staticmethod
    def soft_delete(db: Session, comment_id: int, user_id: int) -> bool:
        comment = db.query(Comment).filter(Comment.id == comment_id, Comment.user_id == user_id).first()
        if not comment:
            return False
        comment.is_deleted = True
        db.commit()
        return True
    
    @staticmethod
    def get_by_id(db: Session, comment_id: int) -> Optional[Comment]:
        from sqlalchemy.orm import joinedload
        return db.query(Comment).options(
            joinedload(Comment.user),
            joinedload(Comment.reply_to_user)
        ).filter(
            Comment.id == comment_id,
            Comment.is_deleted == False
        ).first()
    
    @staticmethod
    def update_comment(db: Session, comment_id: int, user_id: int, content: str) -> Optional[Comment]:
        """更新用户自己的评论"""
        from datetime import datetime
        
        comment = db.query(Comment).filter(
            Comment.id == comment_id,
            Comment.user_id == user_id,
            Comment.is_deleted == False
        ).first()
        
        if not comment:
            return None
            
        comment.content = content
        comment.is_edited = True
        comment.edited_at = datetime.now()
        comment.edit_count += 1
        
        db.commit()
        db.refresh(comment)
        return comment


class CommentLikeCRUD:
    @staticmethod
    def get_user_like_status(db: Session, comment_id: int, user_id: int) -> Optional[bool]:
        """获取用户对评论的点赞状态"""
        like = db.query(CommentLike).filter(
            CommentLike.comment_id == comment_id,
            CommentLike.user_id == user_id
        ).first()
        
        return like.is_like if like else None
    
    @staticmethod
    def toggle_like(db: Session, comment_id: int, user_id: int, is_like: bool) -> bool:
        """切换用户对评论的点赞状态"""
        # 检查评论是否存在
        comment = db.query(Comment).filter(Comment.id == comment_id, Comment.is_deleted == False).first()
        if not comment:
            return False
        
        # 查找现有的点赞记录
        existing_like = db.query(CommentLike).filter(
            CommentLike.comment_id == comment_id,
            CommentLike.user_id == user_id
        ).first()
        
        if existing_like:
            if existing_like.is_like == is_like:
                # 如果重复点击相同的按钮，则取消点赞
                db.delete(existing_like)
                # 更新统计
                if is_like:
                    comment.like_count = max(0, comment.like_count - 1)
                else:
                    comment.dislike_count = max(0, comment.dislike_count - 1)
            else:
                # 如果点击相反的按钮，则改变状态
                old_is_like = existing_like.is_like
                existing_like.is_like = is_like
                # 更新统计
                if old_is_like:
                    comment.like_count = max(0, comment.like_count - 1)
                    comment.dislike_count += 1
                else:
                    comment.dislike_count = max(0, comment.dislike_count - 1)
                    comment.like_count += 1
        else:
            # 创建新的点赞记录
            new_like = CommentLike(
                comment_id=comment_id,
                user_id=user_id,
                is_like=is_like
            )
            db.add(new_like)
            # 更新统计
            if is_like:
                comment.like_count += 1
            else:
                comment.dislike_count += 1
        
        db.commit()
        return True
    
    @staticmethod
    def get_comment_like_stats(db: Session, comment_id: int) -> dict:
        """获取评论的点赞统计信息"""
        comment = db.query(Comment).filter(Comment.id == comment_id).first()
        if not comment:
            return {"like_count": 0, "dislike_count": 0}
        
        return {
            "like_count": comment.like_count,
            "dislike_count": comment.dislike_count
        }


# 空文件