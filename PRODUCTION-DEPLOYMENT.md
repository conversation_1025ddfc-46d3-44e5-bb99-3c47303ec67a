# 动漫平台生产环境部署文档

## 📋 目录

1. [环境要求](#环境要求)
2. [环境配置](#环境配置)
3. [生产环境部署步骤](#生产环境部署步骤)
4. [常见问题解决](#常见问题解决)
5. [性能优化建议](#性能优化建议)
6. [清理不必要文件](#清理不必要文件)

---

## 🛠️ 环境要求

### 服务器最低配置要求
- **CPU**: 2核心
- **内存**: 4GB RAM
- **硬盘**: 20GB 可用空间
- **带宽**: 10Mbps 上行带宽
- **操作系统**: Linux (推荐 Ubuntu 20.04+) 或 Windows Server

### 软件依赖
- **Node.js**: 18.17.0+ (推荐 20.x LTS)
- **Python**: 3.8+
- **MySQL**: 8.0+ 或 MariaDB 10.4+
- **Nginx**: 1.18+ (可选，用于反向代理)

---

## ⚙️ 环境配置

### 1. 前端环境配置

#### 生产环境变量文件 (`frontend/.env.production`)
```bash
# API 配置
NEXT_PUBLIC_API_BASE_URL=https://your-backend-domain.com
# 或使用 IP: NEXT_PUBLIC_API_BASE_URL=http://*************:8000

# 生产环境设置
NODE_ENV=production
NEXT_PUBLIC_DISABLE_DEVTOOLS=true
NEXT_PUBLIC_DISABLE_CONSOLE=true

# 跨域配置 (解决您遇到的错误)
NEXT_PUBLIC_ALLOWED_ORIGINS=*************,your-domain.com

# 分析工具 (可选)
NEXT_PUBLIC_GA_ID=your-google-analytics-id
```

#### Next.js 配置更新 (`frontend/next.config.ts`)
```typescript
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // 🔧 修复跨域问题
  allowedDevOrigins: [
    '*************:3000',  // 您的服务器 IP
    'localhost:3000',
    'your-domain.com:3000'
  ],
  
  // 生产环境优化
  poweredByHeader: false,
  compress: true,
  reactStrictMode: true,
  
  // 构建时配置
  eslint: {
    ignoreDuringBuilds: true, // 生产环境跳过 ESLint 检查
  },
  typescript: {
    ignoreBuildErrors: false, // 生产环境保持类型检查
  },
  
  // 图片优化
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
      {
        protocol: 'http',
        hostname: '*************', // 您的服务器 IP
      },
    ],
    unoptimized: false,
  },
  
  // 输出配置
  output: 'standalone', // 生产环境优化
};

export default nextConfig;
```

### 2. 后端环境配置

#### 生产环境变量文件 (`backend/.env.production`)
```bash
# 环境设置
ENVIRONMENT=production

# 数据库配置 (使用您的云数据库)
DATABASE_URL=mysql+pymysql://sql23721_hentai:507877550%40lihao@************:3306/sql23721_hentai?charset=utf8mb4

# JWT 配置
SECRET_KEY=your-super-secure-production-secret-key-change-me
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS 配置 (解决跨域问题)
CORS_ORIGINS=http://*************:3000,https://your-domain.com,http://localhost:3000

# 数据库连接池配置 (生产环境优化)
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_RECYCLE=3600
DB_POOL_PRE_PING=true

# 日志配置
DEBUG=false
LOG_LEVEL=INFO
```

#### Python 依赖文件 (`backend/requirements.txt`)
```txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
sqlalchemy==2.0.23
pymysql==1.1.0
cryptography==41.0.7
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
pydantic==2.5.0
pydantic-settings==2.1.0
alembic==1.13.1
python-dotenv==1.0.0
```

---

## 🚀 生产环境部署步骤

### 步骤 1: 服务器准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装 Node.js 20.x
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 Python 3 和 pip
sudo apt install python3 python3-pip python3-venv -y

# 验证安装
node --version  # 应显示 v20.x
python3 --version  # 应显示 3.8+
```

### 步骤 2: 后端部署

```bash
# 1. 进入后端目录
cd /path/to/your/anime-website/backend

# 2. 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 Windows: venv\Scripts\activate

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境变量
cp .env.example .env.production
# 编辑 .env.production 文件

# 5. 运行数据库迁移
alembic upgrade head

# 6. 启动后端服务
python start.py
# 或使用 systemd 服务 (推荐)
```

### 步骤 3: 前端部署

```bash
# 1. 进入前端目录
cd /path/to/your/anime-website/frontend

# 2. 安装依赖
npm ci --production

# 3. 配置环境变量
cp .env.example .env.production
# 编辑 .env.production 文件

# 4. 构建生产版本
npm run build

# 5. 启动前端服务
npm run start
# 服务将运行在 http://localhost:3000
```

### 步骤 4: 配置系统服务 (推荐)

#### 后端服务配置 (`/etc/systemd/system/anime-backend.service`)
```ini
[Unit]
Description=Anime Platform Backend
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/your/anime-website/backend
Environment=PATH=/path/to/your/anime-website/backend/venv/bin
ExecStart=/path/to/your/anime-website/backend/venv/bin/python start.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### 前端服务配置 (`/etc/systemd/system/anime-frontend.service`)
```ini
[Unit]
Description=Anime Platform Frontend
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/your/anime-website/frontend
ExecStart=/usr/bin/npm run start
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
```

#### 启用服务
```bash
sudo systemctl enable anime-backend
sudo systemctl enable anime-frontend
sudo systemctl start anime-backend
sudo systemctl start anime-frontend
```

### 步骤 5: 配置 Nginx 反向代理 (可选)

#### Nginx 配置 (`/etc/nginx/sites-available/anime-platform`)
```nginx
server {
    listen 80;
    server_name your-domain.com *************;

    # 前端
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 后端 API
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态文件
    location /static/ {
        proxy_pass http://localhost:8000;
    }
}
```

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/anime-platform /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

---

## 🔧 常见问题解决

### 1. 跨域请求被阻止错误

**错误信息**: `⚠ Blocked cross-origin request from ************* to /_next/* resource`

**解决方案**:
1. 更新 `frontend/next.config.ts` 中的 `allowedDevOrigins`
2. 确保后端 CORS 配置包含您的服务器 IP
3. 如果使用 Nginx，确保正确设置代理头

**具体配置**:
```typescript
// frontend/next.config.ts
allowedDevOrigins: [
  '*************:3000',  // 您的服务器 IP
  'your-domain.com:3000'
]

// backend/.env.production
CORS_ORIGINS=http://*************:3000,https://your-domain.com
```

### 2. 数据库连接失败

**检查步骤**:
1. 确认数据库服务正在运行
2. 检查连接字符串格式
3. 验证防火墙设置
4. 测试数据库连接

**测试连接**:
```bash
cd backend
python -c "from app.core.database import engine; print('Database connected!' if engine.connect() else 'Connection failed!')"
```

### 3. 构建失败

**常见原因**:
- Node.js 版本不兼容
- 依赖版本冲突
- 内存不足

**解决方案**:
```bash
# 清理缓存
npm cache clean --force
rm -rf node_modules package-lock.json
npm install

# 增加构建内存
NODE_OPTIONS="--max-old-space-size=4096" npm run build
```

### 4. 静态文件无法加载

**检查**:
1. 文件路径是否正确
2. 权限设置是否合适
3. Nginx 配置是否正确

---

## 📈 性能优化建议

### 1. 前端优化
- 启用 Gzip/Brotli 压缩
- 配置 CDN 加速静态资源
- 使用 Next.js Image 组件优化图片
- 实施代码分割和懒加载

### 2. 后端优化
- 配置 Redis 缓存
- 优化数据库查询
- 使用连接池
- 启用 Gunicorn/Uvicorn workers

### 3. 数据库优化
- 添加适当的索引
- 定期清理日志
- 配置主从复制
- 监控查询性能

---

## 🧹 清理不必要文件

### 需要删除的测试和开发文件

**前端测试文件**:
```bash
# Jest 测试文件
frontend/jest.config.js
frontend/jest.setup.js

# Playwright 测试文件
frontend/playwright.config.ts
frontend/tests/ (如果存在)
frontend/__tests__/ (如果存在)

# 开发配置文件 (保留源文件但不部署)
frontend/.eslintrc.json (可保留但不必要)
frontend/tailwind.config.ts (构建后不需要)
```

**后端测试文件**:
```bash
# 测试目录
backend/tests/ (如果存在)
backend/test_*.py (如果存在)

# 开发工具
backend/pytest.ini (如果存在)
backend/.coverage (如果存在)
```

**Docker 相关文件**:
```bash
# 删除 Docker 配置 (您说不需要)
docker-compose.prod.yml
docker-compose.yml (如果存在)
Dockerfile (如果存在)
.dockerignore (如果存在)
```

**其他开发文件**:
```bash
# 版本控制
.gitignore (部署时可保留)
.git/ (部署时可删除以减小体积)

# 编辑器配置
.vscode/ (可删除)
.idea/ (可删除)

# 日志文件
*.log
logs/ (如果存在)

# 临时文件
.DS_Store
Thumbs.db
```

### 删除命令示例
```bash
# 进入项目目录后执行
cd anime-website

# 删除前端测试文件
rm -f frontend/jest.config.js
rm -f frontend/jest.setup.js
rm -f frontend/playwright.config.ts
rm -rf frontend/tests/
rm -rf frontend/__tests__/

# 删除 Docker 文件
rm -f docker-compose.prod.yml
rm -f docker-compose.yml

# 删除开发工具文件
rm -rf .vscode/
rm -rf .idea/
find . -name ".DS_Store" -delete
find . -name "Thumbs.db" -delete
```

### 生产环境建议保留的关键文件

**前端必需文件**:
- `package.json` 和 `package-lock.json`
- `next.config.ts`
- `.env.production`
- `src/` 目录及所有源代码
- `public/` 目录及静态资源

**后端必需文件**:
- `requirements.txt`
- `start.py`
- `app/` 目录及所有源代码
- `alembic/` 目录及迁移文件
- `.env.production`

---

## 🔍 部署后验证清单

- [ ] 前端服务正常启动 (http://your-server:3000)
- [ ] 后端服务正常启动 (http://your-server:8000)
- [ ] API 文档可访问 (http://your-server:8000/docs)
- [ ] 数据库连接正常
- [ ] 用户注册/登录功能正常
- [ ] 漫画阅读功能正常
- [ ] 跨域请求问题已解决
- [ ] 静态文件加载正常
- [ ] 服务自动重启配置完成

---

## 🆘 应急联系和支持

如果在部署过程中遇到问题，请检查：

1. **服务日志**:
   ```bash
   sudo journalctl -u anime-backend -f
   sudo journalctl -u anime-frontend -f
   ```

2. **网络连接**:
   ```bash
   curl http://localhost:8000/api/v1/
   curl http://localhost:3000
   ```

3. **端口占用**:
   ```bash
   sudo netstat -tlnp | grep :3000
   sudo netstat -tlnp | grep :8000
   ```

希望这份文档能帮助您成功部署动漫平台到生产环境！🚀