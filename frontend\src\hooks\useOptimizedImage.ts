import { useState, useEffect } from 'react';
import { PlaceholderImages } from '@/lib/placeholderImages';

interface UseOptimizedImageProps {
  src?: string;
  fallbackSrc?: string;
  title: string;
  width?: number;
  height?: number;
  type?: 'cover' | 'banner' | 'thumbnail' | 'avatar';
}

export function useOptimizedImage({
  src,
  fallbackSrc,
  title,
  width,
  height,
  type = 'cover'
}: UseOptimizedImageProps) {
  const [imageSrc, setImageSrc] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // 生成本地占位符
  const generateLocalPlaceholder = () => {
    switch (type) {
      case 'banner':
        return PlaceholderImages.getBannerPlaceholder(title);
      case 'thumbnail':
        return PlaceholderImages.getThumbnailPlaceholder(title);
      case 'avatar':
        return PlaceholderImages.getAvatarPlaceholder(title);
      case 'cover':
      default:
        return PlaceholderImages.getAnimeCoverPlaceholder(title);
    }
  };

  useEffect(() => {
    if (!src && !fallbackSrc) {
      // 如果没有任何图片源，直接使用本地占位符
      setImageSrc(generateLocalPlaceholder());
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setHasError(false);

    // 优先尝试主图片源
    const primarySrc = src || fallbackSrc;
    if (primarySrc && !primarySrc.includes('picsum.photos')) {
      // 如果不是外部占位符，尝试加载
      const img = new Image();
      
      img.onload = () => {
        setImageSrc(primarySrc);
        setIsLoading(false);
        setHasError(false);
      };
      
      img.onerror = () => {
        // 如果主图片加载失败，尝试fallback
        if (fallbackSrc && fallbackSrc !== primarySrc && !fallbackSrc.includes('picsum.photos')) {
          const fallbackImg = new Image();
          fallbackImg.onload = () => {
            setImageSrc(fallbackSrc);
            setIsLoading(false);
            setHasError(false);
          };
          fallbackImg.onerror = () => {
            // 全部失败，使用本地占位符
            setImageSrc(generateLocalPlaceholder());
            setIsLoading(false);
            setHasError(true);
          };
          fallbackImg.src = fallbackSrc;
        } else {
          // 使用本地占位符
          setImageSrc(generateLocalPlaceholder());
          setIsLoading(false);
          setHasError(true);
        }
      };
      
      img.src = primarySrc;
    } else {
      // 如果是picsum.photos或其他外部占位符，直接使用本地占位符
      setImageSrc(generateLocalPlaceholder());
      setIsLoading(false);
    }
  }, [src, fallbackSrc, title, type]);

  return {
    src: imageSrc,
    isLoading,
    hasError,
    reload: () => {
      // 重新加载逻辑
      if (src || fallbackSrc) {
        setIsLoading(true);
        setHasError(false);
        // 触发重新加载
        const timestamp = Date.now();
        const newSrc = src || fallbackSrc;
        if (newSrc && !newSrc.includes('picsum.photos')) {
          setImageSrc(`${newSrc}?t=${timestamp}`);
        } else {
          setImageSrc(generateLocalPlaceholder());
        }
        setIsLoading(false);
      }
    }
  };
}