#!/usr/bin/env python3
"""
Security validation tests for user management endpoints.

Tests cover:
- SQL injection prevention  
- XSS prevention
- CSRF protection
- Rate limiting
- Authentication bypass attempts
- Authorization checks
- Input sanitization
- Password security
"""

import pytest
import asyncio
import time
import requests
from typing import List, Dict, Any
from fastapi.testclient import TestClient
from app.main import app
from app.models import User
from app.core.security import get_password_hash
import string
import random

class TestSecurityValidation:
    """Security validation test suite"""

    def setup_class(self):
        """Set up test client and base configuration"""
        self.client = TestClient(app)
        self.base_url = "http://testserver"
        
    def create_test_user(self, username: str = "securitytest", password: str = "password123") -> Dict[str, Any]:
        """Create a test user and return credentials"""
        user_data = {
            "username": username,
            "email": f"{username}@security.test",
            "password": password
        }
        
        # Register user
        response = self.client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 200
        
        # Login to get token
        response = self.client.post(
            "/api/v1/auth/login",
            data={"username": username, "password": password}
        )
        assert response.status_code == 200
        token_data = response.json()
        
        return {
            "username": username,
            "password": password,
            "token": token_data["access_token"],
            "headers": {"Authorization": f"Bearer {token_data['access_token']}"}
        }

    def test_sql_injection_password_change(self):
        """Test SQL injection attempts in password change"""
        user = self.create_test_user("sqltest")
        
        # SQL injection payloads
        sql_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "' UNION SELECT * FROM users --",
            "'; UPDATE users SET is_admin=true WHERE id=1; --",
            "' OR 1=1; --",
            "admin'--",
            "admin' /*",
            "admin' #",
            "' or 1=1#",
            "' or 1=1--",
            "' or 1=1/*"
        ]
        
        for payload in sql_payloads:
            password_data = {
                "old_password": payload,
                "new_password": "newpass123",
                "confirm_password": "newpass123"
            }
            
            response = self.client.put(
                "/api/v1/user/password",
                json=password_data,
                headers=user["headers"]
            )
            
            # Should return 400 (wrong password) not 500 (SQL error)
            assert response.status_code == 400
            assert "原密码错误" in response.json()["detail"]

    def test_sql_injection_account_deletion(self):
        """Test SQL injection attempts in account deletion"""
        user = self.create_test_user("sqldeletetest")
        
        sql_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            f"{user['username']}'; DROP TABLE favorites; --",
            f"{user['username']}' UNION SELECT * FROM users --"
        ]
        
        for payload in sql_payloads:
            delete_data = {"username_confirmation": payload}
            
            response = self.client.delete(
                "/api/v1/user/account",
                json=delete_data,
                headers=user["headers"]
            )
            
            # Should return 400 (username mismatch) not execute malicious SQL
            assert response.status_code == 400
            assert "用户名确认不匹配" in response.json()["detail"]

    def test_xss_prevention_username_confirmation(self):
        """Test XSS prevention in username confirmation"""
        user = self.create_test_user("xsstest")
        
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>",
            "';alert('XSS');//",
            "<iframe src='javascript:alert(1)'></iframe>",
            "<body onload=alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>",
            "';alert(String.fromCharCode(88,83,83))//",
            "<script>eval('al'+'ert(1)')</script>"
        ]
        
        for payload in xss_payloads:
            delete_data = {"username_confirmation": payload}
            
            response = self.client.delete(
                "/api/v1/user/account",
                json=delete_data,
                headers=user["headers"]
            )
            
            # Should safely handle XSS attempts
            assert response.status_code == 400
            # Response should not contain unescaped payload
            response_text = response.text.lower()
            assert "<script>" not in response_text
            assert "javascript:" not in response_text

    def test_authentication_required(self):
        """Test that authentication is required for user management endpoints"""
        # Test password change without auth
        password_data = {
            "old_password": "old123",
            "new_password": "new123",
            "confirm_password": "new123"
        }
        
        response = self.client.put("/api/v1/user/password", json=password_data)
        assert response.status_code == 401
        
        # Test account deletion without auth
        delete_data = {"username_confirmation": "testuser"}
        
        response = self.client.delete("/api/v1/user/account", json=delete_data)
        assert response.status_code == 401

    def test_invalid_token_handling(self):
        """Test handling of invalid/malformed JWT tokens"""
        invalid_tokens = [
            "invalid_token",
            "Bearer invalid_token",
            "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid",
            "",
            "Bearer ",
            "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************.TJVA95OrM7E2cBab30RMHrHDcEfxjoYZgeFONFh7HgQ",  # Manipulated token
            "Bearer ../../../../etc/passwd",  # Path traversal attempt
            "Bearer <script>alert('xss')</script>",  # XSS attempt
            "Bearer $(rm -rf /)",  # Command injection attempt
        ]
        
        password_data = {
            "old_password": "old123",
            "new_password": "new123",
            "confirm_password": "new123"
        }
        
        for token in invalid_tokens:
            headers = {"Authorization": token} if token else {}
            
            response = self.client.put(
                "/api/v1/user/password",
                json=password_data,
                headers=headers
            )
            
            assert response.status_code == 401

    def test_password_strength_validation(self):
        """Test comprehensive password strength requirements"""
        user = self.create_test_user("strengthtest")
        
        # Test weak passwords
        weak_passwords = [
            "123",                    # Too short
            "password",               # No numbers
            "12345678",              # No letters
            "pass",                  # Too short, no numbers
            "PASSWORD123",           # Could be stronger but should pass minimum
            "p1",                    # Too short
            "a" * 129,               # Too long (129 chars)
            "",                      # Empty
            "   ",                   # Only whitespace
            "password",              # Dictionary word, no numbers
            "123456789",             # Only numbers
            "abcdefgh",              # Only letters
        ]
        
        for weak_pass in weak_passwords:
            password_data = {
                "old_password": user["password"],
                "new_password": weak_pass,
                "confirm_password": weak_pass
            }
            
            response = self.client.put(
                "/api/v1/user/password",
                json=password_data,
                headers=user["headers"]
            )
            
            # Should either fail validation (422) or be rejected (400)
            assert response.status_code in [400, 422]

    def test_unicode_and_special_character_handling(self):
        """Test handling of unicode and special characters"""
        # Test unicode usernames in account deletion
        unicode_user = self.create_test_user("用户测试")
        
        delete_data = {"username_confirmation": "用户测试"}
        
        response = self.client.delete(
            "/api/v1/user/account",
            json=delete_data,
            headers=unicode_user["headers"]
        )
        
        # Should handle unicode correctly
        assert response.status_code == 200
        
        # Test special characters in passwords
        special_user = self.create_test_user("specialtest")
        
        special_passwords = [
            "Pass@123!",
            "密码Test123",
            "páßwörð123",
            "🔒password123",
            "test!@#$%^&*()123"
        ]
        
        for special_pass in special_passwords:
            password_data = {
                "old_password": special_user["password"],
                "new_password": special_pass,
                "confirm_password": special_pass
            }
            
            response = self.client.put(
                "/api/v1/user/password",
                json=password_data,
                headers=special_user["headers"]
            )
            
            # Should handle special characters correctly
            if len(special_pass) >= 6 and any(c.isdigit() for c in special_pass) and any(c.isalpha() for c in special_pass):
                assert response.status_code == 200
                special_user["password"] = special_pass  # Update for next iteration

    def test_concurrent_access_protection(self):
        """Test protection against concurrent access issues"""
        user = self.create_test_user("concurrenttest")
        
        password_data = {
            "old_password": user["password"],
            "new_password": "concurrent123",
            "confirm_password": "concurrent123"
        }
        
        # Simulate concurrent password changes
        import threading
        import time
        
        results = []
        
        def change_password():
            response = self.client.put(
                "/api/v1/user/password",
                json=password_data,
                headers=user["headers"]
            )
            results.append(response.status_code)
        
        # Start multiple threads
        threads = []
        for _ in range(3):
            thread = threading.Thread(target=change_password)
            threads.append(thread)
        
        # Start all threads simultaneously
        for thread in threads:
            thread.start()
        
        # Wait for all to complete
        for thread in threads:
            thread.join()
        
        # Only one should succeed, others should fail with 400 (wrong old password)
        success_count = sum(1 for status in results if status == 200)
        assert success_count == 1
        assert len([status for status in results if status == 400]) >= 1

    def test_rate_limiting_simulation(self):
        """Test rate limiting behavior (if implemented)"""
        user = self.create_test_user("ratetest")
        
        password_data = {
            "old_password": "wrongpassword",
            "new_password": "new123",
            "confirm_password": "new123"
        }
        
        # Make many rapid requests
        status_codes = []
        for i in range(20):
            response = self.client.put(
                "/api/v1/user/password",
                json=password_data,
                headers=user["headers"]
            )
            status_codes.append(response.status_code)
            
            # Small delay to avoid overwhelming the test
            time.sleep(0.1)
        
        # Most should be 400 (wrong password), but if rate limiting is implemented,
        # some might be 429 (too many requests)
        assert all(status in [400, 429] for status in status_codes)

    def test_input_length_limits(self):
        """Test input length limits and buffer overflow protection"""
        user = self.create_test_user("lengthtest")
        
        # Test extremely long inputs
        very_long_string = "a" * 10000
        
        # Test long password
        password_data = {
            "old_password": user["password"],
            "new_password": very_long_string,
            "confirm_password": very_long_string
        }
        
        response = self.client.put(
            "/api/v1/user/password",
            json=password_data,
            headers=user["headers"]
        )
        
        # Should reject overly long password
        assert response.status_code in [400, 422]
        
        # Test long username confirmation
        delete_data = {"username_confirmation": very_long_string}
        
        response = self.client.delete(
            "/api/v1/user/account",
            json=delete_data,
            headers=user["headers"]
        )
        
        # Should reject gracefully
        assert response.status_code == 400

    def test_json_injection_attempts(self):
        """Test JSON injection and malformed JSON handling"""
        user = self.create_test_user("jsontest")
        
        # Malformed JSON payloads
        malformed_payloads = [
            '{"old_password": "test", "new_password": "test123", "confirm_password": "test123"} {"malicious": "payload"}',
            '{"old_password": "test", "new_password": "test123", "confirm_password": "test123", "__proto__": {"isAdmin": true}}',
            '{"old_password": "test", "new_password": "test123", "confirm_password": "test123", "constructor": {"prototype": {"isAdmin": true}}}',
        ]
        
        for payload in malformed_payloads:
            response = self.client.put(
                "/api/v1/user/password",
                content=payload,
                headers={**user["headers"], "Content-Type": "application/json"}
            )
            
            # Should handle malformed JSON gracefully
            assert response.status_code in [400, 422]

    def test_csrf_protection_simulation(self):
        """Test CSRF protection (if implemented)"""
        user = self.create_test_user("csrftest")
        
        # Simulate request without proper CSRF token/origin
        password_data = {
            "old_password": user["password"],
            "new_password": "csrf123",
            "confirm_password": "csrf123"
        }
        
        # Add suspicious origin header
        headers = {
            **user["headers"],
            "Origin": "http://malicious-site.com",
            "Referer": "http://malicious-site.com/attack.html"
        }
        
        response = self.client.put(
            "/api/v1/user/password",
            json=password_data,
            headers=headers
        )
        
        # Depending on CSRF protection implementation, this might be blocked
        # For now, we'll just ensure it doesn't cause a server error
        assert response.status_code in [200, 400, 403]

    def test_session_fixation_protection(self):
        """Test session fixation protection"""
        user1 = self.create_test_user("session1")
        user2 = self.create_test_user("session2")
        
        # Try to use user1's token for user2's operations
        password_data = {
            "old_password": user2["password"],
            "new_password": "fixation123",
            "confirm_password": "fixation123"
        }
        
        # Use user1's token but target user2's data
        response = self.client.put(
            "/api/v1/user/password",
            json=password_data,
            headers=user1["headers"]  # Wrong user's token
        )
        
        # Should fail because the token belongs to different user
        assert response.status_code == 400  # Wrong old password

    def test_error_message_information_disclosure(self):
        """Test that error messages don't disclose sensitive information"""
        user = self.create_test_user("errortest")
        
        # Test various invalid scenarios and check error messages
        test_cases = [
            {
                "data": {
                    "old_password": "wrongpass",
                    "new_password": "new123",
                    "confirm_password": "new123"
                },
                "expected_status": 400,
                "should_not_contain": ["hash", "database", "sql", "exception", "traceback"]
            },
            {
                "data": {
                    "old_password": user["password"],
                    "new_password": "a",
                    "confirm_password": "a"
                },
                "expected_status": 422,
                "should_not_contain": ["internal", "server", "database", "exception"]
            }
        ]
        
        for case in test_cases:
            response = self.client.put(
                "/api/v1/user/password",
                json=case["data"],
                headers=user["headers"]
            )
            
            assert response.status_code == case["expected_status"]
            
            response_text = response.text.lower()
            for sensitive_word in case["should_not_contain"]:
                assert sensitive_word not in response_text, f"Response contains sensitive word: {sensitive_word}"

if __name__ == "__main__":
    pytest.main([__file__])