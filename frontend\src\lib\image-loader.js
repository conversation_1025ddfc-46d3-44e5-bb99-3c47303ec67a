// 自定义图片加载器，处理包含中文字符的URL
export default function imageLoader({ src, width, quality }) {
  // 检查URL是否包含中文字符
  const hasChinese = /[\u4e00-\u9fff]/.test(src);
  
  // 如果包含中文字符，直接返回原URL，跳过Next.js优化
  if (hasChinese) {
    return src;
  }
  
  // 对于其他URL，使用默认的优化逻辑
  const url = new URL(src);
  url.searchParams.set('w', width.toString());
  url.searchParams.set('q', (quality || 75).toString());
  
  return url.href;
}