'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination } from '@/components/ui/Pagination';
import { Manga, MangaListResponse, MangaSearchParams, MangaStatus, apiClient } from '@/lib/api';
import { Search, Filter, Grid, List, Eye, Heart } from 'lucide-react';
import Link from 'next/link';
import SafeImage from '@/components/ui/safe-image';

export default function DoujinshiListPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [mangas, setMangas] = useState<Manga[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(() => {
    // 从URL参数中获取初始页码
    const pageParam = searchParams.get('page');
    return pageParam ? parseInt(pageParam, 10) : 1;
  });
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<string>('created_at');
  const [sortOrder, setSortOrder] = useState<'desc' | 'asc'>('desc');
  const [status, setStatus] = useState<MangaStatus | ''>('');

  const limit = 24;

  // 监听URL参数变化
  useEffect(() => {
    const pageParam = searchParams.get('page');
    const newPage = pageParam ? parseInt(pageParam, 10) : 1;
    if (newPage !== currentPage) {
      setCurrentPage(newPage);
    }
  }, [searchParams]);

  useEffect(() => {
    fetchMangas();
  }, [currentPage, sortBy, sortOrder, status]);

  const fetchMangas = async () => {
    try {
      setLoading(true);
      
      const params: MangaSearchParams = {
        skip: (currentPage - 1) * limit,
        limit,
        search: searchTerm || undefined,
        sort_by: sortBy,
        sort_order: sortOrder,
        manga_type: 'doujinshi', // 固定为同人志类型
        status: status || undefined,
        // 不限制region_code，显示所有地区的同人志
      };

      const response: MangaListResponse = await apiClient.getMangas(params);
      
      setMangas(response.mangas || []);
      setTotalCount(response.total || 0);
      setTotalPages(Math.ceil((response.total || 0) / limit));
    } catch (error) {
      console.error('Failed to fetch doujinshi:', error);
      setMangas([]);
      setTotalCount(0);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = () => {
    setCurrentPage(1);
    fetchMangas();
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    // 更新URL，但保持其他查询参数
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', newPage.toString());
    router.push(`/doujinshi?${params.toString()}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getStatusText = (status: MangaStatus) => {
    const statusMap = {
      ongoing: '连载中',
      completed: '已完结',
      hiatus: '休载',
      cancelled: '已取消'
    };
    return statusMap[status] || status;
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchMangas();
  };

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* 页面标题 */}
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">同人志</h1>
        <p className="text-muted-foreground">
          发现精彩的同人志作品
        </p>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-card border rounded-lg p-6 space-y-4">
        {/* 搜索栏 */}
        <form onSubmit={handleSearch} className="flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="搜索同人志..."
              className="pl-10"
            />
          </div>
          <Button type="submit">搜索</Button>
        </form>

        {/* 筛选和排序 */}
        <div className="flex flex-wrap gap-4">
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">状态:</label>
            <Select value={status} onValueChange={(value: MangaStatus | '') => { setStatus(value); handleFilterChange(); }}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="全部" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="ongoing">连载中</SelectItem>
                <SelectItem value="completed">已完结</SelectItem>
                <SelectItem value="hiatus">休载</SelectItem>
                <SelectItem value="cancelled">已取消</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">排序:</label>
            <Select value={sortBy} onValueChange={(value) => { setSortBy(value); handleFilterChange(); }}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at">创建时间</SelectItem>
                <SelectItem value="updated_at">更新时间</SelectItem>
                <SelectItem value="title">标题</SelectItem>
                <SelectItem value="view_count">浏览量</SelectItem>
                <SelectItem value="favorite_count">收藏量</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">顺序:</label>
            <Select value={sortOrder} onValueChange={(value: 'desc' | 'asc') => { setSortOrder(value); handleFilterChange(); }}>
              <SelectTrigger className="w-24">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="desc">降序</SelectItem>
                <SelectItem value="asc">升序</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2 ml-auto">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* 同人志列表 */}
      {loading ? (
        <div className="text-center py-12">
          <div className="text-lg">加载中...</div>
        </div>
      ) : mangas.length > 0 ? (
        <>
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {mangas.map((manga) => (
                <Link 
                  key={manga.id} 
                  href={`/manga/${manga.id}?from=doujinshi&page=${currentPage}`}
                  className="group block"
                >
                  <div className="bg-card border rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="aspect-[3/4] relative">
                      <SafeImage
                        src={manga.cover || '/placeholder-manga.jpg'}
                        alt={manga.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                        {getStatusText(manga.status)}
                      </div>
                    </div>
                    <div className="p-3">
                      <h3 className="font-medium text-sm text-center truncate leading-tight" title={manga.title}>
                        {manga.title}
                      </h3>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {mangas.map((manga) => (
                <Link 
                  key={manga.id} 
                  href={`/manga/${manga.id}?from=doujinshi&page=${currentPage}`}
                  className="group"
                >
                  <div className="bg-card border rounded-lg p-4 hover:shadow-lg transition-shadow">
                    <div className="flex gap-4">
                      <div className="w-20 h-28 relative flex-shrink-0">
                        <SafeImage
                          src={manga.cover || '/placeholder-manga.jpg'}
                          alt={manga.title}
                          fill
                          className="object-cover rounded"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-2 gap-2">
                          <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-lg group-hover:text-primary transition-colors truncate">
                              {manga.title}
                            </h3>
                            {manga.title_original && (
                              <p className="text-sm text-muted-foreground truncate">
                                {manga.title_original}
                              </p>
                            )}
                          </div>
                          <div className="flex gap-2 flex-shrink-0 self-start">
                            <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded whitespace-nowrap">
                              {getStatusText(manga.status)}
                            </span>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                          {manga.author && <span>作者: {manga.author}</span>}
                          {manga.artist && <span>画师: {manga.artist}</span>}
                          {manga.release_date && (
                            <span>发布: {formatDate(manga.release_date)}</span>
                          )}
                        </div>
                        
                        {manga.description && (
                          <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                            {manga.description}
                          </p>
                        )}
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4 text-sm">
                            <div className="flex items-center gap-1">
                              <Eye className="h-4 w-4" />
                              {manga.view_count}
                            </div>
                            <div className="flex items-center gap-1">
                              <Heart className="h-4 w-4" />
                              {manga.favorite_count}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}

          {/* 高级分页 */}
          {totalPages > 1 && (
            <div className="mt-8">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12">
          <p className="text-lg text-muted-foreground">
            {searchTerm ? '没有找到匹配的同人志' : '暂无同人志'}
          </p>
        </div>
      )}
    </div>
  );
}
