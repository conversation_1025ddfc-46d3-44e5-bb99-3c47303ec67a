/**
 * URL相关工具函数
 */

/**
 * 构建完整的URL
 */
export function buildUrl(baseUrl: string, path: string, params?: Record<string, any>): string {
  const url = new URL(path, baseUrl);
  
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          value.forEach(item => url.searchParams.append(key, String(item)));
        } else {
          url.searchParams.append(key, String(value));
        }
      }
    });
  }
  
  return url.toString();
}

/**
 * 解析URL参数
 */
export function parseUrlParams(url: string): Record<string, string> {
  const urlObj = new URL(url);
  const params: Record<string, string> = {};
  
  urlObj.searchParams.forEach((value, key) => {
    params[key] = value;
  });
  
  return params;
}

/**
 * 获取当前页面的URL参数
 */
export function getCurrentUrlParams(): Record<string, string> {
  if (typeof window === 'undefined') return {};
  return parseUrlParams(window.location.href);
}

/**
 * 更新URL参数而不刷新页面
 */
export function updateUrlParams(params: Record<string, any>, replace: boolean = false): void {
  if (typeof window === 'undefined') return;
  
  const url = new URL(window.location.href);
  
  Object.entries(params).forEach(([key, value]) => {
    if (value === null || value === undefined || value === '') {
      url.searchParams.delete(key);
    } else {
      url.searchParams.set(key, String(value));
    }
  });
  
  const method = replace ? 'replaceState' : 'pushState';
  window.history[method]({}, '', url.toString());
}

/**
 * 移除URL参数
 */
export function removeUrlParams(keys: string[]): void {
  if (typeof window === 'undefined') return;
  
  const url = new URL(window.location.href);
  keys.forEach(key => url.searchParams.delete(key));
  
  window.history.replaceState({}, '', url.toString());
}

/**
 * 检查URL是否为外部链接
 */
export function isExternalUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    return urlObj.origin !== window.location.origin;
  } catch {
    return false;
  }
}

/**
 * 获取文件扩展名
 */
export function getFileExtension(url: string): string {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const lastDot = pathname.lastIndexOf('.');
    return lastDot > 0 ? pathname.substring(lastDot + 1).toLowerCase() : '';
  } catch {
    return '';
  }
}

/**
 * 检查URL是否为图片
 */
export function isImageUrl(url: string): boolean {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp'];
  const extension = getFileExtension(url);
  return imageExtensions.includes(extension);
}

/**
 * 检查URL是否为视频
 */
export function isVideoUrl(url: string): boolean {
  const videoExtensions = ['mp4', 'webm', 'ogg', 'avi', 'mov', 'wmv', 'flv', 'm4v'];
  const extension = getFileExtension(url);
  return videoExtensions.includes(extension);
}

/**
 * 生成安全的URL slug
 */
export function createSlug(text: string): string {
  return text
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/[\s_-]+/g, '-') // 替换空格和下划线为连字符
    .replace(/^-+|-+$/g, ''); // 移除开头和结尾的连字符
}

/**
 * 验证URL格式
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * 获取域名
 */
export function getDomain(url: string): string {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    return '';
  }
}

/**
 * 构建分享URL
 */
export function buildShareUrl(platform: string, url: string, title?: string, description?: string): string {
  const encodedUrl = encodeURIComponent(url);
  const encodedTitle = title ? encodeURIComponent(title) : '';
  const encodedDescription = description ? encodeURIComponent(description) : '';
  
  switch (platform.toLowerCase()) {
    case 'twitter':
      return `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}`;
    case 'facebook':
      return `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`;
    case 'linkedin':
      return `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`;
    case 'reddit':
      return `https://reddit.com/submit?url=${encodedUrl}&title=${encodedTitle}`;
    case 'telegram':
      return `https://t.me/share/url?url=${encodedUrl}&text=${encodedTitle}`;
    case 'whatsapp':
      return `https://wa.me/?text=${encodedTitle}%20${encodedUrl}`;
    case 'weibo':
      return `https://service.weibo.com/share/share.php?url=${encodedUrl}&title=${encodedTitle}`;
    case 'qq':
      return `https://connect.qq.com/widget/shareqq/index.html?url=${encodedUrl}&title=${encodedTitle}&desc=${encodedDescription}`;
    default:
      return url;
  }
}
